import {
  useEffect,
  useMemo,
  useState,
  type Dispatch,
  type SetStateAction,
  useCallback,
} from "react";

import styled from "styled-components";
import { Spin, Typography } from "antd";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { SvgIconError } from "@/components/ui/Icon/IconError";
import { Connection, System } from "@/utils/socket-helper";
import { useGetBatchDispensing } from "@/features/karte/ui/Karte/PrescriptionInformation/useGetBatchDispensing";
import { usePostApiEpsUpdateEpsDispensingsMutation } from "@/apis/gql/operations/__generated__/update-dispensing";
import { usePostApiEpsGetDispensingInfFromCsvDataMutation } from "@/apis/gql/operations/__generated__/prescription-medicine";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { useUpsertEpsRegister } from "@/hooks/useUpsertEpsRegister";
import { formatDateToCompact } from "@/features/karte/ui/Karte/AutomaticGetDispensingResult/utils";

import { useEPrescriptionContext } from "../PrintSetting/EPrescriptionContextProvider";

import { MODAL_TYPE } from "./TableInformationPrescription";

import type { UpdateType } from "../AutomaticGetDispensingResult/type";
import type {
  DomainModelsEpsEpsPrescriptionInfModel,
  EmrCloudApiRequestsEpsUpdateEpsDispensingItemsInput,
  DomainModelsEpsReqEpsReqModel,
} from "@/apis/gql/generated/types";
import type { IFile, IResponse } from "@/utils/socket-helper/socket.type";

type ModalUpdateDispensingResultProps = {
  rowSelected: DomainModelsEpsEpsPrescriptionInfModel | undefined;
  type: MODAL_TYPE | undefined;
  setModalType: Dispatch<SetStateAction<MODAL_TYPE | undefined>>;
  setBase64Dispensing: (base64Dispensing: string[]) => void;
  setDispensingTimes: (dispensingTimes: number[]) => void;
  setOpenModal: (openModal: string) => void;
};
const StyledModal = styled(Modal)<{
  $errorModal?: boolean;
}>`
  .ant-modal-header {
    background-color: ${({ $errorModal }) =>
      $errorModal ? "#e74c3c" : "#005BAC"};
  }
  .ant-modal-body {
    min-height: 256px;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .ant-modal-footer {
    height: 84px;
    align-items: center;
    display: flex;
    justify-content: center;
  }
`;

const ContentErrorWapper = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 24px 50px;
`;

const ModalContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30px;
  min-height: 256px;
  width: 100%;
`;

const ModalContentConfirm = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 24px;
`;

const { Title, Text } = Typography;

type MessageBody = {
  ProcessingResultStatus: string;
  ProcessingResultMessage: string;
  ProcessingResultCode: string;
  PrescriptionStatus: string;
  DispensingResult: string;
  DispensingResultList: DispensingResultList[];
  ReceptionPharmacyName: string;
  MessageFlg: string;
  ErrorCode?: string;
  ErrorMessage?: string;
};

type DispensingResultList = {
  DispensingResult: string;
  DispensingTimes: string;
  MessageFlg: string;
};

type MessageHeader = {
  SegmentOfResult: string;
  ErrorCode?: string;
  ErrorMessage?: string;
};

type ResponseXmlContent = {
  XmlMsg?: {
    MessageHeader?: MessageHeader;
    MessageBody?: MessageBody;
  };
};

type FormattedResponse = {
  fileName: string | undefined;
  msgBody: MessageBody | undefined;
  msgHeader: MessageHeader | undefined;
  segmentOfResult: number;
  processingResultStatus: number;
  processingResultMessage: string | undefined;
  errorCode: string | undefined;
  errorMsg: string | undefined;
  processingResultCode: string | undefined;
  prescriptionStatus: string | undefined;
  dispensingResult: string | undefined;
  dispensingResultList: DispensingResultList[];
  receptionPharmacyName: string | undefined;
  hasMessageHeader: boolean;
  messageFlg: string | undefined;
};

export const ModalUpdateDispensingResult = ({
  rowSelected,
  type,
  setModalType,
  setBase64Dispensing,
  setDispensingTimes,
  setOpenModal,
}: ModalUpdateDispensingResultProps) => {
  const [contentError, setContentError] = useState<{
    title: string;
    content: string;
  }>({
    title: "",
    content: "",
  });

  const [prescriptionStatus, setPrescriptionStatus] = useState<string>("");
  const [contentDone, setContentDone] = useState<{
    title: string;
    content: string;
  }>({
    title: "",
    content: "",
  });
  const [disableCancel, setDisableCancel] = useState(false);
  const { signatureAuthentication } = useGetBatchDispensing();
  const { data: dataSystemConf } = useGetApiSystemConfGetListQuery({
    onError: (error) => {
      setModalType(undefined);
      handleError({ error });
    },
    fetchPolicy: "no-cache",
  });
  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;
  const system = useMemo(
    () => new System("/medical", systemConf),
    [systemConf],
  );
  const connect = new Connection("/medical");
  const { handleError } = useErrorHandler();
  const [waitForUserConfirmation, setWaitForUserConfirmation] = useState<
    (() => void) | null
  >(null);

  const [registerResultDIM06req, setRegisterResultDIM06req] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const [postApiEpsUpdateEpsDispensings] =
    usePostApiEpsUpdateEpsDispensingsMutation({
      onError: (error) => {
        setModalType(undefined);
        handleError({ error });
      },
    });

  const [postApiEpsGetDispensingInfFromCsvData] =
    usePostApiEpsGetDispensingInfFromCsvDataMutation({
      onError: (error) => {
        setModalType(undefined);
        handleError({ error });
      },
    });

  const formatResponseRequestDispensing = (
    files: IResponse<IFile> | null,
  ): FormattedResponse => {
    const fileName = files?.data?.fileName;
    if (fileName?.includes("err")) {
      const xmlContent = files?.data?.content;
      const codeInBrackets = xmlContent?.match(/\[(.*?)\]/)?.[1];
      const messageAfterBrackets = xmlContent?.split("]")[1]?.trim();

      return {
        fileName,
        msgBody: undefined,
        msgHeader: undefined,
        segmentOfResult: 0,
        processingResultStatus: 0,
        processingResultMessage: messageAfterBrackets,
        errorCode: undefined,
        errorMsg: undefined,
        processingResultCode: codeInBrackets,
        prescriptionStatus: undefined,
        dispensingResult: undefined,
        dispensingResultList: [],
        receptionPharmacyName: undefined,
        hasMessageHeader: false,
        messageFlg: undefined,
      };
    }

    const xmlContent: ResponseXmlContent = JSON.parse(
      files?.data?.content || "",
    );

    const msgBody = xmlContent?.XmlMsg?.MessageBody;
    const msgHeader = xmlContent?.XmlMsg?.MessageHeader;
    const segmentOfResult = Number(
      xmlContent?.XmlMsg?.MessageHeader?.SegmentOfResult,
    );

    const processingResultStatus = Number(
      xmlContent?.XmlMsg?.MessageBody?.ProcessingResultStatus,
    );

    const processingResultMessage =
      xmlContent?.XmlMsg?.MessageBody?.ProcessingResultMessage;

    const errorCode = msgHeader?.ErrorCode ?? msgBody?.ErrorCode;
    const errorMsg = msgHeader?.ErrorMessage ?? msgBody?.ErrorMessage;

    const hasMessageHeader = msgHeader?.ErrorCode ? true : false;

    const processingResultCode =
      xmlContent?.XmlMsg?.MessageBody?.ProcessingResultCode;
    const prescriptionStatus =
      xmlContent?.XmlMsg?.MessageBody?.PrescriptionStatus;
    const dispensingResult = xmlContent?.XmlMsg?.MessageBody?.DispensingResult;
    const dispensingResultList = (() => {
      const result = xmlContent?.XmlMsg?.MessageBody?.DispensingResultList;
      if (!result || Array.isArray(result)) {
        return result || [];
      }
      return [result];
    })();
    const receptionPharmacyName =
      xmlContent?.XmlMsg?.MessageBody?.ReceptionPharmacyName;
    const messageFlg = xmlContent?.XmlMsg?.MessageBody?.MessageFlg;
    return {
      fileName,
      msgBody,
      msgHeader,
      segmentOfResult,
      processingResultStatus,
      processingResultMessage,
      errorCode,
      errorMsg,
      processingResultCode,
      prescriptionStatus,
      dispensingResult,
      dispensingResultList,
      receptionPharmacyName,
      hasMessageHeader,
      messageFlg,
    };
  };

  function parseXML(xmlStr: string) {
    const xmlContent = atob(xmlStr);
    return new window.DOMParser().parseFromString(xmlContent, "text/xml");
  }

  const getDispensingData = useCallback(
    async (dispensingResult: string | undefined) => {
      const xmlContent = parseXML(dispensingResult ?? "");
      const dispensingDocument = xmlContent
        ?.querySelector("DispensingDocument")
        ?.textContent?.trim();

      const dispensingId = xmlContent
        ?.querySelector("DispensingId")
        ?.getAttribute("Value");

      const prescriptionId = xmlContent
        ?.querySelector("PrescriptionId")
        ?.getAttribute("Value");

      const doucumentCSV = await postApiEpsGetDispensingInfFromCsvData({
        variables: {
          dispensingCsvBase64Data: dispensingDocument ?? "",
        },
        onError: (error) => {
          setModalType(undefined);
          handleError({ error });
        },
      });

      const dispensingDate =
        doucumentCSV?.data?.postApiEpsGetDispensingInfFromCsvData?.data?.dispensingGroupDetailModels?.find(
          (item) => item.item === "調剤年月日",
        )?.data ?? "0";

      return {
        dispensingDocument: dispensingDocument ?? "",
        dispensingDate: Number(formatDateToCompact(dispensingDate)),
        dispensingId: dispensingId ?? "",
        prescriptionId: prescriptionId ?? "",
      };
    },
    [postApiEpsGetDispensingInfFromCsvData, handleError],
  );

  const createDispensingItem = useCallback(
    (
      record: DomainModelsEpsEpsPrescriptionInfModel | undefined,
      receptionPharmacyName: string | undefined,
      dispensingData: {
        dispensingDocument: string;
        dispensingDate: number;
        dispensingId: string;
        prescriptionId: string;
      },
      resultType: number,
      dispensingTimes: number,
      messageFlg: string | number,
    ): EmrCloudApiRequestsEpsUpdateEpsDispensingItemsInput => ({
      bango: record?.bango,
      cancelReason: "0",
      dispensingDocument: dispensingData.dispensingDocument,
      ptId: record?.ptId,
      hokensyaNo: record?.hokensyaNo,
      kigo: record?.kigo,
      edaNo: record?.edaNo,
      kohiFutansyaNo: record?.kohiFutansyaNo,
      kohiJyukyusyaNo: record?.kohiJyukyusyaNo,
      resultType,
      receptionPharmacyName: receptionPharmacyName ?? "",
      dispensingDate: Number(dispensingData.dispensingDate),
      dispensingTimes,
      prescriptionId: dispensingData.prescriptionId,
      messageFlg: Number(messageFlg),
      isDeleted: 0,
      dispensingResultId: dispensingData.dispensingId,
      epsUpdateDateTime: "0001-01-01T00:00:00",
    }),
    [],
  );

  const handleDispensingByRefillSupported = useCallback(
    async (
      isRefillSupported: boolean,
      sourceData: DispensingResultList[],
      record: DomainModelsEpsEpsPrescriptionInfModel | undefined,
      resultType: number,
      updateType: UpdateType,
      receptionPharmacyName: string | undefined,
    ) => {
      const payload: EmrCloudApiRequestsEpsUpdateEpsDispensingItemsInput[] = [];

      await Promise.all(
        sourceData?.map(async (itemDispensingResult) => {
          const dispensingData = await getDispensingData(
            itemDispensingResult.DispensingResult,
          );

          if (!dispensingData.prescriptionId) return;

          const itemNew = createDispensingItem(
            record,
            receptionPharmacyName,
            dispensingData,
            resultType,
            isRefillSupported
              ? Number(itemDispensingResult?.DispensingTimes)
              : 1,
            itemDispensingResult?.MessageFlg ?? "",
          );

          payload.push(itemNew);
        }),
      );

      const { errors } = await postApiEpsUpdateEpsDispensings({
        variables: {
          input: {
            [updateType]: true,
            updateEpsDispensingItems: payload,
          },
        },
        onError: (error) => {
          setModalType(undefined);
          handleError({ error });
        },
      });

      return !!errors;
    },
    [getDispensingData, createDispensingItem, postApiEpsUpdateEpsDispensings],
  );

  const { raiinNo, ptId, sinDate } = useGetOrderInfoContext();
  const { handleUpsertEpsRegister } = useUpsertEpsRegister();
  const { updateEpsRegister } = useEPrescriptionContext();

  const requestDispensing = useCallback(
    async (record: DomainModelsEpsEpsPrescriptionInfModel | undefined) => {
      const prescriptionId = record?.prescriptionId;
      // step 2
      if (!prescriptionId) {
        setModalType(MODAL_TYPE.ERROR);
        setContentError({
          title: "調剤結果の取得に失敗しました",
          content: `処方箋IDがないため取得できません。`,
        });
        return;
      }

      //get refillSupported
      const config =
        dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.find(
          (item) => item?.grpCd === 100040 && item?.grpEdaNo === 8,
        );

      const refill = config?.val === 1;
      // step 1.1
      const messageBody = {
        PrescriptionId: prescriptionId,
        RefillSupported: refill ? 1 : 0,
      };

      const timeoutConfig =
        dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.find(
          (item) => item?.grpCd === 100040 && item?.grpEdaNo === 4,
        );

      const timeout = Number(timeoutConfig?.val) * 1000;
      // step 1.2
      setModalType(MODAL_TYPE.LOADING);
      setDisableCancel(true);

      setTimeout(() => {
        setDisableCancel(false);
      }, timeout);

      let responseCreateFile: IResponse<IFile> | null = null;
      const arbitraryFileIdentifierDIM06req = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;
      const registerResultDIM06req = await handleUpsertEpsRegister({
        arbitraryFileIdentifier: arbitraryFileIdentifierDIM06req,
        reqDate: +dayjs().format("YYYYMMDD"),
        raiinNo: String(raiinNo),
        ptId: String(ptId),
        sinDate: Number(sinDate),
        prescriptionId: prescriptionId,
        dispensingResultId: "",
        reqType: 8,
        status: 1,
        resultCode: "",
        resultMessage: "",
        result: "",
      });
      if (registerResultDIM06req) {
        setRegisterResultDIM06req(registerResultDIM06req);
      }
      responseCreateFile = await system.createFile(
        {
          messageHeader: {
            ArbitraryFileIdentifier: arbitraryFileIdentifierDIM06req,
          },
          messageBody,
        },
        "EPSsiDIM06req",
      );

      // step 1.3
      if (responseCreateFile && registerResultDIM06req) {
        updateEpsRegister(registerResultDIM06req, null);
      }
      const responseRequestDispensing =
        formatResponseRequestDispensing(responseCreateFile);

      if (responseRequestDispensing.fileName?.includes("err")) {
        setModalType(MODAL_TYPE.ERROR);
        setContentError({
          title: `調剤結果の取得に失敗しました`,
          content: `処理結果コード: ${responseRequestDispensing?.processingResultCode} \n ${responseRequestDispensing?.processingResultMessage}`,
        });
        return;
      }

      if (
        responseRequestDispensing?.hasMessageHeader &&
        responseRequestDispensing?.segmentOfResult !== 1
      ) {
        setModalType(MODAL_TYPE.ERROR);
        setContentError({
          title: `調剤結果の取得に失敗しました`,
          content: `エラーコード: ${responseRequestDispensing?.errorCode} \n ${responseRequestDispensing?.errorMsg}`,
        });
        return;
      }

      if (
        !responseRequestDispensing?.hasMessageHeader &&
        responseRequestDispensing?.processingResultStatus !== 1
      ) {
        setModalType(MODAL_TYPE.ERROR);
        setContentError({
          title: `調剤結果の取得に失敗しました`,
          content: `処理結果コード: ${responseRequestDispensing?.processingResultCode} \n ${responseRequestDispensing?.processingResultMessage}`,
        });
        return;
      }

      // signature authentication
      const arrayDispensingResult = refill
        ? responseRequestDispensing?.dispensingResultList
        : responseRequestDispensing?.dispensingResult
          ? [
              {
                DispensingResult: responseRequestDispensing?.dispensingResult,
                DispensingTimes: "1",
                MessageFlg: responseRequestDispensing?.messageFlg ?? "",
              },
            ]
          : [];

      await Promise.all(
        arrayDispensingResult.map(async (item) => {
          try {
            await signatureAuthentication(item.DispensingResult);
          } catch {
            setModalType(MODAL_TYPE.ERROR);
            setContentError({
              title: `調剤結果の署名検証に失敗しました`,
              content: "",
            });
            return;
          }
        }),
      );

      const itemUpdate = record?.epsDispensing;
      // debugger;
      let errorDispensing = false;
      switch (responseRequestDispensing?.prescriptionStatus) {
        case "薬局にて受付されていません。": {
          if (itemUpdate) {
            const { errors } = await postApiEpsUpdateEpsDispensings({
              variables: {
                input: {
                  notAcceptedAtPharmacy: true,
                  updateEpsDispensingItems: [itemUpdate],
                },
              },
              onError: (error) => {
                setModalType(undefined);
                handleError({ error });
              },
            });

            if (errors) {
              errorDispensing = true;
            }
          }
          break;
        }
        case "薬局にて調剤済です。": {
          errorDispensing = await handleDispensingByRefillSupported(
            refill,
            arrayDispensingResult,
            record,
            1,
            "preparedAtPharmacy",
            responseRequestDispensing.receptionPharmacyName,
          );
          break;
        }
        case "薬局にて回収済です。":
        case "薬局にて調剤中です。": {
          const resultType =
            responseRequestDispensing?.prescriptionStatus ===
            "薬局にて回収済です。"
              ? 3
              : 4;
          errorDispensing = await handleDispensingByRefillSupported(
            refill,
            arrayDispensingResult,
            record,
            resultType,
            "collectedOrDispensedByPharmacy",
            responseRequestDispensing.receptionPharmacyName,
          );
          break;
        }
        case "当該処方箋は処方箋取消されています。": {
          if (itemUpdate) {
            const { errors } = await postApiEpsUpdateEpsDispensings({
              variables: {
                input: {
                  cancelledPrescription: true,
                  updateEpsDispensingItems: [itemUpdate],
                },
              },
              onError: (error) => {
                setModalType(undefined);
                handleError({ error });
              },
            });

            if (errors) {
              errorDispensing = true;
            }
          }
          break;
        }
        default:
          break;
      }

      if (errorDispensing) {
        return;
      }
      setPrescriptionStatus(
        responseRequestDispensing?.prescriptionStatus ?? "",
      );

      setModalType(MODAL_TYPE.DONE);
      setContentDone({
        title: responseRequestDispensing?.prescriptionStatus ?? "",
        content: "",
      });

      const base64Dispensing = [];
      const dispensingTimes = [];
      if (refill) {
        for (const item of responseRequestDispensing?.dispensingResultList ??
          []) {
          const base64 = await getDispensingData(item.DispensingResult);
          base64Dispensing.push(base64.dispensingDocument);
          dispensingTimes.push(Number(item.DispensingTimes));
        }
      } else {
        const base64 = await getDispensingData(
          responseRequestDispensing?.dispensingResult ?? "",
        );
        base64Dispensing.push(base64.dispensingDocument);
        dispensingTimes.push(1);
      }

      setBase64Dispensing(base64Dispensing);
      setDispensingTimes(dispensingTimes);
    },
    [
      dataSystemConf,
      system,
      setModalType,
      setContentError,
      setDisableCancel,
      signatureAuthentication,
      postApiEpsUpdateEpsDispensings,
      setPrescriptionStatus,
      setContentDone,
      handleDispensingByRefillSupported,
    ],
  );

  useEffect(() => {
    if (rowSelected && type === MODAL_TYPE.LOADING && dataSystemConf) {
      requestDispensing(rowSelected);
    }
  }, [dataSystemConf]);

  const titleModal = useMemo(() => {
    switch (type) {
      case MODAL_TYPE.ERROR:
        return "エラー";

      case MODAL_TYPE.LOADING:
        return "処理中";

      case MODAL_TYPE.DONE:
        return "調剤結果の取得";

      default:
        return "処方箋情報の取消";
    }
  }, [type]);

  const contentModal = useMemo(() => {
    switch (type) {
      case MODAL_TYPE.ERROR:
        return (
          <ContentErrorWapper>
            <SvgIconError />
            <Title level={2}>{contentError.title}</Title>
            <div style={{ width: "100%", whiteSpace: "pre-line" }}>
              {contentError.content}
            </div>
          </ContentErrorWapper>
        );

      case MODAL_TYPE.LOADING:
        return (
          <ModalContentWrapper>
            <span>調剤結果を取得しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );
      case MODAL_TYPE.DONE:
        return (
          <ModalContentConfirm>
            <Title level={2}>{contentDone.title}</Title>
            <Text>{contentDone.content}</Text>
          </ModalContentConfirm>
        );
      default:
        return (
          <ModalContentWrapper>
            <span>処方箋情報を取り消しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );
    }
  }, [type, contentError, contentDone]);

  const textCloseModal = useMemo(() => {
    switch (type) {
      case MODAL_TYPE.LOADING:
        return "キャンセル";
      default:
        return "閉じる";
    }
  }, [type]);

  const handleCancelModal = () => {
    if (type === MODAL_TYPE.LOADING && registerResultDIM06req) {
      updateEpsRegister(registerResultDIM06req, null);
    }
    if (waitForUserConfirmation) {
      waitForUserConfirmation();
      setWaitForUserConfirmation(null);
    }

    if (
      prescriptionStatus === "薬局にて調剤済です。" &&
      type === MODAL_TYPE.DONE
    ) {
      setOpenModal("DISPENSING_INFO");
    }
    connect.disconnect();
    setModalType(undefined);
  };

  if (!dataSystemConf) {
    return null;
  }

  return (
    <StyledModal
      centered
      title={titleModal}
      $errorModal={type === MODAL_TYPE.ERROR}
      width={480}
      isOpen={!!type && !!dataSystemConf}
      onCancel={() => setModalType(undefined)}
      footer={[
        <Button
          key={"cancel"}
          disabled={disableCancel && type === MODAL_TYPE.LOADING}
          varient="tertiary"
          onClick={handleCancelModal}
        >
          {textCloseModal}
        </Button>,
      ]}
    >
      {contentModal}
    </StyledModal>
  );
};
