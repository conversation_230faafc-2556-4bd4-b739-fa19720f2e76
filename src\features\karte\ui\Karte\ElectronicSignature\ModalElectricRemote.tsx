import React, { useCallback, useEffect, useRef, useState } from "react";

import { useForm } from "react-hook-form";
import { jwtDecode } from "jwt-decode";

import { ModalSkipSignature } from "@/features/karte/ui/Karte/ElectronicSignature/ModalSkipSignature";
import {
  ModalContentGetBatch,
  StyledModalLoginRemote,
} from "@/features/karte/ui/Karte/PrescriptionInformation/style";
import {
  RemoteType,
  SIGNATURE_TYPE,
} from "@/features/karte/ui/Karte/ElectronicSignature/type";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { ContentLoginRemoteError } from "@/features/karte/ui/Karte/ElectronicSignature/ContentLoginRemoteError";
import { useElectricSignature } from "@/features/karte/ui/Karte/ElectronicSignature/useElectricSignature";
import { Button } from "@/components/ui/NewButton";
import {
  convertXMLToBase64,
  isTimeExpiredHpki,
} from "@/features/karte/ui/Karte/ElectronicSignature/utils";
import { STORAGE_KEYS } from "@/constants/local-storage";
import { ContentProcessLoadingRemote } from "@/features/karte/ui/Karte/ElectronicSignature/ContentProcessLoadingRemote";
import { useObserverWaitingModalContext } from "@/features/karte/ui/Karte/StationPrescription/ObserverWaitingModalActionProvider";
import { System } from "@/utils/socket-helper";
import { useCancelRegister } from "@/features/karte/ui/Karte/CancelRegister";
import { useEPrescriptionContext } from "@/features/karte/ui/Karte/PrintSetting/EPrescriptionContextProvider";
import { usePrintOutPatientPrescription } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/usePrintOutPatientPrescription";
import { URL_HPKI_LOGIN } from "@/features/karte/ui/Karte/constants";

import type { Dispatch, SetStateAction } from "react";
import type { PrescriptionDrugRemote } from "@/features/karte/ui/Karte/StationPrescription";

export interface FormTypeRemote {
  option: string;
}

type Props = {
  prescription: PrescriptionDrugRemote;
  setProcessRecordPrescription: Dispatch<
    SetStateAction<PrescriptionDrugRemote | null>
  >;
};

export function ModalElectricRemote({
  prescription,
  setProcessRecordPrescription,
}: Props) {
  const {
    state: { signatureRemote },
    handleCloseModal,
    handleOpenModal,
  } = useModal();

  const isWaitRetryLoginlRef = useRef(true);

  const loginId = localStorage.getItem(STORAGE_KEYS.LOGIN_ID);

  console.log(prescription, "prescription accepted By remote");
  const { handleCancelRegister } = useCancelRegister();
  const { setFlagRegisterChange } = useEPrescriptionContext();

  const { setResolveWaitModal, setResolveBreakModal } =
    useObserverWaitingModalContext();

  const [step, setStep] = useState(RemoteType.LOADING);
  const [tokenRetryLoginRemote, setTokenRetryLoginRemote] = useState("");

  console.log("tokenRetryLoginRemote", tokenRetryLoginRemote);

  const [responseSignedXmlBase64Process, setResponseSignedXmlBase64Process] =
    useState("");

  console.log(responseSignedXmlBase64Process, "responseSignedXmlBase64Process");

  const {
    loading,
    signRemotely,
    electronicPrescriptionProxyUse,
    electronicPrescriptionProxyServer,
    electronicPrescriptionProxyPort,
    epsSignServerUriType,
    handleDisconnectSocket,
  } = useElectricSignature({
    onError: () => {
      setResolveBreakModal();
      handleCloseModal("SIGNATURE_REMOTE");
    },
  });

  const { handlePrintOutPatientPrescriptionPaper } =
    usePrintOutPatientPrescription();
  const { control, watch } = useForm<FormTypeRemote>({
    defaultValues: {
      option: "2",
    },
  });

  const waitUntilModalCloses = useCallback(() => {
    return new Promise<void>((resolve) => {
      const checkModal = () => {
        if (!isWaitRetryLoginlRef.current) {
          resolve();
        } else {
          requestAnimationFrame(checkModal);
        }
      };
      requestAnimationFrame(checkModal);
    });
  }, []);

  useEffect(() => {
    if (tokenRetryLoginRemote) {
      console.log("start handleSignRemotely", tokenRetryLoginRemote);
      isWaitRetryLoginlRef.current = false;
      handleSignRemotely().then();
    }
  }, [handleSignRemotely, tokenRetryLoginRemote]);

  async function handleRetryLoginRemote() {
    try {
      setStep(RemoteType.LOADING);

      const isExpired = isTimeExpiredHpki();

      if (isExpired) {
        await handleLoginHPKI();
        await waitUntilModalCloses();
        return;
      }

      await handleSignRemotely();
    } catch (e) {
      setStep(RemoteType.LOGIN_ERROR);
    }
  }

  async function handleSignRemotely() {
    try {
      const payload = {
        remoteToken: (localStorage.getItem(STORAGE_KEYS.HPKI_TOKEN) ?? "")
          .trim()
          .replace(/^"|"$/g, ""),
        drugCsv: prescription?.prescriptionDocument ?? "",
        electronicPrescriptionProxyUse: Number(electronicPrescriptionProxyUse),
        electronicPrescriptionProxyServer: String(
          electronicPrescriptionProxyServer,
        ),
        electronicPrescriptionProxyPort: Number(
          electronicPrescriptionProxyPort,
        ),
        epsSignServerUriType: Number(epsSignServerUriType),
        webToken: "",
      };

      console.log(payload, "payload token remote");
      const responseSignRemotely = await signRemotely(payload);
      if (responseSignRemotely) {
        const responseSignedXmlBase64 = convertXMLToBase64(
          typeof responseSignRemotely?.data?.SignedXml === "string"
            ? responseSignRemotely?.data?.SignedXml
            : "",
        );

        //todo set responseSignedXmlBase64
        setResponseSignedXmlBase64Process(responseSignedXmlBase64);
        setProcessRecordPrescription((prevState) => {
          if (!prevState) return prevState;
          return {
            ...prevState,
            base64DrugSignedXml: responseSignedXmlBase64,
          };
        });
        setResolveWaitModal();
        handleCloseModal("SIGNATURE_REMOTE");
      }
    } catch (error) {
      console.log(error);
      setStep(RemoteType.LOGIN_ERROR);
    }
  }

  const fetchTokenLogin = useCallback(async () => {
    try {
      const systemDrug = new System("/medical");
      const response = await systemDrug.getToken({
        secondaryCertId: loginId ?? "",
      });
      if (response?.status === 200) {
        if (response.data && Object.keys(response.data).length > 0) {
          const token = response.data || "";
          const payload = jwtDecode(token);
          localStorage.setItem(STORAGE_KEYS.HPKI_TOKEN, JSON.stringify(token));
          localStorage.setItem(
            STORAGE_KEYS.TIME_EXP_HPKI_TOKEN,
            String(payload?.exp ?? ""),
          );
          setTokenRetryLoginRemote(token);
        } else {
          localStorage.removeItem(STORAGE_KEYS.HPKI_TOKEN);
        }
      }
    } catch (error) {
      console.error("Lỗi khi lấy token:", error);
    }
  }, []);

  const handleLoginHPKI = useCallback(async () => {
    try {
      const popupWindow = window.open(
        `${URL_HPKI_LOGIN}=${loginId}`,
        "popupWindow",
        "width=800,height=600",
      );

      const timer = setInterval(async () => {
        if (popupWindow && popupWindow.closed) {
          console.log("Event close window login HPKI");
          clearInterval(timer);
          await fetchTokenLogin();
        }
      }, 200);
    } catch (error) {
      console.log(error, "error login HPKI");
    }
  }, [fetchTokenLogin, loginId]);

  async function initLoginAndVerifyPrescription() {
    const isExpired = isTimeExpiredHpki();

    if (isExpired) {
      await handleLoginHPKI();
      await waitUntilModalCloses();
      return;
    }

    if (prescription) {
      handleSignRemotely().then();
    }
  }

  useEffect(() => {
    if (!loading) {
      initLoginAndVerifyPrescription().then();
    }
  }, [loading]);

  async function handleActionButtonSubmit() {
    const option = watch("option");

    if (option === "1") {
      //todo Back to flow 5.5.1
      setResolveBreakModal();
      handleCloseModal("SIGNATURE_REMOTE");
      handleCloseModal("IS_PROCESS_STATION_PRESCRIPTION");
      handleOpenModal(
        "IS_PROCESS_FLOW_LOCAL_PRESCRIPTION",
        {
          navigate: "SIGNATURE_REMOTE",
        },
        undefined,
        "SIGNATURE_REMOTE",
      );
      return;
    }

    if (["2", "3", "4"].includes(option)) {
      //todo retry logic login  remove
      await handleRetryLoginRemote();
      return;
    }
  }

  function renderButtonFooter() {
    if (step === RemoteType.LOADING) {
      return [
        <Button
          onClick={() => {
            handleDisconnectSocket();
            setResolveBreakModal();
            setResolveWaitModal();
            handleCloseModal("SIGNATURE_REMOTE");
            handleCancelRegister().then();
            handlePrintOutPatientPrescriptionPaper(true).then();
          }}
          shape="round"
          varient="tertiary"
          key="cancel"
        >
          キャンセル
        </Button>,
      ];
    }

    return [
      <Button
        onClick={() => {
          handleDisconnectSocket();
          setResolveBreakModal();
          setResolveWaitModal();
          handleCloseModal("SIGNATURE_REMOTE");
          //todo STEP 5-6
          handleCancelRegister().then();
          handlePrintOutPatientPrescriptionPaper(true).then();
        }}
        shape="round"
        varient="tertiary"
        key="cancel"
      >
        キャンセル
      </Button>,
      <Button
        onClick={handleActionButtonSubmit}
        shape="round"
        varient="primary"
        key="submit"
      >
        再試行
      </Button>,
    ];
  }

  function renderContent() {
    const actions: Record<RemoteType, JSX.Element> = {
      LOADING: <ContentProcessLoadingRemote isDisable={false} />,
      LOGIN_ERROR: <ContentLoginRemoteError control={control} />,
    };

    return actions[step] || null;
  }

  function renderTitle() {
    return step === RemoteType.LOGIN_ERROR
      ? "エラー"
      : "電子処方箋への電子署名";
  }

  return (
    <StyledModalLoginRemote
      isLoading={step === RemoteType.LOADING}
      errorModal={step === RemoteType.LOGIN_ERROR}
      isOpen={signatureRemote?.isSignatureRemote ?? false}
      title={renderTitle()}
      centered
      forceRender
      width={480}
      centerFooterContent={step === RemoteType.LOADING}
      footer={renderButtonFooter()}
    >
      <ModalSkipSignature
        handleCancel={() => {
          handleCloseModal("SKIP_SIGNATURE");
          if (signatureRemote?.navigate) {
            handleOpenModal(signatureRemote.navigate);
          }
        }}
        handleSubmit={() => {
          handleCloseModal("SKIP_SIGNATURE");
          handleCloseModal("SIGNATURE_REMOTE");
          setResolveBreakModal();
          setResolveWaitModal();
          setFlagRegisterChange("OFF");
          //todo navigate to STEP 5-6
          handleCancelRegister().then();
          handlePrintOutPatientPrescriptionPaper(true).then();
        }}
        type={SIGNATURE_TYPE.REMOTE}
        name={"SIGNATURE_REMOTE"}
      />
      <ModalContentGetBatch>{renderContent()}</ModalContentGetBatch>
    </StyledModalLoginRemote>
  );
}
