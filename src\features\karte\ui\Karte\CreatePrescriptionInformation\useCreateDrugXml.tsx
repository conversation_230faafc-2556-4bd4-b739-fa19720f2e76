import { useMemo, useRef } from "react";

import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { System } from "@/utils/socket-helper";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import type {
  ISystemRequestConfig,
  PayloadCreateDrugXML,
} from "@/utils/socket-helper/socket.type";

type Props = {
  onError?: () => void;
};

export function useCreateDrugXml({ onError }: Props) {
  const { handleError } = useErrorHandler();
  const controllerAbort = useRef<AbortController | null>(null);
  const controllerAbortVerify = useRef<AbortController | null>(null);
  const controllerAbortCreateDrugXML = useRef<AbortController | null>(null);

  const { data: dataSystemConf, loading: loadingSystem } =
    useGetApiSystemConfGetListQuery({
      onError: (error) => {
        handleError({ error }).then();
        if (onError) {
          onError();
        }
      },
    });

  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;

  const system = new System("/medical", systemConf);

  const timeOutConfig = useMemo(() => {
    return (
      dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.filter(
        (item) => item.grpCd === 100040 && item.grpEdaNo === 4,
      )?.[0]?.val ?? 1
    );
  }, [dataSystemConf]);

  function handleDisconnectSocket() {
    if (controllerAbort.current) {
      controllerAbort.current.abort();
    }

    if (controllerAbortVerify.current) {
      controllerAbortVerify.current.abort();
    }

    if (controllerAbortCreateDrugXML.current) {
      controllerAbortCreateDrugXML.current.abort();
    }
  }

  async function createDrugXML(drugCsv: string) {
    controllerAbortCreateDrugXML.current = new AbortController();
    const signal = controllerAbortCreateDrugXML.current.signal;
    const configSignal: ISystemRequestConfig = { signal };
    const payload: PayloadCreateDrugXML = {
      drugCsv,
    };

    const response = await system.createDrugXML(payload, configSignal);

    return response;
  }

  return {
    timeout: timeOutConfig,
    loadingSystem,
    handleDisconnectSocket,
    createDrugXML,
  };
}
