import { useEffect, useMemo, useState } from "react";

import { useSearchParams } from "next/navigation";
import router from "next/router";
import { useFormContext } from "react-hook-form";

import { useGetApiDiseasesGetListQuery } from "@/apis/gql/operations/__generated__/disease";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import {
  useGetApiPdfCreatorExportDrugInfoLazyQuery,
  useGetApiPdfCreatorExportKarte1PrintSettingLazyQuery,
  useGetApiPdfCreatorInDrugLazyQuery,
  useGetApiReceptionGetLastRaiinInfsForPrintSettingQuery,
  usePostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation,
  usePostApiPdfCreatorExportSijisenMutation,
  usePostApiPdfCreatorOutDrugMutation,
} from "@/apis/gql/operations/__generated__/print-setting";
import { useGetApiReceptionGetQuery } from "@/apis/gql/operations/__generated__/reception";
import {
  DEFAULT_DATE,
  GRP_CD_LICENSE,
  GRP_CD_UN_LICENSE,
  GRP_EDA_LICENSE,
  GRP_EDA_UN_LICENSE,
  GRP_VALUE_LICENSE,
  GRP_VALUE_UN_LICENSE,
  paramsPrintKarte2,
  SHIJISEN_ORDER_KOUIKBN_MODEL,
} from "@/features/karte/ui/Karte/PrintSetting/constant";
import { convertFormValueToUpsertParams } from "@/features/karte/ui/Karte/MedicineOrder/utils/orderForm";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { useGetApiOrdInfGetListLazyQuery } from "@/apis/gql/operations/__generated__/karte-medical";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGetApiPdfCreatorExportKarte2LazyQuery } from "@/apis/gql/operations/__generated__/karte-pdf";
// eslint-disable-next-line import/no-restricted-paths
import { openPdfKarteFile } from "@/features/setting-karte-pdf/utils";

import type { KarteFormData } from "@/features/karte/types/karte-order";
import type { FormStatePrintSetting } from "@/features/karte/ui/Karte/PrintSetting/ModalPrintSetting";

const errorDefault = {
  isErrorMessageIsErrorHokenExpired: false,
  isErrorMessageIsErrorHokenNotCoverDrug: false,
  isErrorMessageIsErrorHokenNotHealthInsurance: false,
  isErrorSplitPrescriptions: false,
  isErrorPrescriptionRetreatmentRequired: false,
  isErrorMessageIsErrorAcceptedByPharmacy: false,
  isErrorMessageIsErrorManyHokenUsingForRp: false,
  isErrorMessageIsErrorMedicalMaterialsNotCovered: false,
  isErrorMessageIsErrrorContainsGenericNamesNotIncluded: false,
};

export function usePrintSetting() {
  const [loading, setLoading] = useState(true);
  const { insuranceDefault, invalidRaiino } = useGetOrderInfoContext();
  const searchParams = useSearchParams();
  const raiinNo = searchParams.get("raiinNo") ?? "0";
  const { id } = router.query;
  const sinDate = parseInt(searchParams.get("sinDate") ?? "0");
  const ptId = id?.toString() ?? "0";
  const navigate = searchParams.get("from") ?? "";

  const { watch } = useFormContext<KarteFormData>();
  const orderRpsWatch = watch("orderRps");
  const jikanKbn = watch("jikanKbn");
  const syosaiKbn = watch("syosaiKbn");
  const schemaImages = watch("schemaImages");

  const { handleError } = useErrorHandler();

  const [postInfoPdfSijisen] = usePostApiPdfCreatorExportSijisenMutation();
  const [getInfoPdfKarte1PrintSetting] =
    useGetApiPdfCreatorExportKarte1PrintSettingLazyQuery();
  const [getPdfKarte2PrintSetting] = useGetApiPdfCreatorExportKarte2LazyQuery();
  const [getInfoPdfInDrug] = useGetApiPdfCreatorInDrugLazyQuery();
  const [getPdfDrugInfo] = useGetApiPdfCreatorExportDrugInfoLazyQuery();
  const [postApiPdfCreatorOutDrug] = usePostApiPdfCreatorOutDrugMutation();

  const [
    getMedical,
    { data: dataGetMedicalStatus, loading: loadingGetMedicalStatus },
  ] = useGetApiOrdInfGetListLazyQuery({
    variables: { raiinNo, ptId, sinDate },
    onError: (error) => handleError({ error }),
    fetchPolicy: "no-cache",
  });

  const { dataHeaderInfo } = useGetOrderInfoContext();

  const valueUpsertParams = {
    orderRps: orderRpsWatch,
    jikanKbn,
    syosaiKbn,
    schemaImages: schemaImages ?? [],
  };

  const params = convertFormValueToUpsertParams({
    value: valueUpsertParams,
    raiinNo,
    ptId,
    sinDate,
    defaultHokenPid: insuranceDefault?.hokenPid,
  });

  const { data: dataSystemConf, loading: loadingSystem } =
    useGetApiSystemConfGetListQuery({
      onError: (error) => handleError({ error }),
    });

  const { data: dataReception, loading: loadingReception } =
    useGetApiReceptionGetLastRaiinInfsForPrintSettingQuery({
      variables: {
        sinDate: sinDate + 1,
        ptId,
        isLastVisit: true,
      },
      onError: (error) => handleError({ error }),
    });

  const [
    checkErrorTodayOdrForEpsRegistrationPrintSetting,
    { loading: loadingCheckErrorToday, data: dataCheckError },
  ] = usePostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation();

  useEffect(() => {
    if (invalidRaiino) return;

    checkErrorTodayOdrForEpsRegistrationPrintSetting({
      variables: {
        input: {
          sinDate,
          ptId,
          raiinNo,
          odrInfs: params?.odrInfs || [],
        },
      },
      onError: (error) => handleError({ error }),
    }).then();

    getMedical().then();
  }, [invalidRaiino]);

  async function handlePrintPDF(
    state: Omit<FormStatePrintSetting, "outpatientOption">,
  ) {
    const promises = [];
    if (state.isInstruction) {
      promises.push(
        postInfoPdfSijisen({
          variables: {
            emrCloudApiRequestsExportPdFSijisenExportRequestInput: {
              ptId: ptId,
              sinDate: sinDate,
              raiinNo: raiinNo,
              printNoOdr: true,
              formType: 0,
              odrKouiKbns:
                SHIJISEN_ORDER_KOUIKBN_MODEL?.flatMap(
                  (item) => item.odrKouiKbnPairs,
                ) || [],
            },
          },
          onError: (error) => {
            handleError({ error });
          },
        }).then(
          (responseSijisen) =>
            responseSijisen?.data?.postApiPdfCreatorExportSijisen?.data
              ?.fileUrl ?? "",
        ),
      );
    }

    if (state.isMedicalRecord1) {
      promises.push(
        getInfoPdfKarte1PrintSetting({
          variables: {
            ptId: ptId,
            sinDate: sinDate,
            hokenPid: dataHeaderInfo?.hokenPid,
            syuByomei: true,
            tenkiByomei: true,
          },
          onError: (error) => {
            handleError({ error });
          },
        }).then(
          (resonseKarte1) =>
            resonseKarte1?.data?.getApiPdfCreatorExportKarte1?.fileUrl ?? "",
        ),
      );
    }

    if (state.isMedicalRecord2) {
      promises.push(
        getPdfKarte2PrintSetting({
          variables: {
            ...paramsPrintKarte2,
            sinDate,
            ptId,
            startDate: sinDate,
            endDate: sinDate,
            raiinNo,
          },
          onError: (error) => {
            handleError({ error });
          },
        }).then(
          (responseKarte2) =>
            responseKarte2?.data?.getApiPdfCreatorExportKarte2?.fileUrl ?? "",
        ),
      );
    }

    if (state.isHospitalPrescription) {
      promises.push(
        getInfoPdfInDrug({
          variables: {
            ptId: ptId,
            sinDate: sinDate,
            raiinNo: raiinNo,
          },
          onError: (error) => {
            handleError({ error });
          },
        }).then(
          (responseInfoIndrug) =>
            responseInfoIndrug?.data?.getApiPdfCreatorInDrug?.fileUrl ?? "",
        ),
      );
    }

    if (state.isDrugInformationSheet) {
      promises.push(
        getPdfDrugInfo({
          variables: {
            sinDate,
            ptId,
            raiinNo,
          },
          onError: (error) => {
            handleError({ error });
          },
        }).then(
          (responsePdfDrugInfo) =>
            responsePdfDrugInfo?.data?.getApiPdfCreatorExportDrugInfo
              ?.fileUrl ?? "",
        ),
      );
    }

    if (state.isOutpatientPrescription) {
      promises.push(
        postApiPdfCreatorOutDrug({
          variables: {
            ptId: ptId,
            sinDate: sinDate,
            raiinNo: raiinNo,
            epsPrintType: 1,
            hokenGp: -1,
          },
          onError: (error) => {
            handleError({ error });
          },
        }).then(
          (responseOutDrug) =>
            responseOutDrug?.data?.postApiPdfCreatorOutDrug?.fileUrl ?? "",
        ),
      );
    }

    const results = await Promise.allSettled(promises);
    results.forEach((rs) => {
      if (rs.status === "fulfilled" && !!rs.value) {
        openPdfKarteFile(rs.value);
      }
    });
  }

  const { data: dataConfirmOnline, loading: loadingConfirmOnline } =
    useGetApiReceptionGetQuery({
      variables: {
        raiinNo,
      },
      onError: (error) => {
        handleError({ error });
      },
    });

  const { data: dataDisease, loading: loadingDisease } =
    useGetApiDiseasesGetListQuery({
      variables: {
        sinDate,
        ptId,
      },
      onError: (error) => {
        handleError({ error });
      },
    });

  const isErrorSplitPrescriptions = useMemo(() => {
    return (
      orderRpsWatch?.some((item) =>
        item?.orderItems?.some(({ itemCd }) => itemCd === "@BUNKATU"),
      ) || false
    );
  }, [orderRpsWatch]);

  const isKouiKbn = useMemo(() => {
    return (
      orderRpsWatch?.every((item) =>
        item?.orderItems?.every(({ sinKouiKbn }) => sinKouiKbn === 10),
      ) || false
    );
  }, [orderRpsWatch]);

  const isErrorPrescriptionRetreatmentRequired = useMemo(() => {
    const isSystemConfValid =
      dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.some(
        (sys) => sys.grpCd === 100040 && sys.grpEdaNo === 8 && sys.val === 1,
      );

    const isOrderError =
      orderRpsWatch?.some((item) =>
        item.orderItems?.some(({ itemCd }) => itemCd === "@REFILL"),
      ) || false;

    return isSystemConfValid ? false : isOrderError;
  }, [orderRpsWatch, dataSystemConf]);

  const isDisease = useMemo(() => {
    const responseDataDisease =
      dataDisease?.getApiDiseasesGetList?.data?.diseaseList || [];
    return responseDataDisease?.length > 0;
  }, [dataDisease]);

  const hasShowMedicalOptionPrintAndCheckErrorRegisterPrescription =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.some(
      (item) =>
        item.grpCd === GRP_CD_UN_LICENSE &&
        item.grpEdaNo === GRP_EDA_UN_LICENSE &&
        item.val === GRP_VALUE_UN_LICENSE,
    ) || false;

  const isLicenseRegisterPrescription =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.some(
      (item) =>
        item.grpCd === GRP_CD_LICENSE &&
        item.grpEdaNo === GRP_EDA_LICENSE &&
        item.val === GRP_VALUE_LICENSE,
    ) || false;

  const isCheckBoxOutPrescription =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.some(
      (item) => item.grpCd === 92003 && item.grpEdaNo === 0 && item.val === 1,
    ) || false;

  const isCheckDrugInformation =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.some(
      (item) => item.grpCd === 92004 && item.grpEdaNo === 0 && item.val === 1,
    ) || false;

  const isCheckBoxHospitalPrescription =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.some(
      (item) => item.grpCd === 92002 && item.grpEdaNo === 0 && item.val === 1,
    ) || false;

  useEffect(() => {
    if (
      !loadingDisease &&
      !loadingSystem &&
      !loadingReception &&
      !loadingConfirmOnline &&
      !loadingCheckErrorToday &&
      !loadingGetMedicalStatus
    ) {
      setLoading(false);
    }
  }, [
    loadingDisease,
    setLoading,
    loadingSystem,
    loadingReception,
    loadingConfirmOnline,
    loadingCheckErrorToday,
    loadingGetMedicalStatus,
  ]);

  const errorMapping = {
    isErrorMessageIsErrorHokenExpired:
      !!dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
        ?.errorMessageIsErrorHokenExpired,
    isErrorMessageIsErrorHokenNotCoverDrug:
      !!dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
        ?.errorMessageIsErrorHokenNotCoverDrug,
    isErrorMessageIsErrorHokenNotHealthInsurance:
      !!dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
        ?.errorMessageIsErrorHokenNotHealthInsurance,
    isErrorSplitPrescriptions: isErrorSplitPrescriptions,
    isErrorPrescriptionRetreatmentRequired:
      isErrorPrescriptionRetreatmentRequired,
    isErrorMessageIsErrorAcceptedByPharmacy:
      !!dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
        ?.errorMessageIsErrorAcceptedByPharmacy,
    isErrorMessageIsErrorManyHokenUsingForRp:
      !!dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
        ?.errorMessageIsErrorManyHokenUsingForRp,
    isErrorMessageIsErrorMedicalMaterialsNotCovered:
      !!dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
        ?.errorMessageIsErrorMedicalMaterialsNotCovered,
    isErrorMessageIsErrrorContainsGenericNamesNotIncluded:
      !!dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
        ?.errorMessageIsErrrorContainsGenericNamesNotIncluded,
  };

  return {
    isKouiKbn,
    navigate,
    sinDate,
    raiinNo,
    ptId,
    orderRpsWatch,
    schemaImages,
    jikanKbn,
    syosaiKbn,
    isDisease,
    dataReception,
    dataConfirmOnline,
    isLicenseRegisterPrescription,
    isCheckBoxOutPrescription,
    isCheckDrugInformation,
    isCheckBoxHospitalPrescription,
    hasShowMedicalOptionPrintAndCheckErrorRegisterPrescription,
    error: isLicenseRegisterPrescription ? errorMapping : errorDefault,
    checkPrintPdfJson: {
      flagPrintInstruction:
        dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
          ?.flagPrintInstruction ?? DEFAULT_DATE,
      flagPrintOutpatientPrescription:
        dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
          ?.flagPrintOutpatientPrescription ?? DEFAULT_DATE,
      flagPrintHospitalPrescription:
        dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
          ?.flagPrintHospitalPrescription ?? DEFAULT_DATE,
      flagPrintDrugInformationSheet:
        dataCheckError?.postApiEpsCheckErrorTodayOdrForEPSRegistration?.data
          ?.flagPrintDrugInformationSheet ?? DEFAULT_DATE,
    },
    loading,
    karteStatus:
      dataGetMedicalStatus?.getApiOrdInfGetList?.data?.karteEdition
        ?.karteStatus ?? 0,
    handlePrintPDF,
    dataGetMedicalStatus,
  };
}
