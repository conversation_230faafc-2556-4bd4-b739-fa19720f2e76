import { pdf } from "@react-pdf/renderer";
import dayjs from "dayjs";
import QRCode from "qrcode";
import { useForm } from "react-hook-form";

import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { formatYYYYMMDD } from "@/utils/datetime-format";
import { useGetSurveysWithoutPermissionQuery } from "@/apis/gql/operations/__generated__/survey";

import { SurveyAnswerNoPatientPdf } from "../ui/pdf/SurveyAnswerNoPatinentPdf";

import type { PatientSearchFormType } from "@/hooks/usePatientSearchForm";
import type { SurveyRes } from "@/apis/gql/generated/types";

type UseSurveyAnswerNoPatientFilterProps = {
  refetchSurveyAnswerNoPatients?: () => void;
  selectedDate?: string;
  setSelectedDate?: (date: string) => void;
  selectedSurvey?: SurveyRes;
  setSelectedSurvey?: (survey: SurveyRes | undefined) => void;
  searchKeyword?: string;
  setSearchKeyword?: (keyword: string) => void;
  setSelectedSurveyAnswerId?: (id: string) => void;
};

export const useSurveyAnswerNoPatientFilter = ({
  refetchSurveyAnswerNoPatients,
  selectedDate,
  setSelectedDate,
  selectedSurvey,
  setSelectedSurvey,
  searchKeyword,
  setSearchKeyword,
  setSelectedSurveyAnswerId,
}: UseSurveyAnswerNoPatientFilterProps) => {
  const { notification } = useGlobalNotification();
  const { handleError } = useErrorHandler();
  const { control } = useForm<PatientSearchFormType>();

  const { data: getSurveys } = useGetSurveysWithoutPermissionQuery({
    variables: {
      input: {
        date: selectedDate,
        keyword: "",
      },
    },
    onError: (error) => handleError({ error }),
  });

  const clearSelection = () => {
    setSelectedSurveyAnswerId?.("");
  };

  const handleFilterBySurvey = (survey: SurveyRes | undefined) => {
    if (survey?.secret === "") {
      notification.error({ message: "システムエラーが発生しました" });
      return;
    }
    setSelectedSurvey?.(survey);
    clearSelection();
  };

  const handleFilterByName = (value: string) => {
    setSearchKeyword?.(value);
    clearSelection();
  };

  const handleFilterByAnswerDate = (value: string) => {
    setSelectedDate?.(value);
    clearSelection();
  };

  const goToCurrentDate = () => {
    const today = formatYYYYMMDD(dayjs().toDate());

    if (selectedDate === today) {
      refetchSurveyAnswerNoPatients?.();
    }

    setSelectedDate?.(today);
    clearSelection();
  };

  const onChangeDate = (diffDate: number) => {
    const newDate = formatYYYYMMDD(
      dayjs(selectedDate).add(diffDate, "d").toDate(),
    );

    setSelectedDate?.(newDate);
    clearSelection();
  };

  const handlePrintSurvey = async () => {
    if (!selectedSurvey?.secret) {
      notification.error({ message: "システムエラーが発生しました" });
      return;
    }

    try {
      const qrUrl = `${window.location.origin}/survey/${selectedSurvey.secret}`;
      const qrImageUrl = await QRCode.toDataURL(qrUrl);

      const MySurveyPDF = (
        <SurveyAnswerNoPatientPdf
          qrImageUrl={qrImageUrl}
          selectedSurvey={selectedSurvey}
        />
      );

      const blob = await pdf(MySurveyPDF).toBlob();
      window.open(URL.createObjectURL(blob), "問診票.pdf");
    } catch (error) {
      handleError({ error: error as Error });
    }
  };

  const handleUrlClipboardCopy = (secret: string) => {
    if (!secret) {
      notification.error({ message: "システムエラーが発生しました" });
      return;
    }
    window.navigator.clipboard.writeText(
      `${window.location.origin}/survey/${secret}`,
    );
    notification.success({ message: "クリップボードにコピーしました" });
  };

  return {
    handlePrintSurvey,
    handleUrlClipboardCopy,
    surveys: getSurveys?.getSurveysWithoutPermission,
    control,
    goToCurrentDate,
    onChangeDate,
    handleFilterByAnswerDate,
    handleFilterBySurvey,
    handleFilterByName,
    selectedSurvey,
    searchKeyword,
    selectedDate,
  };
};
