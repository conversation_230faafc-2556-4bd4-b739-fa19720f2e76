import { useGlobalNotification } from "@/hooks/useGlobalNotification";

export const useClipboardCopy = () => {
  const { notification } = useGlobalNotification();

  const copyToClipboard = async (
    answerFields: { key: string; value: string }[],
  ) => {
    try {
      await window.navigator.clipboard.writeText(
        answerFields
          .map((field) => `【${field.key}】${field.value || "-"}`)
          .join("\n"),
      );
      notification.success({
        message: "コピーしました",
      });
    } catch {
      notification.error({
        message: "コピーに失敗しました",
      });
    }
  };

  return { copyToClipboard };
};
