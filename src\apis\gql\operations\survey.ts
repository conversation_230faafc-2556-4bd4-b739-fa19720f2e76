import { gql } from "@/apis/gql/apollo-client";

export const GET_SURVEYS = gql`
  query getSurveys($input: GetSurveysReq) {
    getSurveys(input: $input) {
      surveyId
      name
      fQuesJson
      createdDate
      lastModifiedDate
      secret
      isDeleted
      createdAt
    }
  }
`;

export const GET_SURVEYS_WITHOUT_PERMISSION = gql`
  query getSurveysWithoutPermission($input: GetSurveysReq) {
    getSurveysWithoutPermission(input: $input) {
      surveyId
      name
      fQuesJson
      createdDate
      lastModifiedDate
      secret
      isDeleted
      createdAt
    }
  }
`;

export const GET_SURVEY_BY_ID = gql`
  query getSurveyById($id: Int!) {
    getSurveyById(id: $id) {
      surveyId
      name
      fQuesJson
    }
  }
`;

export const CREATE_SURVEY = gql`
  mutation createSurvey($input: SurveyCreateReq!) {
    createSurvey(input: $input) {
      surveyId
      fQues<PERSON>son
      name
    }
  }
`;

export const UPDATE_SURVEY = gql`
  mutation updateSurvey($input: SurveyInformationReq!) {
    updateSurvey(input: $input) {
      surveyId
      fQuesJson
      name
    }
  }
`;

export const DELETE_SURVEY = gql`
  mutation deleteSurveyById($id: Int!) {
    deleteSurveyById(id: $id) {
      surveyId
    }
  }
`;

export const DOWNLOAD_SURVEY_PDF_ZIP_BY_DATE_RANGE = gql`
  query downloadSurveyPDFZipByDateRange($input: SurveyDateRangeReq!) {
    downloadSurveyPDFZipByDateRange(input: $input) {
      pdfURL
    }
  }
`;

export const GET_SURVEY_TEMPLATES = gql`
  query getSurveyTemplates {
    getSurveyTemplates {
      surveyTemplateId
      fQuesJson
      name
    }
  }
`;

export const GET_SURVEY_ANSWERS = gql`
  query getSurveyAnswers($patientId: Int!) {
    getSurveyAnswers(patientId: $patientId) {
      surveyAnswerId
      treatmentTitle
      treatmentType
      basicAnswerJson
      customAnswerJson
      createdAt
      examStartDate
      reserveDetailId
    }
  }
`;

export const POST_CONSULTATION_RESULT_UPDATE = gql`
  mutation postApiConsultationResultUpdate(
    $emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput: EmrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput
  ) {
    postApiConsultationResultUpdate(
      emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput: $emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput
    ) {
      message
      status
      data {
        status
      }
    }
  }
`;
