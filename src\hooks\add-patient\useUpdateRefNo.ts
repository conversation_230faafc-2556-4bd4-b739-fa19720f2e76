import { useRef } from "react";

import dayjs from "dayjs";

import { System } from "@/utils/socket-helper";
import { usePostApiOnlineConvertXmlToQcXmlMsgMutation } from "@/apis/gql/operations/__generated__/online-convert";
import { SystemHub, SystemScreenCode } from "@/constants/confirm-online";
import { usePostApiOnlineUpdateRefNoMutation } from "@/apis/gql/operations/__generated__/online";

import { useCheckConctionAgent } from "../useCheckConctionAgent";
import { useErrorHandler } from "../useErrorHandler";

import type { DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation } from "@/apis/gql/generated/types";

const oQTimestamp = dayjs().format("YYYYMMDDHHmmss");
const fileName = `OQSsiimm01req_${oQTimestamp}.xml`;

type Props = {
  screenCode?: string;
  systemHub?: SystemHub;
};

export const useUpdateRefNo = ({
  screenCode = SystemScreenCode.PatientInfo,
  systemHub = SystemHub.PatientInf,
}: Props) => {
  const { handleError } = useErrorHandler();
  const { checkConnection } = useCheckConctionAgent();
  const [convertXmlToQCXmlMsg] = usePostApiOnlineConvertXmlToQcXmlMsgMutation({
    onError: (error) => handleError({ error }),
  });

  const [updateRefNo] = usePostApiOnlineUpdateRefNoMutation({
    onError: (error) => handleError({ error }),
  });

  const controllerAbort = useRef<AbortController | null>(null);
  controllerAbort.current = new AbortController();
  const signal = controllerAbort.current.signal;

  const onlinePatientInf = new System(systemHub, undefined, {
    screenCode,
  });

  const handleUpdateRefNo = async ({
    ptNum,
    resultOfQualificationConfirmation,
    ptId,
    isBiggerThan75YearsOld,
    isKohi,
  }: {
    ptNum?: string;
    resultOfQualificationConfirmation?: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation;
    ptId: string;
    isBiggerThan75YearsOld?: boolean;
    isKohi?: boolean;
  }) => {
    const isConnected = await checkConnection();
    if (!isConnected) {
      return;
    }

    const dataRefno = await onlinePatientInf.requestUpdateRefNumber(
      String(ptNum),
      resultOfQualificationConfirmation,
      fileName,
      { signal },
      isBiggerThan75YearsOld,
      isKohi,
    );

    if (dataRefno && dataRefno.status === 200) {
      const dataRes = dataRefno?.data.content;
      if (!dataRes) return;
      await onlinePatientInf.moveFile({ files: [dataRefno.data.fileName] });
      convertXmlToQCXmlMsg({
        variables: {
          payload: {
            xmlString: dataRes,
          },
        },
        onCompleted: (data) => {
          const dataResponse =
            data.postApiOnlineConvertXmlToQCXmlMsg?.data?.qcXmlMsgResponse;
          if (
            Number(dataResponse?.messageHeader?.segmentOfResult) === 1 &&
            Number(dataResponse?.messageBody?.processingResultStatus) === 1
          ) {
            updateRefNo({
              variables: {
                payload: {
                  ptId,
                },
              },
            });
          }
        },
      });
    }
  };
  return { handleUpdateRefNo };
};
