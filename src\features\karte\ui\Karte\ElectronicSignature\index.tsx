import React, {
  type Dispatch,
  type SetStateAction,
  useEffect,
  useState,
} from "react";

import { useForm } from "react-hook-form";
import dayjs from "dayjs";

import {
  ModalContentGetBatch,
  StyledModal,
} from "@/features/karte/ui/Karte/PrescriptionInformation/style";
import { Button } from "@/components/ui/NewButton";
import {
  ElectricType,
  SIGNATURE_TYPE,
} from "@/features/karte/ui/Karte/ElectronicSignature/type";
import { ElectricNotInstall } from "@/features/karte/ui/Karte/ElectronicSignature/ElectricNotInstall";
import { ElectricProcessRead } from "@/features/karte/ui/Karte/ElectronicSignature/ElectricProcessRead";
import { ModalSkipSignature } from "@/features/karte/ui/Karte/ElectronicSignature/ModalSkipSignature";
import { ElectricEnterPin } from "@/features/karte/ui/Karte/ElectronicSignature/ElectricEnterPin";
import { useElectricSignature } from "@/features/karte/ui/Karte/ElectronicSignature/useElectricSignature";
import { ElectricLoading } from "@/features/karte/ui/Karte/ElectronicSignature/ElectricLoading";
import { sleep } from "@/features/karte/ui/Karte/PrescriptionInformation/utils";
import { ElectricLoginProcess } from "@/features/karte/ui/Karte/ElectronicSignature/ElectricLoginProcess";
import { ElectricLoginError } from "@/features/karte/ui/Karte/ElectronicSignature/ElectricLoginError";
import {
  convertXMLToBase64,
  isTimeExpired,
} from "@/features/karte/ui/Karte/ElectronicSignature/utils";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { useCancelRegister } from "@/features/karte/ui/Karte/CancelRegister";
import { useObserverWaitingModalContext } from "@/features/karte/ui/Karte/StationPrescription/ObserverWaitingModalActionProvider";
import { useEPrescriptionContext } from "@/features/karte/ui/Karte/PrintSetting/EPrescriptionContextProvider";
import { usePrintOutPatientPrescription } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/usePrintOutPatientPrescription";
import { ContentLoginRemoteError } from "@/features/karte/ui/Karte/ElectronicSignature/ContentLoginRemoteError";
import { STORAGE_KEYS } from "@/constants/local-storage";

import type { PrescriptionDrugLocalFlow } from "@/features/karte/ui/Karte/FlowLocalPresciption";
import type { FormTypeRemote } from "@/features/karte/ui/Karte/ElectronicSignature/ModalElectricRemote";

export type FormTypePin = {
  pin: string;
};

type Props = {
  prescription: PrescriptionDrugLocalFlow;
  setProcessRecordPrescription: Dispatch<
    SetStateAction<PrescriptionDrugLocalFlow | null>
  >;
};

export function ElectronicSignatureModal({
  prescription,
  setProcessRecordPrescription,
}: Props) {
  const {
    state: { loginElectronicSignature, navigate },
    handleCloseModal,
    handleOpenModal,
  } = useModal();

  const { getValues, control, watch, reset } = useForm<FormTypePin>({
    defaultValues: {
      pin: "",
    },
  });

  const pinEnter = watch("pin");

  const {
    timeSettingEnableBtn,
    timeCachePinSetting,
    checkHpkiDriverSetting,
    loginElectricSignature,
    handleDisconnectSocket,
    loading,
  } = useElectricSignature({
    onError: () => {
      handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE");
      setResolveBreakModalFlowLocalPrescription();
    },
  });

  const { setFlagRegisterChange } = useEPrescriptionContext();

  const [step, setStep] = useState<ElectricType>(ElectricType.LOADING);
  const [loadingLogin, setLoadingLogin] = useState(false);
  const [isEnableSkip, setIsEnableSkip] = useState(true);
  const { handleCancelRegister } = useCancelRegister();
  const { setResolveWaitModal, setResolveBreakModalFlowLocalPrescription } =
    useObserverWaitingModalContext();

  const { handlePrintOutPatientPrescriptionPaper } =
    usePrintOutPatientPrescription();

  const { control: controlRemote, watch: watchRemote } =
    useForm<FormTypeRemote>({
      defaultValues: {
        option: "1",
      },
    });

  const [responseSignedXmlBase64Process, setResponseSignedXmlBase64Process] =
    useState("");

  console.log(responseSignedXmlBase64Process, "responseSignedXmlBase64Process");

  async function handleRetryLoginLocal() {
    try {
      setStep(ElectricType.LOADING);
      await processLoadDriverAndCheckingLogin();
    } catch (e) {
      console.log(e);
      console.log("ERROR RETRY NAVIGATE SCREEN", navigate);
      if (navigate === "SIGNATURE_REMOTE") {
        setStep(ElectricType.LOGIN_ERROR_NAVIGATE_REMOTE);
        return;
      }
      setStep(ElectricType.LOGIN_ERROR);
    }
  }

  useEffect(() => {
    if (step !== ElectricType.LOGIN) return;
    const timeoutId = setTimeout(
      () => setIsEnableSkip(false),
      timeSettingEnableBtn * 1000,
    );
    return () => clearTimeout(timeoutId);
  }, [step, timeSettingEnableBtn]);

  async function processLoopEachRecordPrescription(pin: string) {
    try {
      //todo nhận dữ liệu từ flow 5.5.1
      setStep(ElectricType.LOGIN);
      const responseSignedLocal = await loginElectricSignature(
        pin,
        prescription?.drugXml ?? "",
      );

      if (responseSignedLocal) {
        const responseSignedXmlBase64 = convertXMLToBase64(
          typeof responseSignedLocal?.data?.SignedXml === "string"
            ? responseSignedLocal?.data?.SignedXml
            : "",
        );

        setResponseSignedXmlBase64Process(responseSignedXmlBase64);
        setProcessRecordPrescription((prevState) => {
          if (!prevState) return prevState;
          return {
            ...prevState,
            base64DrugSignedXml: responseSignedXmlBase64,
          };
        });
        localStorage.setItem("pin", pin);
        const timeExpire = dayjs().add(timeCachePinSetting, "second").unix();
        localStorage.setItem("timeExpire", timeExpire.toString());
        setResolveWaitModal();
        handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
      }
    } catch (error) {
      console.error(error);
      console.log(
        "ERROR  NAVIGATE SCREEN case processLoopEachRecordPrescription",
        navigate,
      );
      localStorage.removeItem(STORAGE_KEYS.PIN);
      localStorage.removeItem(STORAGE_KEYS.TIME_PIN_EXPIRE);
      reset({ pin: "" });
      if (navigate === "SIGNATURE_REMOTE") {
        setStep(ElectricType.LOGIN_ERROR_NAVIGATE_REMOTE);
        return;
      }
      setStep(ElectricType.LOGIN_ERROR);
    } finally {
      setLoadingLogin(false);
    }
  }

  async function processLoadDriverAndCheckingLogin() {
    setStep(ElectricType.PROGRESS_READ);
    try {
      await checkHpkiDriverSetting();
    } catch (error) {
      setStep(ElectricType.NOT_INSTALL);
      return;
    }

    await sleep(1000);

    if (isTimeExpired()) {
      setStep(ElectricType.ENTER_PIN);
      return;
    }

    const pin = localStorage.getItem("pin");
    if (!pin) return;

    setLoadingLogin(true);

    await processLoopEachRecordPrescription(pin);
  }

  useEffect(() => {
    if (!loading) {
      processLoadDriverAndCheckingLogin().then();
    }
  }, [loading]);

  function renderTitle() {
    if (
      step === ElectricType.NOT_INSTALL ||
      step === ElectricType.LOGIN_ERROR ||
      step === ElectricType.LOGIN_ERROR_NAVIGATE_REMOTE
    ) {
      return "エラー";
    }

    return "電子処方箋への電子署名";
  }

  async function handleLogin() {
    const valueForm = getValues();

    if (valueForm.pin.length < 4) return;

    try {
      setStep(ElectricType.LOGIN);
      await sleep(2000);
      await processLoopEachRecordPrescription(valueForm?.pin);
    } catch (e) {
      console.log("ERROR  NAVIGATE SCREEN case handleLogin", navigate);
      localStorage.removeItem(STORAGE_KEYS.PIN);
      localStorage.removeItem(STORAGE_KEYS.TIME_PIN_EXPIRE);
      if (navigate === "SIGNATURE_REMOTE") {
        setStep(ElectricType.LOGIN_ERROR_NAVIGATE_REMOTE);
        return;
      }
      setStep(ElectricType.LOGIN_ERROR);
    }
  }

  async function handleActionButtonSubmit() {
    const option = watchRemote("option");
    console.log("handle vao flow hpki local remote navigate");

    if (option === "1") {
      //todo Back to flow 5.5.1
      console.log("handle vao flow hpki local remote navigate option 1");
      handleRetryLoginLocal().then();
      return;
    }

    if (["2", "3", "4"].includes(option)) {
      //todo retry logic login  remove
      console.log("handle vao flow hpki local remote navigate option 2,3,4");
      setResolveBreakModalFlowLocalPrescription();
      setResolveWaitModal();
      handleCloseModal("IS_PROCESS_FLOW_LOCAL_PRESCRIPTION");
      handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
      console.log("IS_PROCESS_STATION_PRESCRIPTION");
      setTimeout(() => {
        handleOpenModal("IS_PROCESS_STATION_PRESCRIPTION");
      }, 1000);
      return;
    }
  }

  function renderButtonFooter() {
    if (step === ElectricType.PROGRESS_READ) {
      return [
        <Button
          onClick={() => {
            setResolveWaitModal();
            setResolveBreakModalFlowLocalPrescription();
            handleDisconnectSocket();
            handleCancelRegister().then();
            handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
          }}
          shape="round"
          varient="tertiary"
          key="cancel"
        >
          キャンセル
        </Button>,
      ];
    }

    if (step === ElectricType.NOT_INSTALL) {
      return [
        <Button
          onClick={() => {
            setResolveBreakModalFlowLocalPrescription();
            setResolveWaitModal();
            handleDisconnectSocket();
            handleCancelRegister().then();
            handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
          }}
          shape="round"
          varient="tertiary"
          key="cancel"
        >
          キャンセル
        </Button>,
        <Button
          onClick={() => {
            //todo 5.6 and Tiếp tục xử ký in đơn thuốc ngoại viện
            setResolveBreakModalFlowLocalPrescription();
            setResolveWaitModal();
            handleDisconnectSocket();
            handleCancelRegister().then();
            handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
            handlePrintOutPatientPrescriptionPaper(true).then();
          }}
          shape="round"
          varient="primary"
          key="submit"
        >
          発行
        </Button>,
      ];
    }

    if (step === ElectricType.ENTER_PIN) {
      return [
        <Button
          onClick={() => {
            setResolveBreakModalFlowLocalPrescription();
            setResolveWaitModal();
            handleDisconnectSocket();
            handleCancelRegister().then();
            handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
          }}
          shape="round"
          varient="tertiary"
          key="cancel"
        >
          キャンセル
        </Button>,
        <Button
          disabled={pinEnter.length < 4}
          loading={loadingLogin}
          onClick={handleLogin}
          shape="round"
          varient="primary"
          key="submit"
        >
          署名
        </Button>,
      ];
    }

    if (step === ElectricType.LOGIN) {
      return [
        <Button
          disabled={isEnableSkip}
          shape="round"
          varient="tertiary"
          key="cancel"
        >
          キャンセル
        </Button>,
      ];
    }

    if (step === ElectricType.LOGIN_ERROR) {
      return [
        <Button
          onClick={async () => {
            //todo 5.6
            setResolveBreakModalFlowLocalPrescription();
            setResolveWaitModal();
            handleCancelRegister().then();
            handleDisconnectSocket();
            handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
          }}
          shape="round"
          varient="tertiary"
          key="cancel"
        >
          キャンセル
        </Button>,
        <Button
          onClick={handleRetryLoginLocal}
          shape="round"
          varient="primary"
          key="submit"
        >
          再試行
        </Button>,
      ];
    }

    if (step === ElectricType.LOGIN_ERROR_NAVIGATE_REMOTE) {
      return [
        <Button
          onClick={() => {
            handleDisconnectSocket();
            setResolveBreakModalFlowLocalPrescription();
            setResolveWaitModal();
            handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
            //todo STEP 5-6
            handleCancelRegister().then();
            handlePrintOutPatientPrescriptionPaper(true).then();
          }}
          shape="round"
          varient="tertiary"
          key="cancel"
        >
          キャンセル
        </Button>,
        <Button
          onClick={handleActionButtonSubmit}
          shape="round"
          varient="primary"
          key="submit"
        >
          再試行
        </Button>,
      ];
    }

    return [
      <Button
        onClick={() => {
          setResolveBreakModalFlowLocalPrescription();
          setResolveWaitModal();
          handleDisconnectSocket();
          handleCancelRegister().then();
          handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
        }}
        shape="round"
        varient="tertiary"
        key="cancel"
      >
        キャンセル
      </Button>,
      <Button disabled={true} shape="round" varient="primary" key="submit">
        発行
      </Button>,
    ];
  }

  function renderContent() {
    const actions: Record<ElectricType, JSX.Element> = {
      LOADING: <ElectricLoading />,
      NOT_INSTALL: <ElectricNotInstall />,
      PROGRESS_READ: <ElectricProcessRead />,
      ENTER_PIN: <ElectricEnterPin control={control} />,
      LOGIN: <ElectricLoginProcess isDisable={isEnableSkip} />,
      LOGIN_ERROR: <ElectricLoginError />,
      LOGIN_ERROR_NAVIGATE_REMOTE: (
        <ContentLoginRemoteError navigate={navigate} control={controlRemote} />
      ),
    };

    return actions[step] || null;
  }

  return (
    <StyledModal
      isHeight={
        step === ElectricType.NOT_INSTALL ||
        step === ElectricType.LOGIN_ERROR_NAVIGATE_REMOTE
      }
      errorModal={
        step === ElectricType.NOT_INSTALL ||
        step === ElectricType.LOGIN_ERROR ||
        step === ElectricType.LOGIN_ERROR_NAVIGATE_REMOTE
      }
      isOpen={loginElectronicSignature}
      title={renderTitle()}
      centered
      forceRender
      width={480}
      centerFooterContent={
        step === ElectricType.LOGIN || step === ElectricType.PROGRESS_READ
      }
      footer={renderButtonFooter()}
    >
      <ModalSkipSignature
        handleCancel={async () => {
          handleCloseModal("SKIP_SIGNATURE");
          // await handleRetryLoginLocal();
        }}
        handleSubmit={() => {
          handleCloseModal("SKIP_SIGNATURE");
          handleCloseModal("LOGIN_ELECTRONIC_SIGNATURE_LOCAL");
          handleDisconnectSocket();
          setFlagRegisterChange("OFF");
          //todo navigate to step 5-6
          setResolveBreakModalFlowLocalPrescription();
          setResolveWaitModal();
          handleCancelRegister().then();
          handlePrintOutPatientPrescriptionPaper().then();
        }}
        type={SIGNATURE_TYPE.LOCAL}
        name={"LOGIN_ELECTRONIC_SIGNATURE_LOCAL"}
      />
      <ModalContentGetBatch>{renderContent()}</ModalContentGetBatch>
    </StyledModal>
  );
}
