// ![DO NOT EDIT] this file is auto-generated by svgr;
import * as React from "react";
import type { SVGProps } from "react";
export const SvgIconsIconHelp = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={20}
    height={20}
    fill="none"
    {...props}
  >
    <path
      fill="#FBFCFE"
      d="M9.95 16q.525 0 .888-.363.362-.362.362-.887c0-.525-.12-.646-.362-.887a1.2 1.2 0 0 0-.888-.363 1.2 1.2 0 0 0-.887.363 1.2 1.2 0 0 0-.363.887q0 .525.363.887.362.363.887.363m-.9-3.85h1.85q0-.825.188-1.3.187-.475 1.062-1.3a7.5 7.5 0 0 0 1.025-1.238q.375-.587.375-1.412 0-1.4-1.025-2.15T10.1 4q-1.424 0-2.313.75a3.97 3.97 0 0 0-1.237 1.8l1.65.65q.124-.45.563-.975Q9.2 5.7 10.1 5.7q.8 0 1.2.438.4.437.4.962 0 .5-.3.938t-.75.812q-1.1.975-1.35 1.475t-.25 1.825M10 20a9.7 9.7 0 0 1-3.9-.788 10.1 10.1 0 0 1-3.175-2.137Q1.575 15.725.788 13.9A9.7 9.7 0 0 1 0 10q0-2.074.787-3.9a10.1 10.1 0 0 1 2.138-3.175Q4.275 1.575 6.1.788A9.7 9.7 0 0 1 10 0q2.075 0 3.9.787a10.1 10.1 0 0 1 3.175 2.138q1.35 1.35 2.137 3.175A9.7 9.7 0 0 1 20 10a9.7 9.7 0 0 1-.788 3.9 10.1 10.1 0 0 1-2.137 3.175q-1.35 1.35-3.175 2.137A9.7 9.7 0 0 1 10 20m0-2q3.35 0 5.675-2.325T18 10t-2.325-5.675T10 2 4.325 4.325 2 10t2.325 5.675T10 18"
    />
  </svg>
);
