import { Controller } from "react-hook-form";
import styled from "styled-components";

import { Checkbox } from "@/components/ui/Checkbox";
import { InputLabel } from "@/components/ui/InputLabel";

import { useSearch } from "../../providers/SearchProvider";

const StyledCheckbox = styled(Checkbox)``;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 10px;
`;

export const FutureTreatmentFilter = () => {
  const { control, onSubmitSearchQuery } = useSearch();

  return (
    <Controller
      name="showFutureTreatment"
      control={control}
      render={({ field: { value, onChange } }) => (
        <>
          <StyledLabel label="診療日" />
          <StyledCheckbox
            onChange={(e) => {
              onChange(e.target.checked);
              setTimeout(() => {
                onSubmitSearchQuery();
              }, 0);
            }}
            checked={value}
          >
            未来日表示
          </StyledCheckbox>
        </>
      )}
    />
  );
};
