import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetSurveyBySecretQueryVariables = Types.Exact<{
  secret: Types.Scalars["String"]["input"];
}>;

export type GetSurveyBySecretQuery = {
  __typename?: "query_root";
  getSurveyBySecret?: {
    __typename?: "SurveyRes";
    name: string;
    fQuesJson: string;
    clinicName?: string;
  };
};

export type GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables = Types.Exact<{
  input: Types.SurveyAnswerNoPatientUploadFileUrLsReq;
}>;

export type GetSurveyAnswerNoPatientUploadFileUrLsQuery = {
  __typename?: "query_root";
  getSurveyAnswerNoPatientUploadFileURLs: Array<{
    __typename?: "SurveyAnswerNoPatientUploadFileURL";
    fileName: string;
    uploadUrl: string;
    s3Key: string;
  }>;
};

export type CreateSurveyAnswerNoPatientMutationVariables = Types.Exact<{
  input: Types.SurveyAnswerNoPatientCreateReq;
}>;

export type CreateSurveyAnswerNoPatientMutation = {
  __typename?: "mutation_root";
  createSurveyAnswerNoPatient: string;
};

export const GetSurveyBySecretDocument = gql`
  query getSurveyBySecret($secret: String!) {
    getSurveyBySecret(secret: $secret) {
      name
      fQuesJson
      clinicName
    }
  }
`;

/**
 * __useGetSurveyBySecretQuery__
 *
 * To run a query within a React component, call `useGetSurveyBySecretQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveyBySecretQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveyBySecretQuery({
 *   variables: {
 *      secret: // value for 'secret'
 *   },
 * });
 */
export function useGetSurveyBySecretQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetSurveyBySecretQuery,
    GetSurveyBySecretQueryVariables
  > &
    (
      | { variables: GetSurveyBySecretQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSurveyBySecretQuery,
    GetSurveyBySecretQueryVariables
  >(GetSurveyBySecretDocument, options);
}
export function useGetSurveyBySecretLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveyBySecretQuery,
    GetSurveyBySecretQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSurveyBySecretQuery,
    GetSurveyBySecretQueryVariables
  >(GetSurveyBySecretDocument, options);
}
export function useGetSurveyBySecretSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveyBySecretQuery,
    GetSurveyBySecretQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSurveyBySecretQuery,
    GetSurveyBySecretQueryVariables
  >(GetSurveyBySecretDocument, options);
}
export type GetSurveyBySecretQueryHookResult = ReturnType<
  typeof useGetSurveyBySecretQuery
>;
export type GetSurveyBySecretLazyQueryHookResult = ReturnType<
  typeof useGetSurveyBySecretLazyQuery
>;
export type GetSurveyBySecretSuspenseQueryHookResult = ReturnType<
  typeof useGetSurveyBySecretSuspenseQuery
>;
export type GetSurveyBySecretQueryResult = Apollo.QueryResult<
  GetSurveyBySecretQuery,
  GetSurveyBySecretQueryVariables
>;
export const GetSurveyAnswerNoPatientUploadFileUrLsDocument = gql`
  query getSurveyAnswerNoPatientUploadFileURLs(
    $input: SurveyAnswerNoPatientUploadFileURLsReq!
  ) {
    getSurveyAnswerNoPatientUploadFileURLs(input: $input) {
      fileName
      uploadUrl
      s3Key
    }
  }
`;

/**
 * __useGetSurveyAnswerNoPatientUploadFileUrLsQuery__
 *
 * To run a query within a React component, call `useGetSurveyAnswerNoPatientUploadFileUrLsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveyAnswerNoPatientUploadFileUrLsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveyAnswerNoPatientUploadFileUrLsQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetSurveyAnswerNoPatientUploadFileUrLsQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetSurveyAnswerNoPatientUploadFileUrLsQuery,
    GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables
  > &
    (
      | {
          variables: GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSurveyAnswerNoPatientUploadFileUrLsQuery,
    GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables
  >(GetSurveyAnswerNoPatientUploadFileUrLsDocument, options);
}
export function useGetSurveyAnswerNoPatientUploadFileUrLsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveyAnswerNoPatientUploadFileUrLsQuery,
    GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSurveyAnswerNoPatientUploadFileUrLsQuery,
    GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables
  >(GetSurveyAnswerNoPatientUploadFileUrLsDocument, options);
}
export function useGetSurveyAnswerNoPatientUploadFileUrLsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveyAnswerNoPatientUploadFileUrLsQuery,
    GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSurveyAnswerNoPatientUploadFileUrLsQuery,
    GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables
  >(GetSurveyAnswerNoPatientUploadFileUrLsDocument, options);
}
export type GetSurveyAnswerNoPatientUploadFileUrLsQueryHookResult = ReturnType<
  typeof useGetSurveyAnswerNoPatientUploadFileUrLsQuery
>;
export type GetSurveyAnswerNoPatientUploadFileUrLsLazyQueryHookResult =
  ReturnType<typeof useGetSurveyAnswerNoPatientUploadFileUrLsLazyQuery>;
export type GetSurveyAnswerNoPatientUploadFileUrLsSuspenseQueryHookResult =
  ReturnType<typeof useGetSurveyAnswerNoPatientUploadFileUrLsSuspenseQuery>;
export type GetSurveyAnswerNoPatientUploadFileUrLsQueryResult =
  Apollo.QueryResult<
    GetSurveyAnswerNoPatientUploadFileUrLsQuery,
    GetSurveyAnswerNoPatientUploadFileUrLsQueryVariables
  >;
export const CreateSurveyAnswerNoPatientDocument = gql`
  mutation createSurveyAnswerNoPatient(
    $input: SurveyAnswerNoPatientCreateReq!
  ) {
    createSurveyAnswerNoPatient(input: $input)
  }
`;
export type CreateSurveyAnswerNoPatientMutationFn = Apollo.MutationFunction<
  CreateSurveyAnswerNoPatientMutation,
  CreateSurveyAnswerNoPatientMutationVariables
>;

/**
 * __useCreateSurveyAnswerNoPatientMutation__
 *
 * To run a mutation, you first call `useCreateSurveyAnswerNoPatientMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateSurveyAnswerNoPatientMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createSurveyAnswerNoPatientMutation, { data, loading, error }] = useCreateSurveyAnswerNoPatientMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateSurveyAnswerNoPatientMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateSurveyAnswerNoPatientMutation,
    CreateSurveyAnswerNoPatientMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateSurveyAnswerNoPatientMutation,
    CreateSurveyAnswerNoPatientMutationVariables
  >(CreateSurveyAnswerNoPatientDocument, options);
}
export type CreateSurveyAnswerNoPatientMutationHookResult = ReturnType<
  typeof useCreateSurveyAnswerNoPatientMutation
>;
export type CreateSurveyAnswerNoPatientMutationResult =
  Apollo.MutationResult<CreateSurveyAnswerNoPatientMutation>;
export type CreateSurveyAnswerNoPatientMutationOptions =
  Apollo.BaseMutationOptions<
    CreateSurveyAnswerNoPatientMutation,
    CreateSurveyAnswerNoPatientMutationVariables
  >;
