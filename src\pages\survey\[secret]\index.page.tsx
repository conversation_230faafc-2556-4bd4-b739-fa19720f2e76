import { createGlobalStyle } from "styled-components";

import { WebSurvey } from "@/features/survey/ui/WebSurvey";

const OverrideStyle = createGlobalStyle`
  html {
    overscroll-behavior: none;
  }

  body {
    overscroll-behavior-y: none;
    background-color: #e9f0f7;

    @media (max-width: 600px) {
      background-color: #fff;
    }
  }
`;

const SurveyPage = () => {
  return (
    <>
      <OverrideStyle />
      <WebSurvey />
    </>
  );
};

export default SurveyPage;
