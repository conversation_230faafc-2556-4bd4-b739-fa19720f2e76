import { useState } from "react";

import {
  useGetMailDeliverySettingsQuery,
  useSetMailDeliverySettingsMutation,
} from "@/apis/gql/operations/__generated__/mail";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";

import { useErrorHandler } from "./useErrorHandler";

import type { MailDeliverySettingsReq } from "@/apis/gql/generated/types";

export const useMailDeliverySetting = (
  isModalOpen: boolean,
  onClose: () => void,
) => {
  const { handleError } = useErrorHandler();
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const { notification } = useGlobalNotification();

  const handleConfirmModalOpen = () => {
    setIsConfirmModalOpen(true);
  };

  const handleConfirmModalClose = () => {
    setIsConfirmModalOpen(false);
  };

  const { data: mailDeliveryData, loading } = useGetMailDeliverySettingsQuery({
    onError: (error) => {
      handleError({
        error,
        commonMessage: "メール配信設定の取得に失敗しました",
      });
    },
    skip: !isModalOpen,
  });

  const [setMailDeliverySetting, { loading: submitting }] =
    useSetMailDeliverySettingsMutation();

  const submit = (input: MailDeliverySettingsReq) => {
    const flagKeys: (keyof typeof input)[] = [
      "allowLogin",
      "allowReservation",
      "allowNewMessage",
      "allowTask",
    ];

    const isFlagChanged = flagKeys.some(
      (key) => input[key] !== mailDeliveryData?.getMailDeliverySettings?.[key],
    );

    setMailDeliverySetting({
      variables: {
        input,
      },
      onCompleted: () => {
        if (input.email !== mailDeliveryData?.getMailDeliverySettings?.email) {
          handleConfirmModalOpen();
        } else if (isFlagChanged) {
          notification.success({
            message: "メール配信設定を変更しました。",
          });
        }
        onClose();
      },
      onError: (error) => {
        handleError({
          error,
          commonMessage: "メール配信設定の更新に失敗しました",
        });
      },
    });
  };

  return {
    loading,
    mailDeliverySetting: mailDeliveryData?.getMailDeliverySettings,
    submitting,
    submit,
    isConfirmModalOpen,
    handleConfirmModalClose,
  };
};
