import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetSurveyAnswerNoPatientsQueryVariables = Types.Exact<{
  answerDate: Types.Scalars["String"]["input"];
  surveyId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetSurveyAnswerNoPatientsQuery = {
  __typename?: "query_root";
  getSurveyAnswerNoPatients: Array<{
    __typename?: "SurveyAnswerNoPatient";
    birthday?: number;
    createdAt?: string;
    name?: string;
    kanaName?: string;
    surveyAnswerNoPatientId?: string;
    surveyId?: number;
    surveyName?: string;
  }>;
};

export type GetSurveyAnswerNoPatientByIdQueryVariables = Types.Exact<{
  id: Types.Scalars["Int64"]["input"];
}>;

export type GetSurveyAnswerNoPatientByIdQuery = {
  __typename?: "query_root";
  getSurveyAnswerNoPatientById?: {
    __typename?: "SurveyAnswerNoPatientDetail";
    birthday?: number;
    createdAt?: string;
    kanaName?: string;
    name?: string;
    surveyName?: string;
    surveyAnswer?: string;
    surveyId?: number;
    surveyAnswerNoPatientId?: string;
    files?: Array<{
      __typename?: "SurveyAnswerNoPatientFileWithDownloadURL";
      fileName: string;
      downloadUrl: string;
      s3Key: string;
    }>;
  };
};

export const GetSurveyAnswerNoPatientsDocument = gql`
  query getSurveyAnswerNoPatients($answerDate: String!, $surveyId: Int) {
    getSurveyAnswerNoPatients(answerDate: $answerDate, surveyId: $surveyId) {
      birthday
      createdAt
      name
      kanaName
      surveyAnswerNoPatientId
      surveyId
      surveyName
    }
  }
`;

/**
 * __useGetSurveyAnswerNoPatientsQuery__
 *
 * To run a query within a React component, call `useGetSurveyAnswerNoPatientsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveyAnswerNoPatientsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveyAnswerNoPatientsQuery({
 *   variables: {
 *      answerDate: // value for 'answerDate'
 *      surveyId: // value for 'surveyId'
 *   },
 * });
 */
export function useGetSurveyAnswerNoPatientsQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetSurveyAnswerNoPatientsQuery,
    GetSurveyAnswerNoPatientsQueryVariables
  > &
    (
      | { variables: GetSurveyAnswerNoPatientsQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSurveyAnswerNoPatientsQuery,
    GetSurveyAnswerNoPatientsQueryVariables
  >(GetSurveyAnswerNoPatientsDocument, options);
}
export function useGetSurveyAnswerNoPatientsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveyAnswerNoPatientsQuery,
    GetSurveyAnswerNoPatientsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSurveyAnswerNoPatientsQuery,
    GetSurveyAnswerNoPatientsQueryVariables
  >(GetSurveyAnswerNoPatientsDocument, options);
}
export function useGetSurveyAnswerNoPatientsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveyAnswerNoPatientsQuery,
    GetSurveyAnswerNoPatientsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSurveyAnswerNoPatientsQuery,
    GetSurveyAnswerNoPatientsQueryVariables
  >(GetSurveyAnswerNoPatientsDocument, options);
}
export type GetSurveyAnswerNoPatientsQueryHookResult = ReturnType<
  typeof useGetSurveyAnswerNoPatientsQuery
>;
export type GetSurveyAnswerNoPatientsLazyQueryHookResult = ReturnType<
  typeof useGetSurveyAnswerNoPatientsLazyQuery
>;
export type GetSurveyAnswerNoPatientsSuspenseQueryHookResult = ReturnType<
  typeof useGetSurveyAnswerNoPatientsSuspenseQuery
>;
export type GetSurveyAnswerNoPatientsQueryResult = Apollo.QueryResult<
  GetSurveyAnswerNoPatientsQuery,
  GetSurveyAnswerNoPatientsQueryVariables
>;
export const GetSurveyAnswerNoPatientByIdDocument = gql`
  query getSurveyAnswerNoPatientById($id: Int64!) {
    getSurveyAnswerNoPatientById(id: $id) {
      birthday
      createdAt
      kanaName
      name
      surveyName
      surveyAnswer
      surveyId
      surveyAnswerNoPatientId
      files {
        fileName
        downloadUrl
        s3Key
      }
    }
  }
`;

/**
 * __useGetSurveyAnswerNoPatientByIdQuery__
 *
 * To run a query within a React component, call `useGetSurveyAnswerNoPatientByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveyAnswerNoPatientByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveyAnswerNoPatientByIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetSurveyAnswerNoPatientByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetSurveyAnswerNoPatientByIdQuery,
    GetSurveyAnswerNoPatientByIdQueryVariables
  > &
    (
      | {
          variables: GetSurveyAnswerNoPatientByIdQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSurveyAnswerNoPatientByIdQuery,
    GetSurveyAnswerNoPatientByIdQueryVariables
  >(GetSurveyAnswerNoPatientByIdDocument, options);
}
export function useGetSurveyAnswerNoPatientByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveyAnswerNoPatientByIdQuery,
    GetSurveyAnswerNoPatientByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSurveyAnswerNoPatientByIdQuery,
    GetSurveyAnswerNoPatientByIdQueryVariables
  >(GetSurveyAnswerNoPatientByIdDocument, options);
}
export function useGetSurveyAnswerNoPatientByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveyAnswerNoPatientByIdQuery,
    GetSurveyAnswerNoPatientByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSurveyAnswerNoPatientByIdQuery,
    GetSurveyAnswerNoPatientByIdQueryVariables
  >(GetSurveyAnswerNoPatientByIdDocument, options);
}
export type GetSurveyAnswerNoPatientByIdQueryHookResult = ReturnType<
  typeof useGetSurveyAnswerNoPatientByIdQuery
>;
export type GetSurveyAnswerNoPatientByIdLazyQueryHookResult = ReturnType<
  typeof useGetSurveyAnswerNoPatientByIdLazyQuery
>;
export type GetSurveyAnswerNoPatientByIdSuspenseQueryHookResult = ReturnType<
  typeof useGetSurveyAnswerNoPatientByIdSuspenseQuery
>;
export type GetSurveyAnswerNoPatientByIdQueryResult = Apollo.QueryResult<
  GetSurveyAnswerNoPatientByIdQuery,
  GetSurveyAnswerNoPatientByIdQueryVariables
>;
