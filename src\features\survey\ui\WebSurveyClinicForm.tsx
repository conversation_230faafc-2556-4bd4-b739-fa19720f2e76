import "survey-react/defaultV2.min.css";

import styled from "styled-components";
import {
  ComputedUpdater,
  Model,
  Survey,
  surveyLocalization,
} from "survey-react";

import {
  type CustomAnswerType,
  transformSurveyQuestion,
} from "../utils/customer-survey";

import type { ITheme } from "survey-react";

/**
 * Web問診票：問診内容の入力フォーム
 *
 * 問診入力のソースコードは、クリニックマップの次のコードをベースに作成しています。
 * @see {@link https://github.com/bizleap-healthcare/clinic-reservation/blob/main/user-client/src/components/molecules/customer-survey/CustomerSurveyClinicForm.tsx}
 */

export type WebSurveyClinicFormType = {
  /** survey-reactの回答データ */
  answerData: unknown;
  /** survey-reactの添付ファイル */
  tempFiles: Record<string, File>;
  /** サーバ送信用の回答データ（answerDataとtempFilesから生成） */
  surveyAnswer: CustomAnswerType[];
};

type Props = {
  theme: ITheme;
  surveyModelJson: string;
  data: WebSurveyClinicFormType;
  onSubmit: (data: WebSurveyClinicFormType) => void;
  onBack: (data: WebSurveyClinicFormType) => void;
};

export const WebSurveyClinicForm = ({
  theme,
  surveyModelJson,
  data: { answerData, tempFiles, surveyAnswer },
  onSubmit: onSaveSurvey,
  onBack,
}: Props) => {
  const jaLocale = surveyLocalization.locales.ja;
  jaLocale.completeText = "問診票を提出する";
  jaLocale.pagePrevText = "戻る";
  jaLocale.booleanCheckedLabel = "はい";
  jaLocale.booleanUncheckedLabel = "いいえ";
  jaLocale.completingSurvey =
    "問診票の事前送信にご協力いただきありがとうございます。";
  jaLocale.completeText = "次へ";
  surveyLocalization.defaultLocale = "ja";
  const survey = new Model(surveyModelJson);

  survey.applyTheme(theme);
  survey.showPreviewBeforeComplete = "noPreview";
  if (answerData) {
    survey.data = answerData;
  }
  survey.clearInvisibleValues = "none"; //非表示の質問の回答価値を維持する

  survey.addNavigationItem({
    id: "back-to-customer-survey",
    title: "戻る",
    visibleIndex: 5, //before Start Button
    visible: new ComputedUpdater(() => {
      return survey.isFirstPage;
    }),
    action: () =>
      onBack({
        answerData: survey.data,
        tempFiles: tempFileStorage,
        surveyAnswer,
      }),
    innerCss: "nav-back-to-customer-survey",
  });

  survey.getAllQuestions(false).forEach((question) => {
    if (question.getType() === "file") {
      question.storeDataAsText = false;
    }
    if (question.getType() === "expression") {
      question.visible = false;
    }
  });

  const tempFileStorage: Record<string, File> = { ...tempFiles };
  survey.onUploadFiles.add((_, options) => {
    // Add files to the temporary storage with key being questionName
    tempFileStorage[options.name] = options.files[0] || new File([], "");
    // Load file previews
    const content: {
      name: string;
      type: string;
      content: string | ArrayBuffer | null;
      file: File;
    }[] = [];
    options.files.forEach((file) => {
      const fileReader = new FileReader();
      fileReader.onload = () => {
        content.push({
          name: file.name,
          type: file.type,
          content: fileReader.result,
          file: file,
        });
        if (content.length === options.files.length) {
          options.callback(
            "success",
            content.map((fileContent) => {
              return {
                file: fileContent.file,
                content: fileContent.content,
              };
            }),
          );
        }
      };
      fileReader.readAsDataURL(file);
    });
  });

  // Handle file removal
  survey.onClearFiles.add((_, options) => {
    if (options.fileName === null) {
      if (tempFileStorage[options.name]) {
        delete tempFileStorage[options.name];
      }
      options.callback("success");
      return;
    }
    options.callback("success");
  });

  survey.onComplete.add((sender) => {
    const expressionQuestions = sender
      .getAllQuestions()
      .filter((question) => question.getType() === "expression");

    const visibleQuestions = sender.getAllQuestions(true);

    const surveyResultData = transformSurveyQuestion(
      [...visibleQuestions, ...expressionQuestions],
      tempFileStorage,
    );
    onSaveSurvey({
      answerData: sender.data,
      tempFiles: tempFileStorage,
      surveyAnswer: surveyResultData,
    });
  });

  return (
    <Wrapper>
      <ScrollArea>
        <SurveyWrapper>
          <StyledSurvey model={survey} />
        </SurveyWrapper>
      </ScrollArea>
    </Wrapper>
  );
};

const COLOR = {
  TEXT_PRIMARY: "#243544",
  TEXT_WHITE: "#fff",
  DIVIDER_BUTTON: "#CFDDEA",
  TINT_SECONDARY: "#4EBBE0",
};

const Wrapper = styled.div`
  padding-bottom: 48px;
  width: 100%;
  height: calc(100vh - 48px - 40px - 48px);
  overflow-y: auto;
  display: flex;
  justify-content: center;
  background-color: #e9f0f7;
`;

const ScrollArea = styled.div``;

const SurveyWrapper = styled.div`
  width: 832px;
  padding: 24px;

  @media (max-width: 600px) {
    width: 375px;
    background-color: #f6f6f6;
  }
`;

const StyledSurvey = styled(Survey)`
  .sd-progress__text {
    padding: 0;
  }

  .sv_progress-toc {
    min-height: calc(100vh - 248px);
  }

  .sv_progress-toc--mobile {
    display: none;
  }

  .sv-components-column:has(> .sd-action-bar) {
    position: fixed;
    bottom: 40px;
    left: 0px;
    right: 0px;
    background-color: rgb(255, 255, 255);
    height: 68px;
    text-align: center;
    display: block;
    padding: 0 8px;
    z-index: 100; // Z_INDEX_ORDER.FOOTER
  }

  .sd-action-bar {
    transform: translateY(25%);
    text-align: center;
    margin: 0px auto;
    padding: 0px !important;
    justify-content: center;
    flex-wrap: nowrap;
    gap: 12px;
  }

  #back-to-customer-survey,
  #sv-nav-prev {
    width: 100%;
    max-width: 120px;
    &:hover {
      opacity: 0.7;
    }
  }

  .sd-btn {
    display: block;
    cursor: pointer;
    border-radius: 8px;
    border: none;
    font-size: 14px;
    height: 44px;
    padding: 0 !important;
    line-height: normal !important;
    &:hover {
      opacity: 0.7;
    }
  }

  .sd-navigation__prev-btn {
    width: 120px;
    color: ${COLOR.TEXT_PRIMARY};
    background-color: ${COLOR.DIVIDER_BUTTON};
    font-weight: 500;
    box-shadow: none;
  }

  .sd-navigation__next-btn,
  .sd-navigation__complete-btn {
    color: ${COLOR.TEXT_WHITE};
    background-color: ${COLOR.TINT_SECONDARY};
    height: 44px;
    box-shadow: none;
    width: 180px;
    flex-grow: 0 !important;
    justify-content: center;
  }

  .nav-back-to-customer-survey {
    color: ${COLOR.TEXT_PRIMARY};
    background-color: ${COLOR.DIVIDER_BUTTON};
    cursor: pointer;
    border-radius: 8px;
    border: none;
    height: 44px;
    width: 120px;
    font-weight: 400;
    font-size: 14px;
    padding: 0px;
    -webkit-font-smoothing: auto;
  }

  .sv-action {
    max-width: 180px;
    box-shadow: 0px;
  }

  .sd-btn.sd-btn--action.sd-navigation__complete-btn {
    width: 180px;
    font-size: 14px;
    font-weight: 400;
    font-stretch: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-align: center;
    -webkit-font-smoothing: auto;
  }
`;
