import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";

import { usePostApiOnlineConvertXmlToQcXmlMsgMutation } from "@/apis/gql/operations/__generated__/online-convert";
import {
  CONFIRM_CERTIFICATION_SUCCESS_MSG,
  CONFIRMATION_FILE_NAME,
  CONFIRMATION_TYPE,
  SystemHub,
  SystemScreenCode,
} from "@/constants/confirm-online";
import { System } from "@/utils/socket-helper";
import { createXmlFileUpdateConfirmHoken } from "@/utils/socket-helper/onlineQualification";
import { OnlineCertificationLoadingModal } from "@/components/common/OnlineCertification/OnlineCertificationLoadingModal";
import { OnlineCertificationErrorModal } from "@/components/common/OnlineCertification/OnlineCertificationErrorModal";
import { RenderIf } from "@/utils/common/render-if";
import { OnlineCertificationSuccessModal } from "@/components/common/OnlineCertification/OnlineCertificationSuccessModal";
import { SelectPatientInfoModal } from "@/components/common/OnlineCertification/SelectPatientInfoModal";
import { usePatientContext } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { stringToInfoConsFlg } from "@/utils/add-patient";
import { AuditEventCode } from "@/constants/audit-log";

import { useErrorHandler } from "../useErrorHandler";
import { useAuditLog } from "../useAuditLog";
import { useCheckConctionAgent } from "../useCheckConctionAgent";

import type {
  DomainModelsOnlineQualificationConfirmationQcXmlMsgResponse,
  DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
  EmrCloudApiResponsesOnlineConvertXmlToQcXmlMsgResponse,
} from "@/apis/gql/generated/types";

type ValidateState = {
  type: "success" | "error";
  content: string;
};

type ConfirmOnlineInfo = {
  birthdate: string;
  limitConsFlg: string;
  selectedHokenInf: {
    hokensyaNo: string;
    kigo: string;
    bango: string;
    edaNo: string;
    hokenId: number;
  };
};

type DataSourceItem =
  DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation;

type Props = {
  callback?: (
    res?: EmrCloudApiResponsesOnlineConvertXmlToQcXmlMsgResponse,
    isNotEndFlow?: boolean,
  ) => void;
  handleSetSelectedResult?: (data: DataSourceItem, checkDate: string) => void;
  ptId?: string;
  hasSelectPatient?: boolean;
  type?: SystemHub;
  onDone?: (result: DataSourceItem[], checkDate: string) => void;
};

export const useReceptionConfirmOnlineCertification = ({
  ptId,
  hasSelectPatient,
  type = SystemHub.Reception,
  callback,
  handleSetSelectedResult,
  onDone,
}: Props) => {
  const [isLoading, setLoading] = useState<boolean>(false);
  const [openValidateModal, setOpenValidateModal] = useState<ValidateState>();
  const [dataSource, setDataSource] = useState<DataSourceItem[]>([]);
  const [openSelectPatientModal, setOpenSelectPatientModal] =
    useState<boolean>(false);
  const [xmlString, setXmlString] = useState<string>("");
  const [checkDate, setCheckDate] = useState<string>("");

  const {
    handleUpdateOnlineInsuranceData,
    resetOnlineData,
    confirmingType,
    setSelectedInsurance,
    handleSetOnlineConfirmationHistoryData,
  } = usePatientContext();

  const waitingOQController = useRef<AbortController>();

  const onlineClass = useMemo(() => {
    switch (type) {
      case SystemHub.PatientInf:
        return new OnlinePatientInf();
      case SystemHub.Visiting:
        return new OnlineVisiting();
      case SystemHub.Reception:
      default:
        return new OnlineReception();
    }
  }, [type]);

  const { handleError } = useErrorHandler();
  const { checkConnection } = useCheckConctionAgent();
  const [convertXmlToQCXmlMsg] = usePostApiOnlineConvertXmlToQcXmlMsgMutation({
    onError: (error) => handleError({ error }),
  });

  const { handleAuditLogMutation } = useAuditLog();

  const onCancel = useCallback(() => {
    if (
      confirmingType &&
      [
        "CONFIRMING_HOKEN_MY_INSURANCE",
        "CONFIRMING_KOHI_MY_INSURANCE",
      ].includes(confirmingType)
    ) {
      setSelectedInsurance(null);
    }
    resetOnlineData();
    waitingOQController.current?.abort();
    setLoading(false);
  }, [confirmingType, resetOnlineData, setSelectedInsurance]);

  const handleValidate = useCallback(
    (
      response?: DomainModelsOnlineQualificationConfirmationQcXmlMsgResponse,
    ) => {
      const qualificationValidity = Number(
        response?.messageBody?.qualificationValidity,
      );

      if (qualificationValidity === 5 && hasSelectPatient) {
        return setOpenSelectPatientModal(true);
      }

      if ([1, 2, 3, 4].includes(qualificationValidity)) {
        return setOpenValidateModal({
          type: "success",
          content:
            CONFIRM_CERTIFICATION_SUCCESS_MSG[
              qualificationValidity as keyof typeof CONFIRM_CERTIFICATION_SUCCESS_MSG
            ],
        });
      }

      if (Number(response?.messageHeader?.segmentOfResult) === 9) {
        return setOpenValidateModal({
          type: "error",
          content: `処理結果コード: ${response?.messageHeader?.errorCode ?? ""}\r\n${response?.messageHeader?.errorMessage}`,
        });
      }
      if (Number(response?.messageBody?.processingResultStatus) === 2) {
        return setOpenValidateModal({
          type: "error",
          content: `処理結果コード: ${response?.messageBody?.processingResultCode ?? ""}\r\n${response?.messageBody?.processingResultMessage}`,
        });
      }
    },
    [hasSelectPatient],
  );

  const handleConfirmOnlineHoken = useCallback(
    async (xmlString: string) => {
      const res = await convertXmlToQCXmlMsg({
        variables: {
          payload: {
            xmlString,
          },
        },
      });
      const response =
        res?.data?.postApiOnlineConvertXmlToQCXmlMsg?.data?.qcXmlMsgResponse;
      setXmlString(xmlString);
      setCheckDate(
        res.data?.postApiOnlineConvertXmlToQCXmlMsg?.data?.qcXmlMsgResponse
          ?.messageHeader?.qualificationConfirmationDate ?? "",
      );
      if (res.data?.postApiOnlineConvertXmlToQCXmlMsg?.data) {
        handleSetOnlineConfirmationHistoryData(
          res.data?.postApiOnlineConvertXmlToQCXmlMsg?.data,
        );
      }
      handleValidate(response);
      const resultOfQualificationConfirmation =
        response?.messageBody?.resultList?.resultOfQualificationConfirmation;

      if (resultOfQualificationConfirmation?.length) {
        setDataSource(resultOfQualificationConfirmation);
        const {
          pharmacistsInfoConsFlg,
          specificHealthCheckupsInfoConsFlg,
          diagnosisInfoConsFlg,
          operationInfoConsFlg,
        } = resultOfQualificationConfirmation[0]!;

        if (
          confirmingType &&
          ![
            "CONFIRMING_HOKEN_MY_INSURANCE",
            "CONFIRMING_KOHI_MY_INSURANCE",
            "ADDING_HOKEN_MY_INSURANCE",
            "ADDING_KOHI_MY_INSURANCE",
            "EDITING_HOKEN_MY_INSURANCE",
            "EDITING_KOHI_MY_INSURANCE",
          ].includes(confirmingType)
        ) {
          return;
        }

        let confirmationType = 2;
        if (
          confirmingType &&
          ["ADDING_PATIENT_MY_CARD", "ADDING_RECEPTION_MY_CARD"].includes(
            confirmingType,
          )
        ) {
          confirmationType = CONFIRMATION_TYPE.CONFIRMATION_PATIENT_INFO;
        }

        handleUpdateOnlineInsuranceData({
          isConfirmOnline: true,
          onlineConfirmationHistory: {
            uketukeStatus: 0,
            prescriptionIssueType: 0,
            ptId: ptId,
            infoConsFlg: stringToInfoConsFlg(
              pharmacistsInfoConsFlg ?? "",
              specificHealthCheckupsInfoConsFlg ?? "",
              diagnosisInfoConsFlg ?? "",
              operationInfoConsFlg ?? "",
            ),
            confirmationType,
            confirmationResult: xmlString,
          },
        });
      }

      const qualificationValidity = Number(
        response?.messageBody?.qualificationValidity,
      );

      const isNotEndFlow =
        (qualificationValidity === 5 && hasSelectPatient) ||
        [1, 2, 3, 4].includes(qualificationValidity) ||
        Number(response?.messageHeader?.segmentOfResult) === 9 ||
        Number(response?.messageBody?.processingResultStatus) === 2;

      callback?.(
        res.data?.postApiOnlineConvertXmlToQCXmlMsg?.data,
        isNotEndFlow,
      );
    },
    [
      callback,
      confirmingType,
      convertXmlToQCXmlMsg,
      handleUpdateOnlineInsuranceData,
      handleValidate,
      ptId,
    ],
  );

  const handleConfirmOnline = useCallback(
    async (confirmOnlineInfo: ConfirmOnlineInfo) => {
      try {
        const isConnected = await checkConnection();
        if (!isConnected) {
          return;
        }

        setLoading(true);
        waitingOQController.current?.abort();
        waitingOQController.current = new AbortController();

        const { birthdate, limitConsFlg, selectedHokenInf } = confirmOnlineInfo;

        const { fileName, msg } = createXmlFileUpdateConfirmHoken(
          birthdate,
          limitConsFlg ?? "0",
          {
            ...selectedHokenInf,
            ptId: ptId ?? "",
          },
        );

        handleAuditLogMutation({
          ptId: ptId,
          eventCd: AuditEventCode.OnlineQualificationVerification,
        });

        const res = await onlineClass.createFile(
          msg.XmlMsg,
          fileName,
          { signal: waitingOQController.current?.signal },
          "CreateXmlFile",
        );

        if (!res || waitingOQController.current.signal.aborted) {
          return onCancel();
        }
        await onlineClass.moveFile({ files: [res.data.fileName] });
        if (res.data.fileName.includes(CONFIRMATION_FILE_NAME.PATIENT_INFO)) {
          const requestFileName = res.data.fileName.replace("err", "xml");
          await onlineClass.moveFile({ files: [requestFileName] });
          setOpenValidateModal({
            type: "error",
            content: "通信環境をご確認のうえ、再度資格確認を行ってください。",
          });
        }

        if (res.data?.content) handleConfirmOnlineHoken(res.data.content);
      } catch (err) {
        onCancel();
        if (err instanceof Error) {
          return setOpenValidateModal({
            type: "error",
            content: err?.message,
          });
        }
        setOpenValidateModal({
          type: "error",
          content: "通信環境をご確認のうえ、再度資格確認を行ってください。",
        });
      } finally {
        setLoading(false);
      }
    },
    [
      handleAuditLogMutation,
      handleConfirmOnlineHoken,
      onCancel,
      onlineClass,
      ptId,
    ],
  );

  useEffect(() => {
    if (hasSelectPatient) return;
    if (!openValidateModal && dataSource.length) {
      onDone?.([...dataSource], checkDate);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dataSource, openValidateModal, hasSelectPatient, checkDate]);

  const ValidateOnlineConfirmationComponent = useCallback(() => {
    return (
      <>
        <OnlineCertificationLoadingModal open={isLoading} onClose={onCancel} />

        <RenderIf condition={openValidateModal?.type === "error"}>
          <OnlineCertificationErrorModal
            open={true}
            onClose={() => {
              resetOnlineData();
              setOpenValidateModal(undefined);
            }}
            content={openValidateModal?.content ?? ""}
          />
        </RenderIf>

        <RenderIf condition={openValidateModal?.type === "success"}>
          <OnlineCertificationSuccessModal
            open={true}
            onClose={() => {
              setOpenValidateModal(undefined);
              if (dataSource[0]) {
                handleSetSelectedResult?.(dataSource[0], checkDate);
              }
            }}
            content={openValidateModal?.content ?? ""}
          />
        </RenderIf>

        <RenderIf condition={openSelectPatientModal}>
          <SelectPatientInfoModal
            open={openSelectPatientModal}
            onClose={() => setOpenSelectPatientModal(false)}
            data={dataSource}
            onConfirm={(data) => handleSetSelectedResult?.(data, checkDate)}
          />
        </RenderIf>
      </>
    );
  }, [
    checkDate,
    dataSource,
    handleSetSelectedResult,
    isLoading,
    onCancel,
    openSelectPatientModal,
    openValidateModal?.content,
    openValidateModal?.type,
    resetOnlineData,
  ]);

  return useMemo(
    () => ({
      isLoading,
      openValidateModal,
      handleConfirmOnline,
      onCancel,
      ValidateOnlineConfirmationComponent,
      xmlString,
    }),
    [
      ValidateOnlineConfirmationComponent,
      handleConfirmOnline,
      isLoading,
      onCancel,
      openValidateModal,
      xmlString,
    ],
  );
};

class OnlineReception extends System {
  constructor() {
    super(SystemHub.Reception, [{}], {
      screenCode: SystemScreenCode.Reception,
    });
  }
}

class OnlinePatientInf extends System {
  constructor() {
    super(SystemHub.PatientInf, [{}], {
      screenCode: SystemScreenCode.PatientInfo,
    });
  }
}

class OnlineVisiting extends System {
  constructor() {
    super(SystemHub.Visiting, [{}], {
      screenCode: SystemScreenCode.Visiting,
    });
  }
}
