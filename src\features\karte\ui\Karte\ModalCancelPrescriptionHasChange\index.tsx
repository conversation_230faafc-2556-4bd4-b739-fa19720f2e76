import { useEffect, useMemo, useState } from "react";

import styled from "styled-components";
import { Spin, Typography } from "antd";
import { v4 as uuidv4 } from "uuid";
import dayjs from "dayjs";

import { Modal } from "@/components/ui/Modal";
import { But<PERSON> } from "@/components/ui/NewButton";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { Connection, System } from "@/utils/socket-helper";
import { usePostApiEpsUpdatePrescriptionStatusByIdsMutation } from "@/apis/gql/operations/__generated__/karte-retry-cancel";
import { SvgIconError } from "@/components/ui/Icon/IconError";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { usePrintOutPatientPrescription } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/usePrintOutPatientPrescription";
import {
  Flow,
  PaymentAutoCalculationFrom,
  useModal,
} from "@/features/karte/providers/ModalProvider";
import { useUpsertEpsRegister } from "@/hooks/useUpsertEpsRegister";

import { useEPrescriptionContext } from "../PrintSetting/EPrescriptionContextProvider";
import { useElectricSignature } from "../ElectronicSignature/useElectricSignature";
import { useObserverWaitingModalContext } from "../StationPrescription/ObserverWaitingModalActionProvider";
import { closeWindow } from "../PrintOutpatientPrescription/utils";

import type { DomainModelsEpsReqEpsReqModel } from "@/apis/gql/generated/types";

type ModalCancelPrescriptionHasChangeProps = {
  ptId: string;
  raiinNo: string;
  sinDate: number;
  prescriptionIdList: string[];
  openModalCancelPrescriptionHasChange: boolean;
  handleCloseModal: () => void;
};

enum MODAL_TYPE {
  REGISTER = "register",
  ERROR02 = "error02",
  ERROR06 = "error06",
  LOADING = "loading",
  CONFIRM = "confirm",
  LOADING_GET_INFO = "loadingGetInfo",
  CONFIRM_GET_INFO = "confirmGetInfo",
  LOADING_CREATE_CSV = "loadingCreateCSV",
}

const { Title, Text } = Typography;

const StyledModal = styled(Modal)<{
  $errorModal?: boolean;
}>`
  .ant-modal-header {
    background-color: ${({ $errorModal }) =>
      $errorModal ? "#e74c3c" : "#005BAC"};
  }
  .ant-modal-body {
    min-height: 256px;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .ant-modal-footer {
    height: 84px;
    align-items: center;
  }
`;

const ModalContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30px;
  min-height: 256px;
  width: 100%;
`;

const ModalContentConfirm = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 24px;
`;

const ContentErrorWapper = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 24px 50px;
`;

const ConfirmButton = styled(Button)`
  position: absolute;
  bottom: 8px;
  right: 24px;
`;

export const ModalCancelPrescriptionHasChange = ({
  ptId,
  raiinNo,
  sinDate,
  prescriptionIdList,
  openModalCancelPrescriptionHasChange,
  handleCloseModal,
}: ModalCancelPrescriptionHasChangeProps) => {
  const {
    handleOpenModal,
    state: stateModal,
    handleCloseModal: handleCloseModalProvider,
  } = useModal();
  const [isLoadingPrint, setIsLoadingPrint] = useState(false);
  const [disabledBtn, setDisabledBtn] = useState(true);
  const [showCreatePrescriptionInfo, setShowCreatePrescriptionInfo] =
    useState(false);
  const [modalType, setModalType] = useState<MODAL_TYPE>(MODAL_TYPE.LOADING);
  const [registerResult, setRegisterResult] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const [registerResultDIM06req, setRegisterResultDIM06req] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const [contentError, setContentError] = useState<{
    title: string;
    content?: string;
    content2?: string;
  }>({
    title: "",
    content: "",
    content2: "",
  });

  const { setFlow } = useObserverWaitingModalContext();

  const {
    statePrintSetting: { isOutpatientPrescription, outpatientOption },
    setFlagRegisterChange,
    setStatePrintSetting,
    updateEpsRegister,
    isClosePageKarte,
  } = useEPrescriptionContext();

  const { handleError } = useErrorHandler();
  const { handlePrintOutPatientPrescriptionPaper } =
    usePrintOutPatientPrescription();

  useEffect(() => {
    if (contentError.title) {
      handlePrintOutPatientPrescriptionPaper().then();
    }
  }, [contentError]);

  const { data: dataSystemConf } = useGetApiSystemConfGetListQuery({
    onError: (error) => {
      handleCloseModal();
      handleError({ error });
    },
  });

  const { isSignatureLocal } = useElectricSignature({});

  const [updateStatusPrescription] =
    usePostApiEpsUpdatePrescriptionStatusByIdsMutation({
      onError: (error) => {
        handleCloseModal();
        handleError({ error });
      },
    });

  const { handleUpsertEpsRegister } = useUpsertEpsRegister();

  const handleUpdateStatus = (id: string | undefined, status: number) => {
    updateStatusPrescription({
      variables: {
        emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput: {
          ptId,
          raiinNo,
          sinDate,
          prescriptionStatusItem: [
            {
              prescriptionId: id,
              status: status,
            },
          ],
        },
      },
      onError: (error) => {
        handleError({ error }).then();
      },
    });
  };

  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;

  const system = new System("/medical", systemConf);
  const connect = new Connection("/medical");

  const timeGetResult = useMemo(() => {
    const dataSystemConfFiltered =
      dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.find(
        (item) => item.grpCd === 100040 && item.grpEdaNo === 4,
      );
    return Number(dataSystemConfFiltered?.val) * 1000;
  }, [dataSystemConf]);

  const handleGoToStep6AndPrint = () => {
    setIsLoadingPrint(true);
    handlePrintOutPatientPrescriptionPaper().then();
    setIsLoadingPrint(false);
  };

  const submit = () => {
    if (registerResult) {
      updateEpsRegister(registerResult, null);
    }
    if (registerResultDIM06req) {
      updateEpsRegister(registerResultDIM06req, null);
    }
    setFlagRegisterChange("OFF");
    connect.disconnect();
    handleGoToStep6AndPrint();
    handleCloseModal();
  };

  const handleCloseModalCancelPrescriptionHasChange = () => {
    if (registerResult) {
      updateEpsRegister(registerResult, null);
    }

    if (registerResultDIM06req) {
      updateEpsRegister(registerResultDIM06req, null);
    }
    if (modalType === MODAL_TYPE.LOADING_GET_INFO) {
      setModalType(MODAL_TYPE.CONFIRM_GET_INFO);
    }

    if (modalType === MODAL_TYPE.REGISTER) {
      if (!isClosePageKarte) {
        handleCloseModal();
        if (stateModal.paymentAutoCalculationOpen) {
          handleOpenModal(
            "PAYMENT_AUTO_CALCULATION",
            undefined,
            Flow.Flow2,
            undefined,
            undefined,
            PaymentAutoCalculationFrom.Accounting,
          );
        }
      } else {
        closeWindow(isClosePageKarte);
      }
    }

    if (
      modalType !== MODAL_TYPE.REGISTER &&
      modalType !== MODAL_TYPE.LOADING_GET_INFO
    ) {
      connect.disconnect();
      handleCloseModal();
    }
  };

  const handleShowConfirm = () => {
    if (modalType === MODAL_TYPE.CONFIRM_GET_INFO) {
      window.close();
    }
    if (
      !isOutpatientPrescription ||
      modalType === MODAL_TYPE.LOADING_CREATE_CSV
    ) {
      submit();
    }
    if (modalType !== MODAL_TYPE.CONFIRM_GET_INFO && isOutpatientPrescription) {
      setModalType(MODAL_TYPE.CONFIRM);
    }
  };

  useEffect(() => {
    if (timeGetResult) {
      setTimeout(() => {
        setDisabledBtn(false);
      }, timeGetResult);
    }
  }, [timeGetResult]);

  useEffect(() => {
    if (prescriptionIdList.length > 0) {
      setStatePrintSetting((prev) => ({
        ...prev,
        isCreatePrescription: false,
      }));
      const sendReq = async () => {
        let i = 0;
        while (i < prescriptionIdList.length) {
          const currentIndex = i;
          const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;

          const registerResult = await handleUpsertEpsRegister({
            arbitraryFileIdentifier,
            reqDate: +dayjs().format("YYYYMMDD"),
            raiinNo: raiinNo,
            ptId: ptId,
            sinDate: sinDate,
            prescriptionId: prescriptionIdList[currentIndex],
            dispensingResultId: "",
            reqType: 6,
            status: 1,
            resultCode: "",
            resultMessage: "",
            result: "",
          });

          if (registerResult) {
            setRegisterResult(registerResult);
          }

          await new Promise<void>((resolve) => {
            const messageBody = {
              PrescriptionId: prescriptionIdList[currentIndex],
            };
            system
              .createFile(
                {
                  messageHeader: {
                    ArbitraryFileIdentifier: arbitraryFileIdentifier,
                  },
                  messageBody,
                },
                "EPSsiPIR02req",
              )
              .then(async (files) => {
                let xmlContent;
                try {
                  xmlContent = JSON.parse(files?.data?.content || "{}");
                } catch {
                  const contentError = files?.data?.content;
                  const codeError = `${contentError}`.match(/\[(.*?)\]/);
                  setModalType(MODAL_TYPE.ERROR02);
                  setContentError({
                    title: "調剤結果を取得しています",
                    content: `処理結果コード:${codeError && codeError[1] ? codeError[1] : ""}`,
                    content2: contentError,
                  });
                }
                // const xmlContent = JSON.parse(files?.data?.content || "{}");
                const msgBody = xmlContent?.XmlMsg?.MessageBody;
                const segmentOfResult =
                  xmlContent?.XmlMsg?.MessageHeader?.SegmentOfResult;
                const processingResultStatus =
                  xmlContent?.XmlMsg?.MessageBody?.ProcessingResultStatus;

                const processingResultMessage =
                  xmlContent?.XmlMsg?.MessageBody?.ProcessingResultMessage;

                const errorCode = xmlContent?.XmlMsg?.MessageHeader?.ErrorCode;
                const errorMsg =
                  xmlContent?.XmlMsg?.MessageHeader?.ErrorMessage;
                const processingResultCode =
                  xmlContent?.XmlMsg?.MessageBody?.ProcessingResultCode;

                const isShowError =
                  errorCode !== "EPSB1030W" &&
                  processingResultCode !== "EPSB1030W" &&
                  errorCode !== "EPSB1032W" &&
                  processingResultCode !== "EPSB1032W";

                if (!msgBody && Number(segmentOfResult) === 9 && isShowError) {
                  setModalType(MODAL_TYPE.ERROR02);
                  setContentError({
                    title: "調剤結果を取得しています",
                    content: `エラーコード: ${errorCode}\n${errorMsg}`,
                  });
                }

                if (registerResult) {
                  updateEpsRegister(registerResult, null);
                }

                if (
                  msgBody &&
                  Number(processingResultStatus) === 2 &&
                  isShowError
                ) {
                  setModalType(MODAL_TYPE.ERROR02);
                  setContentError({
                    title: "調剤結果を取得しています",
                    content: `処理結果コード: ${processingResultCode}\n${processingResultMessage}`,
                  });
                }

                // if (files?.data?.fileName?.includes(".err") && isShowError) {
                //   setModalType(MODAL_TYPE.ERROR02);
                //   setContentError({
                //     title: "調剤結果を取得しています",
                //     content: `${xmlContent}`,
                //   });
                // }

                if (
                  (Number(segmentOfResult) === 1 &&
                    Number(processingResultStatus) === 1) ||
                  errorCode === "EPSB1030W" ||
                  processingResultCode === "EPSB1030W"
                ) {
                  handleUpdateStatus(prescriptionIdList[currentIndex], 3);
                  resolve();
                }

                if (
                  errorCode === "EPSB1032W" ||
                  processingResultCode === "EPSB1032W"
                ) {
                  setModalType(MODAL_TYPE.LOADING_GET_INFO);
                  connect.disconnect();
                  const arbitraryFileIdentifierDIM06req = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;

                  const registerResultDIM06req = await handleUpsertEpsRegister({
                    arbitraryFileIdentifier,
                    reqDate: +dayjs().format("YYYYMMDD"),
                    raiinNo: raiinNo,
                    ptId: ptId,
                    sinDate: sinDate,
                    prescriptionId: prescriptionIdList[currentIndex],
                    dispensingResultId: "",
                    reqType: 8,
                    status: 1,
                    resultCode: "",
                    resultMessage: "",
                    result: "",
                  });
                  if (registerResultDIM06req) {
                    setRegisterResultDIM06req(registerResultDIM06req);
                  }
                  await new Promise<void>((resolveGetInfo) => {
                    const dataSystemConfFiltered =
                      dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.find(
                        (item) => item.grpCd === 100040 && item.grpEdaNo === 8,
                      );

                    const messageBody = {
                      PrescriptionId: prescriptionIdList[currentIndex],
                      RefillSupported: dataSystemConfFiltered?.val,
                    };

                    system
                      .createFile(
                        {
                          messageHeader: {
                            ArbitraryFileIdentifier:
                              arbitraryFileIdentifierDIM06req,
                          },
                          messageBody,
                        },
                        "EPSsiDIM06req",
                      )
                      .then((filesGetInfo) => {
                        let xmlContentGetInfo;
                        try {
                          xmlContentGetInfo = JSON.parse(
                            filesGetInfo?.data?.content || "{}",
                          );
                        } catch {
                          const contentError = files?.data?.content;
                          const codeError = `${contentError}`.match(
                            /\[(.*?)\]/,
                          );
                          setModalType(MODAL_TYPE.ERROR06);
                          setContentError({
                            title: "調剤結果を取得しています",
                            content: `処理結果コード:${codeError && codeError[1] ? codeError[1] : ""}`,
                            content2: contentError,
                          });
                        }
                        // const xmlContentGetInfo = JSON.parse(
                        //   filesGetInfo?.data?.content || "{}",
                        // );
                        const msgBodyGetInfo =
                          xmlContentGetInfo?.XmlMsg?.MessageBody;
                        const processingResultStatusGetInfo =
                          xmlContentGetInfo?.XmlMsg?.MessageBody
                            ?.ProcessingResultStatus;
                        const processingResultCodeGetInfo =
                          xmlContentGetInfo?.XmlMsg?.MessageBody
                            ?.ProcessingResultCode;
                        const processingResultMessageGetInfo =
                          xmlContentGetInfo?.XmlMsg?.MessageBody
                            ?.ProcessingResultMessage;

                        const segmentOfResultGetInfo =
                          xmlContentGetInfo?.XmlMsg?.MessageHeader
                            ?.SegmentOfResult;
                        const errorCodeGetInfo =
                          xmlContentGetInfo?.XmlMsg?.MessageHeader?.ErrorCode;
                        const errorMsgGetInfo =
                          xmlContentGetInfo?.XmlMsg?.MessageHeader
                            ?.ErrorMessage;

                        if (registerResultDIM06req) {
                          updateEpsRegister(registerResultDIM06req, null);
                        }
                        if (
                          !msgBodyGetInfo &&
                          Number(segmentOfResultGetInfo) === 9
                        ) {
                          setModalType(MODAL_TYPE.ERROR06);
                          setContentError({
                            title: "調剤結果を取得しています",
                            content: `エラーコード: ${errorCodeGetInfo}\n${errorMsgGetInfo}`,
                          });
                        }

                        if (
                          msgBodyGetInfo &&
                          Number(processingResultStatusGetInfo) === 2
                        ) {
                          setModalType(MODAL_TYPE.ERROR06);
                          setContentError({
                            title: "調剤結果を取得しています",
                            content: `処理結果コード: ${processingResultCodeGetInfo}\n${processingResultMessageGetInfo}`,
                          });
                        }

                        // if (filesGetInfo?.data?.fileName?.includes(".err")) {
                        //   setModalType(MODAL_TYPE.ERROR06);
                        //   setContentError({
                        //     title: "調剤結果を取得しています",
                        //     content: `${xmlContentGetInfo}`,
                        //   });
                        // }

                        const prescriptionStatusGetInfo =
                          xmlContentGetInfo?.XmlMsg?.MessageBody
                            ?.PrescriptionStatus;

                        if (
                          prescriptionStatusGetInfo ===
                          "当該処方箋は処方箋取消されています。"
                        ) {
                          handleUpdateStatus(
                            prescriptionIdList[currentIndex],
                            3,
                          );
                          resolveGetInfo();
                          resolve();
                        }

                        if (
                          prescriptionStatusGetInfo ===
                          "薬局にて受付されていません。"
                        ) {
                          handleUpdateStatus(
                            prescriptionIdList[currentIndex],
                            1,
                          );
                          resolveGetInfo();
                          resolve();
                        }

                        if (
                          prescriptionStatusGetInfo &&
                          prescriptionStatusGetInfo !==
                            "薬局にて受付されていません。" &&
                          prescriptionStatusGetInfo !==
                            "当該処方箋は処方箋取消されています。"
                        ) {
                          handleUpdateStatus(
                            prescriptionIdList[currentIndex],
                            0,
                          );
                          setModalType(MODAL_TYPE.REGISTER);
                        }
                      });
                  });
                }
              });
          });
          i++;
        }

        console.log("vao day r 421");
        if (prescriptionIdList.length === i) {
          console.log("vao day r 422");
          setShowCreatePrescriptionInfo(true);
          // handleCloseModalCancelPrescriptionHasChange();
        }
      };
      const prescriptionStatusItem = prescriptionIdList.map((item) => ({
        deletedReason: 2,
        status: 2,
        prescriptionId: item,
      }));

      updateStatusPrescription({
        variables: {
          emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput: {
            prescriptionStatusItem,
            ptId: String(ptId),
            raiinNo: String(raiinNo),
            sinDate: Number(sinDate),
          },
        },
      }).then((res) => {
        if (
          res.data?.postApiEpsUpdatePrescriptionStatusByIds?.data?.isSuccess
        ) {
          sendReq();
        }
      });
    }
  }, [prescriptionIdList]);

  const textCloseModal = useMemo(() => {
    switch (modalType) {
      case MODAL_TYPE.REGISTER:
        return "閉じる";

      case MODAL_TYPE.LOADING_GET_INFO:
        return "中断";

      default:
        return "キャンセル";
    }
  }, [modalType]);

  const titleModal = useMemo(() => {
    switch (modalType) {
      case MODAL_TYPE.REGISTER:
        return "処方箋情報の変更";

      case MODAL_TYPE.CONFIRM:
        return "処方箋発行形態の確認";

      case MODAL_TYPE.CONFIRM_GET_INFO:
        return "処方箋情報の登録";

      case MODAL_TYPE.ERROR02:
        return "エラー";

      case MODAL_TYPE.ERROR06:
        return "エラー";

      default:
        return "処理中";
    }
  }, [modalType]);

  const contentModal = useMemo(() => {
    switch (modalType) {
      case MODAL_TYPE.ERROR02:
        return (
          <ContentErrorWapper>
            <SvgIconError />
            <Title level={2}>{contentError.title}</Title>
            <div style={{ width: "100%" }}>
              {contentError.content}
              <div>{contentError.content2}</div>
              {contentError.content2 ? null : (
                <div>
                  処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？
                </div>
              )}
            </div>
          </ContentErrorWapper>
        );

      case MODAL_TYPE.ERROR06:
        return (
          <ContentErrorWapper>
            <SvgIconError />
            <Title level={2}>{contentError.title}</Title>
            <div style={{ width: "100%" }}>
              {contentError.content}
              <div>{contentError.content2}</div>
              {contentError.content2 ? null : (
                <div>薬局で受付済または、取消済の処方箋です。</div>
              )}
            </div>
          </ContentErrorWapper>
        );

      case MODAL_TYPE.REGISTER:
        return (
          <ModalContentConfirm>
            <Title level={2} style={{ fontSize: 24 }}>
              処方箋情報を変更できません
            </Title>
            <Text>薬局で受付済または、取消済の処方箋です。</Text>
          </ModalContentConfirm>
        );

      case MODAL_TYPE.CONFIRM:
        return (
          <ModalContentConfirm>
            <Title level={2}>処方箋情報の登録が完了していません</Title>
            <Text>
              処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？
            </Text>
          </ModalContentConfirm>
        );

      case MODAL_TYPE.CONFIRM_GET_INFO:
        return (
          <ModalContentConfirm>
            <Title level={2} style={{ fontSize: 24 }}>
              処方箋情報を登録できません
            </Title>
            <Text>薬局で受付済または、取消済の処方箋です。</Text>
          </ModalContentConfirm>
        );

      case MODAL_TYPE.LOADING_GET_INFO:
        return (
          <ModalContentWrapper>
            <span>調剤結果を取得しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );

      case MODAL_TYPE.LOADING_CREATE_CSV:
        return (
          <ModalContentWrapper>
            <span>処方箋情報CSVを作成しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );

      default:
        return (
          <ModalContentWrapper>
            <span>処方箋情報を取り消しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );
    }
  }, [modalType, contentError]);

  useEffect(() => {
    console.log("showCreatePrescriptionInfo", showCreatePrescriptionInfo);
    console.log("isSignatureLocal", isSignatureLocal);
    const fileType = outpatientOption === "3" ? 2 : 1;

    if (showCreatePrescriptionInfo) {
      if (!isSignatureLocal && fileType == 1) {
        console.log("vao day r 572");
        setFlow("REMOTE");
        handleCloseModalProvider("IS_PROCESS_STATION_PRESCRIPTION");
        setTimeout(() => {
          handleOpenModal("IS_PROCESS_STATION_PRESCRIPTION");
        }, 1000);
      } else {
        console.log("vao day r 575");
        setFlow("LOCAL");
        handleOpenModal("IS_PROCESS_FLOW_LOCAL_PRESCRIPTION");
      }
      handleCloseModalCancelPrescriptionHasChange();
    }
  }, [
    showCreatePrescriptionInfo,
    isSignatureLocal,
    outpatientOption,
    handleOpenModal,
    handleCloseModalCancelPrescriptionHasChange,
  ]);

  const isModalError =
    modalType === MODAL_TYPE.ERROR06 || modalType === MODAL_TYPE.ERROR02;

  return (
    <StyledModal
      centered
      $errorModal={
        modalType === MODAL_TYPE.ERROR02 || modalType === MODAL_TYPE.ERROR06
      }
      title={titleModal}
      width={480}
      isOpen={openModalCancelPrescriptionHasChange}
      onCancel={handleCloseModalCancelPrescriptionHasChange}
      centerFooterContent={
        MODAL_TYPE.CONFIRM !== modalType && MODAL_TYPE.ERROR02 !== modalType
      }
      footer={
        MODAL_TYPE.CONFIRM === modalType || MODAL_TYPE.ERROR02 === modalType
          ? [
              <Button
                key={"cancel"}
                varient="tertiary"
                onClick={() => setModalType(MODAL_TYPE.LOADING)}
              >
                戻る
              </Button>,
              <Button
                key={"ok"}
                varient="primary"
                onClick={submit}
                loading={isLoadingPrint}
              >
                発行
              </Button>,
            ]
          : [
              <Button
                key={"cancel"}
                varient="tertiary"
                onClick={handleCloseModalCancelPrescriptionHasChange}
                disabled={isModalError ? false : disabledBtn}
              >
                {textCloseModal}
              </Button>,
            ]
      }
    >
      {contentModal}
      {modalType !== MODAL_TYPE.ERROR02 &&
      modalType !== MODAL_TYPE.LOADING_GET_INFO &&
      modalType !== MODAL_TYPE.REGISTER &&
      modalType !== MODAL_TYPE.CONFIRM ? (
        <ConfirmButton
          varient="inline"
          onClick={isModalError ? handleGoToStep6AndPrint : handleShowConfirm}
          disabled={isModalError ? false : disabledBtn}
        >
          処方箋情報を登録しない
        </ConfirmButton>
      ) : null}
    </StyledModal>
  );
};
