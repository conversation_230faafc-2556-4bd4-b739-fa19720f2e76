import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetApiMstItemGetAllCmtCheckMstQueryVariables = Types.Exact<{
  sinDay?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiMstItemGetAllCmtCheckMstQuery = {
  __typename?: "query_root";
  getApiMstItemGetAllCmtCheckMst?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesMstItemGetAllCmtCheckMstResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesMstItemGetAllCmtCheckMstResponse";
      itemCmtModels?: Array<{
        __typename?: "DomainModelsMstItemCommentCheckMstModel";
        hpId?: number;
        isDeleted?: number;
        santeiItemCd?: string;
        masterSbt?: string;
        yohoKbn?: number;
        itemCd?: string;
        kanaName1?: string;
        kanaName2?: string;
        kanaName3?: string;
        kohatuKbn?: number;
        kohatuKbnDisplay?: string;
        kouiName?: string;
        ten?: number;
        tenDisplay?: string;
        tenId?: number;
        tenMstName?: string;
      }>;
    };
  };
};

export type PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationVariables =
  Types.Exact<{
    input?: Types.InputMaybe<Types.EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput>;
  }>;

export type PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation =
  {
    __typename?: "mutation_root";
    postApiEpsCheckErrorTodayOdrForEPSRegistration?: {
      __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesEpsCheckErrorTodayOdrForEpsRegistrationResponse";
      message?: string;
      status?: number;
      data?: {
        __typename?: "EmrCloudApiResponsesEpsCheckErrorTodayOdrForEpsRegistrationResponse";
        errorMessageIsErrorAcceptedByPharmacy?: string;
        errorMessageIsErrorDetailBunkatu?: string;
        errorMessageIsErrorDetailRefill?: string;
        errorMessageIsErrorHokenExpired?: string;
        errorMessageIsErrorHokenNotCoverDrug?: string;
        errorMessageIsErrorHokenNotHealthInsurance?: string;
        errorMessageIsErrorManyHokenUsingForRp?: string;
        errorMessageIsErrorMedicalMaterialsNotCovered?: string;
        errorMessageIsErrrorContainsGenericNamesNotIncluded?: string;
        flagPrintDrugInformationSheet?: string;
        flagPrintHospitalPrescription?: string;
        flagPrintInstruction?: string;
        flagPrintOutpatientPrescription?: string;
      };
    };
  };

export type GetApiReceptionGetLastRaiinInfsForPrintSettingQueryVariables =
  Types.Exact<{
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    isLastVisit?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  }>;

export type GetApiReceptionGetLastRaiinInfsForPrintSettingQuery = {
  __typename?: "query_root";
  getApiReceptionGetLastRaiinInfs?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesReceptionGetLastRaiinInfsResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesReceptionGetLastRaiinInfsResponse";
      data?: Array<{
        __typename?: "DomainModelsReceptionReceptionModel";
        sinDate?: number;
        tantoId?: number;
        canCombine?: number;
        comment?: string;
        confirmationType?: number;
        departmentSName?: string;
        hasMessage?: boolean;
        hokenHoubetu?: string;
        hokenId?: number;
        hokenKbn?: number;
        hokenKbnName?: string;
        hokenName?: string;
        hokenPid?: number;
        hokenSbtCd?: number;
        hokensyaNo?: string;
        houbetu?: string;
        hpId?: number;
        infoConsFlg?: string;
        isDeleted?: boolean;
        isYoyaku?: number;
        jikanKbn?: number;
        kaId?: number;
        kaSname?: string;
        kaikeiId?: number;
        kaikeiTime?: string;
        kateEditionRaiinNo?: string;
        onlineConfirmationHistoryId?: number;
        oyaRaiinNo?: string;
        raiinNo?: string;
        prescriptionIssueType?: number;
        printEpsReference?: number;
        syosaisinKbn?: number;
        updateDate?: string;
      }>;
    };
  };
};

export type GetApiPdfCreatorExportDrugInfoQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiPdfCreatorExportDrugInfoQuery = {
  __typename?: "query_root";
  getApiPdfCreatorExportDrugInfo?: {
    __typename?: "ExportDrugInfoRespone";
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type GetApiPdfCreatorExportSijisenQueryVariables = Types.Exact<{
  formType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  odrKouiKbns?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.EmrCloudApiRequestsExportPdFLimitModelInput>>
    | Types.InputMaybe<Types.EmrCloudApiRequestsExportPdFLimitModelInput>
  >;
  printNoOdr?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiPdfCreatorExportSijisenQuery = {
  __typename?: "query_root";
  getApiPdfCreatorExportSijisen?: {
    __typename?: "ExportSijisenRespone";
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type PostApiPdfCreatorExportSijisenMutationVariables = Types.Exact<{
  emrCloudApiRequestsExportPdFSijisenExportRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsExportPdFSijisenExportRequestInput>;
}>;

export type PostApiPdfCreatorExportSijisenMutation = {
  __typename?: "mutation_root";
  postApiPdfCreatorExportSijisen?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesPdfCreatorExportSijisenResponse";
    data?: {
      __typename?: "EmrCloudApiResponsesPdfCreatorExportSijisenResponse";
      base64Pdfs?: Array<string>;
      fileUrl?: string;
      message?: string;
      status?: number;
    };
  };
};

export type GetApiPdfCreatorExportKarte1PrintSettingQueryVariables =
  Types.Exact<{
    hokenPid?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    syuByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    tenkiByomei?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  }>;

export type GetApiPdfCreatorExportKarte1PrintSettingQuery = {
  __typename?: "query_root";
  getApiPdfCreatorExportKarte1?: {
    __typename?: "ExportKarte1Respone";
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type GetApiPdfCreatorOutDrugQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hokenGp?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  epsPrintType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  delayPrint?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  windowType?: Types.InputMaybe<Types.WindowType>;
  registrationChecked?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isPrescriptionCanCancel?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  printEpsReference?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  issueType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type GetApiPdfCreatorOutDrugQuery = {
  __typename?: "query_root";
  getApiPdfCreatorOutDrug?: {
    __typename?: "ExportOutDrugRespone";
    base64Pdfs?: Array<string>;
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type GetApiPdfCreatorInDrugQueryVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
}>;

export type GetApiPdfCreatorInDrugQuery = {
  __typename?: "query_root";
  getApiPdfCreatorInDrug?: {
    __typename?: "ExportInDrugRespone";
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type GetApiPdfCreatorExportKarte2PrintSettingQueryVariables =
  Types.Exact<{
    deletedOdrVisibilitySetting?: Types.InputMaybe<
      Types.Scalars["Int"]["input"]
    >;
    emptyMode?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    endDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    hpId?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    isCheckedApproved?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedDoctor?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedEndTime?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedHideOrder?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedHoken?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedHokenJibai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedHokenJihi?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedHokenRousai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedInputDate?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedJihi?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedJihiRece?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedSetName?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedStartTime?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedSyosai?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isCheckedVisitingTime?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isIncludeTempSave?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isIppanNameChecked?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isUketsukeNameChecked?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
    sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    startDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
    includeDraft?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
    isGetVersionData?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  }>;

export type GetApiPdfCreatorExportKarte2PrintSettingQuery = {
  __typename?: "query_root";
  getApiPdfCreatorExportKarte2?: {
    __typename?: "getApiPdfCreatorExportKarte2Response";
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export type PostApiPdfCreatorOutDrugMutationVariables = Types.Exact<{
  ptId?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  sinDate?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  raiinNo?: Types.InputMaybe<Types.Scalars["BigInt"]["input"]>;
  hokenGp?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  epsPrintType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  delayPrint?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  windowType?: Types.InputMaybe<Types.WindowType>;
  registrationChecked?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  isPrescriptionCanCancel?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
  printEpsReference?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
  issueType?: Types.InputMaybe<Types.Scalars["Int"]["input"]>;
}>;

export type PostApiPdfCreatorOutDrugMutation = {
  __typename?: "mutation_root";
  postApiPdfCreatorOutDrug?: {
    __typename?: "ExportOutDrugRespone";
    base64Pdfs?: Array<string>;
    fileUrl?: string;
    message?: string;
    status?: number;
  };
};

export const GetApiMstItemGetAllCmtCheckMstDocument = gql`
  query getApiMstItemGetAllCmtCheckMst($sinDay: Int) {
    getApiMstItemGetAllCmtCheckMst(sinDay: $sinDay) {
      data {
        itemCmtModels {
          hpId
          isDeleted
          santeiItemCd
          masterSbt
          yohoKbn
          itemCd
          kanaName1
          kanaName2
          kanaName3
          kohatuKbn
          kohatuKbnDisplay
          kouiName
          ten
          tenDisplay
          tenId
          tenMstName
        }
      }
    }
  }
`;

/**
 * __useGetApiMstItemGetAllCmtCheckMstQuery__
 *
 * To run a query within a React component, call `useGetApiMstItemGetAllCmtCheckMstQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiMstItemGetAllCmtCheckMstQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiMstItemGetAllCmtCheckMstQuery({
 *   variables: {
 *      sinDay: // value for 'sinDay'
 *   },
 * });
 */
export function useGetApiMstItemGetAllCmtCheckMstQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiMstItemGetAllCmtCheckMstQuery,
    GetApiMstItemGetAllCmtCheckMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiMstItemGetAllCmtCheckMstQuery,
    GetApiMstItemGetAllCmtCheckMstQueryVariables
  >(GetApiMstItemGetAllCmtCheckMstDocument, options);
}
export function useGetApiMstItemGetAllCmtCheckMstLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiMstItemGetAllCmtCheckMstQuery,
    GetApiMstItemGetAllCmtCheckMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiMstItemGetAllCmtCheckMstQuery,
    GetApiMstItemGetAllCmtCheckMstQueryVariables
  >(GetApiMstItemGetAllCmtCheckMstDocument, options);
}
export function useGetApiMstItemGetAllCmtCheckMstSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiMstItemGetAllCmtCheckMstQuery,
    GetApiMstItemGetAllCmtCheckMstQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiMstItemGetAllCmtCheckMstQuery,
    GetApiMstItemGetAllCmtCheckMstQueryVariables
  >(GetApiMstItemGetAllCmtCheckMstDocument, options);
}
export type GetApiMstItemGetAllCmtCheckMstQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetAllCmtCheckMstQuery
>;
export type GetApiMstItemGetAllCmtCheckMstLazyQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetAllCmtCheckMstLazyQuery
>;
export type GetApiMstItemGetAllCmtCheckMstSuspenseQueryHookResult = ReturnType<
  typeof useGetApiMstItemGetAllCmtCheckMstSuspenseQuery
>;
export type GetApiMstItemGetAllCmtCheckMstQueryResult = Apollo.QueryResult<
  GetApiMstItemGetAllCmtCheckMstQuery,
  GetApiMstItemGetAllCmtCheckMstQueryVariables
>;
export const PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingDocument = gql`
  mutation postApiEpsCheckErrorTodayOdrForEPSRegistrationPrintSetting(
    $input: EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput
  ) {
    postApiEpsCheckErrorTodayOdrForEPSRegistration(
      emrCloudApiRequestsEpsGetPreRegistrationDataRequestInput: $input
    ) {
      data {
        errorMessageIsErrorAcceptedByPharmacy
        errorMessageIsErrorDetailBunkatu
        errorMessageIsErrorDetailRefill
        errorMessageIsErrorHokenExpired
        errorMessageIsErrorHokenNotCoverDrug
        errorMessageIsErrorHokenNotHealthInsurance
        errorMessageIsErrorManyHokenUsingForRp
        errorMessageIsErrorMedicalMaterialsNotCovered
        errorMessageIsErrrorContainsGenericNamesNotIncluded
        flagPrintDrugInformationSheet
        flagPrintHospitalPrescription
        flagPrintInstruction
        flagPrintOutpatientPrescription
      }
      message
      status
    }
  }
`;
export type PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationFn =
  Apollo.MutationFunction<
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation,
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationVariables
  >;

/**
 * __usePostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation__
 *
 * To run a mutation, you first call `usePostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation, { data, loading, error }] = usePostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function usePostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation,
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation,
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationVariables
  >(
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingDocument,
    options,
  );
}
export type PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationHookResult =
  ReturnType<
    typeof usePostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation
  >;
export type PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationResult =
  Apollo.MutationResult<PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation>;
export type PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutation,
    PostApiEpsCheckErrorTodayOdrForEpsRegistrationPrintSettingMutationVariables
  >;
export const GetApiReceptionGetLastRaiinInfsForPrintSettingDocument = gql`
  query getApiReceptionGetLastRaiinInfsForPrintSetting(
    $ptId: BigInt
    $sinDate: Int
    $isLastVisit: Boolean
  ) {
    getApiReceptionGetLastRaiinInfs(
      isLastVisit: $isLastVisit
      ptId: $ptId
      sinDate: $sinDate
    ) {
      data {
        data {
          sinDate
          tantoId
          canCombine
          comment
          confirmationType
          departmentSName
          hasMessage
          hokenHoubetu
          hokenId
          hokenKbn
          hokenKbnName
          hokenName
          hokenPid
          hokenSbtCd
          hokensyaNo
          houbetu
          hpId
          infoConsFlg
          isDeleted
          isYoyaku
          jikanKbn
          kaId
          kaSname
          kaikeiId
          kaikeiTime
          kateEditionRaiinNo
          onlineConfirmationHistoryId
          oyaRaiinNo
          raiinNo
          prescriptionIssueType
          printEpsReference
          syosaisinKbn
          updateDate
        }
      }
    }
  }
`;

/**
 * __useGetApiReceptionGetLastRaiinInfsForPrintSettingQuery__
 *
 * To run a query within a React component, call `useGetApiReceptionGetLastRaiinInfsForPrintSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiReceptionGetLastRaiinInfsForPrintSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiReceptionGetLastRaiinInfsForPrintSettingQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      isLastVisit: // value for 'isLastVisit'
 *   },
 * });
 */
export function useGetApiReceptionGetLastRaiinInfsForPrintSettingQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiReceptionGetLastRaiinInfsForPrintSettingQuery,
    GetApiReceptionGetLastRaiinInfsForPrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiReceptionGetLastRaiinInfsForPrintSettingQuery,
    GetApiReceptionGetLastRaiinInfsForPrintSettingQueryVariables
  >(GetApiReceptionGetLastRaiinInfsForPrintSettingDocument, options);
}
export function useGetApiReceptionGetLastRaiinInfsForPrintSettingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiReceptionGetLastRaiinInfsForPrintSettingQuery,
    GetApiReceptionGetLastRaiinInfsForPrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiReceptionGetLastRaiinInfsForPrintSettingQuery,
    GetApiReceptionGetLastRaiinInfsForPrintSettingQueryVariables
  >(GetApiReceptionGetLastRaiinInfsForPrintSettingDocument, options);
}
export function useGetApiReceptionGetLastRaiinInfsForPrintSettingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiReceptionGetLastRaiinInfsForPrintSettingQuery,
    GetApiReceptionGetLastRaiinInfsForPrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiReceptionGetLastRaiinInfsForPrintSettingQuery,
    GetApiReceptionGetLastRaiinInfsForPrintSettingQueryVariables
  >(GetApiReceptionGetLastRaiinInfsForPrintSettingDocument, options);
}
export type GetApiReceptionGetLastRaiinInfsForPrintSettingQueryHookResult =
  ReturnType<typeof useGetApiReceptionGetLastRaiinInfsForPrintSettingQuery>;
export type GetApiReceptionGetLastRaiinInfsForPrintSettingLazyQueryHookResult =
  ReturnType<typeof useGetApiReceptionGetLastRaiinInfsForPrintSettingLazyQuery>;
export type GetApiReceptionGetLastRaiinInfsForPrintSettingSuspenseQueryHookResult =
  ReturnType<
    typeof useGetApiReceptionGetLastRaiinInfsForPrintSettingSuspenseQuery
  >;
export type GetApiReceptionGetLastRaiinInfsForPrintSettingQueryResult =
  Apollo.QueryResult<
    GetApiReceptionGetLastRaiinInfsForPrintSettingQuery,
    GetApiReceptionGetLastRaiinInfsForPrintSettingQueryVariables
  >;
export const GetApiPdfCreatorExportDrugInfoDocument = gql`
  query getApiPdfCreatorExportDrugInfo(
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
  ) {
    getApiPdfCreatorExportDrugInfo(
      raiinNo: $raiinNo
      ptId: $ptId
      sinDate: $sinDate
    ) {
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorExportDrugInfoQuery__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorExportDrugInfoQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorExportDrugInfoQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorExportDrugInfoQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiPdfCreatorExportDrugInfoQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorExportDrugInfoQuery,
    GetApiPdfCreatorExportDrugInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorExportDrugInfoQuery,
    GetApiPdfCreatorExportDrugInfoQueryVariables
  >(GetApiPdfCreatorExportDrugInfoDocument, options);
}
export function useGetApiPdfCreatorExportDrugInfoLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorExportDrugInfoQuery,
    GetApiPdfCreatorExportDrugInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorExportDrugInfoQuery,
    GetApiPdfCreatorExportDrugInfoQueryVariables
  >(GetApiPdfCreatorExportDrugInfoDocument, options);
}
export function useGetApiPdfCreatorExportDrugInfoSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorExportDrugInfoQuery,
    GetApiPdfCreatorExportDrugInfoQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorExportDrugInfoQuery,
    GetApiPdfCreatorExportDrugInfoQueryVariables
  >(GetApiPdfCreatorExportDrugInfoDocument, options);
}
export type GetApiPdfCreatorExportDrugInfoQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportDrugInfoQuery
>;
export type GetApiPdfCreatorExportDrugInfoLazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportDrugInfoLazyQuery
>;
export type GetApiPdfCreatorExportDrugInfoSuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportDrugInfoSuspenseQuery
>;
export type GetApiPdfCreatorExportDrugInfoQueryResult = Apollo.QueryResult<
  GetApiPdfCreatorExportDrugInfoQuery,
  GetApiPdfCreatorExportDrugInfoQueryVariables
>;
export const GetApiPdfCreatorExportSijisenDocument = gql`
  query getApiPdfCreatorExportSijisen(
    $formType: Int
    $odrKouiKbns: [EmrCloudApiRequestsExportPdFLimitModelInput]
    $printNoOdr: Boolean
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
  ) {
    getApiPdfCreatorExportSijisen(
      formType: $formType
      odrKouiKbns: $odrKouiKbns
      printNoOdr: $printNoOdr
      raiinNo: $raiinNo
      ptId: $ptId
      sinDate: $sinDate
    ) {
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorExportSijisenQuery__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorExportSijisenQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorExportSijisenQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorExportSijisenQuery({
 *   variables: {
 *      formType: // value for 'formType'
 *      odrKouiKbns: // value for 'odrKouiKbns'
 *      printNoOdr: // value for 'printNoOdr'
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiPdfCreatorExportSijisenQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorExportSijisenQuery,
    GetApiPdfCreatorExportSijisenQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorExportSijisenQuery,
    GetApiPdfCreatorExportSijisenQueryVariables
  >(GetApiPdfCreatorExportSijisenDocument, options);
}
export function useGetApiPdfCreatorExportSijisenLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorExportSijisenQuery,
    GetApiPdfCreatorExportSijisenQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorExportSijisenQuery,
    GetApiPdfCreatorExportSijisenQueryVariables
  >(GetApiPdfCreatorExportSijisenDocument, options);
}
export function useGetApiPdfCreatorExportSijisenSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorExportSijisenQuery,
    GetApiPdfCreatorExportSijisenQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorExportSijisenQuery,
    GetApiPdfCreatorExportSijisenQueryVariables
  >(GetApiPdfCreatorExportSijisenDocument, options);
}
export type GetApiPdfCreatorExportSijisenQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportSijisenQuery
>;
export type GetApiPdfCreatorExportSijisenLazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportSijisenLazyQuery
>;
export type GetApiPdfCreatorExportSijisenSuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorExportSijisenSuspenseQuery
>;
export type GetApiPdfCreatorExportSijisenQueryResult = Apollo.QueryResult<
  GetApiPdfCreatorExportSijisenQuery,
  GetApiPdfCreatorExportSijisenQueryVariables
>;
export const PostApiPdfCreatorExportSijisenDocument = gql`
  mutation postApiPdfCreatorExportSijisen(
    $emrCloudApiRequestsExportPdFSijisenExportRequestInput: EmrCloudApiRequestsExportPdFSijisenExportRequestInput
  ) {
    postApiPdfCreatorExportSijisen(
      emrCloudApiRequestsExportPdFSijisenExportRequestInput: $emrCloudApiRequestsExportPdFSijisenExportRequestInput
    ) {
      data {
        base64Pdfs
        fileUrl
        message
        status
      }
    }
  }
`;
export type PostApiPdfCreatorExportSijisenMutationFn = Apollo.MutationFunction<
  PostApiPdfCreatorExportSijisenMutation,
  PostApiPdfCreatorExportSijisenMutationVariables
>;

/**
 * __usePostApiPdfCreatorExportSijisenMutation__
 *
 * To run a mutation, you first call `usePostApiPdfCreatorExportSijisenMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPdfCreatorExportSijisenMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPdfCreatorExportSijisenMutation, { data, loading, error }] = usePostApiPdfCreatorExportSijisenMutation({
 *   variables: {
 *      emrCloudApiRequestsExportPdFSijisenExportRequestInput: // value for 'emrCloudApiRequestsExportPdFSijisenExportRequestInput'
 *   },
 * });
 */
export function usePostApiPdfCreatorExportSijisenMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPdfCreatorExportSijisenMutation,
    PostApiPdfCreatorExportSijisenMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPdfCreatorExportSijisenMutation,
    PostApiPdfCreatorExportSijisenMutationVariables
  >(PostApiPdfCreatorExportSijisenDocument, options);
}
export type PostApiPdfCreatorExportSijisenMutationHookResult = ReturnType<
  typeof usePostApiPdfCreatorExportSijisenMutation
>;
export type PostApiPdfCreatorExportSijisenMutationResult =
  Apollo.MutationResult<PostApiPdfCreatorExportSijisenMutation>;
export type PostApiPdfCreatorExportSijisenMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPdfCreatorExportSijisenMutation,
    PostApiPdfCreatorExportSijisenMutationVariables
  >;
export const GetApiPdfCreatorExportKarte1PrintSettingDocument = gql`
  query getApiPdfCreatorExportKarte1PrintSetting(
    $hokenPid: Int
    $ptId: BigInt
    $sinDate: Int
    $syuByomei: Boolean
    $tenkiByomei: Boolean
  ) {
    getApiPdfCreatorExportKarte1(
      hokenPid: $hokenPid
      ptId: $ptId
      sinDate: $sinDate
      syuByomei: $syuByomei
      tenkiByomei: $tenkiByomei
    ) {
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorExportKarte1PrintSettingQuery__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorExportKarte1PrintSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorExportKarte1PrintSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorExportKarte1PrintSettingQuery({
 *   variables: {
 *      hokenPid: // value for 'hokenPid'
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      syuByomei: // value for 'syuByomei'
 *      tenkiByomei: // value for 'tenkiByomei'
 *   },
 * });
 */
export function useGetApiPdfCreatorExportKarte1PrintSettingQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorExportKarte1PrintSettingQuery,
    GetApiPdfCreatorExportKarte1PrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorExportKarte1PrintSettingQuery,
    GetApiPdfCreatorExportKarte1PrintSettingQueryVariables
  >(GetApiPdfCreatorExportKarte1PrintSettingDocument, options);
}
export function useGetApiPdfCreatorExportKarte1PrintSettingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorExportKarte1PrintSettingQuery,
    GetApiPdfCreatorExportKarte1PrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorExportKarte1PrintSettingQuery,
    GetApiPdfCreatorExportKarte1PrintSettingQueryVariables
  >(GetApiPdfCreatorExportKarte1PrintSettingDocument, options);
}
export function useGetApiPdfCreatorExportKarte1PrintSettingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorExportKarte1PrintSettingQuery,
    GetApiPdfCreatorExportKarte1PrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorExportKarte1PrintSettingQuery,
    GetApiPdfCreatorExportKarte1PrintSettingQueryVariables
  >(GetApiPdfCreatorExportKarte1PrintSettingDocument, options);
}
export type GetApiPdfCreatorExportKarte1PrintSettingQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorExportKarte1PrintSettingQuery>;
export type GetApiPdfCreatorExportKarte1PrintSettingLazyQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorExportKarte1PrintSettingLazyQuery>;
export type GetApiPdfCreatorExportKarte1PrintSettingSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorExportKarte1PrintSettingSuspenseQuery>;
export type GetApiPdfCreatorExportKarte1PrintSettingQueryResult =
  Apollo.QueryResult<
    GetApiPdfCreatorExportKarte1PrintSettingQuery,
    GetApiPdfCreatorExportKarte1PrintSettingQueryVariables
  >;
export const GetApiPdfCreatorOutDrugDocument = gql`
  query getApiPdfCreatorOutDrug(
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
    $hokenGp: Int
    $epsPrintType: Int
    $delayPrint: Boolean
    $windowType: WindowType
    $registrationChecked: Boolean
    $isPrescriptionCanCancel: Boolean
    $printEpsReference: Int
    $issueType: Int
  ) {
    getApiPdfCreatorOutDrug(
      raiinNo: $raiinNo
      ptId: $ptId
      sinDate: $sinDate
      hokenGp: $hokenGp
      epsPrintType: $epsPrintType
      delayPrint: $delayPrint
      windowType: $windowType
      registrationChecked: $registrationChecked
      isPrescriptionCanCancel: $isPrescriptionCanCancel
      printEpsReference: $printEpsReference
      issueType: $issueType
    ) {
      base64Pdfs
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorOutDrugQuery__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorOutDrugQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorOutDrugQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorOutDrugQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *      hokenGp: // value for 'hokenGp'
 *      epsPrintType: // value for 'epsPrintType'
 *      delayPrint: // value for 'delayPrint'
 *      windowType: // value for 'windowType'
 *      registrationChecked: // value for 'registrationChecked'
 *      isPrescriptionCanCancel: // value for 'isPrescriptionCanCancel'
 *      printEpsReference: // value for 'printEpsReference'
 *      issueType: // value for 'issueType'
 *   },
 * });
 */
export function useGetApiPdfCreatorOutDrugQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorOutDrugQuery,
    GetApiPdfCreatorOutDrugQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorOutDrugQuery,
    GetApiPdfCreatorOutDrugQueryVariables
  >(GetApiPdfCreatorOutDrugDocument, options);
}
export function useGetApiPdfCreatorOutDrugLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorOutDrugQuery,
    GetApiPdfCreatorOutDrugQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorOutDrugQuery,
    GetApiPdfCreatorOutDrugQueryVariables
  >(GetApiPdfCreatorOutDrugDocument, options);
}
export function useGetApiPdfCreatorOutDrugSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorOutDrugQuery,
    GetApiPdfCreatorOutDrugQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorOutDrugQuery,
    GetApiPdfCreatorOutDrugQueryVariables
  >(GetApiPdfCreatorOutDrugDocument, options);
}
export type GetApiPdfCreatorOutDrugQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorOutDrugQuery
>;
export type GetApiPdfCreatorOutDrugLazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorOutDrugLazyQuery
>;
export type GetApiPdfCreatorOutDrugSuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorOutDrugSuspenseQuery
>;
export type GetApiPdfCreatorOutDrugQueryResult = Apollo.QueryResult<
  GetApiPdfCreatorOutDrugQuery,
  GetApiPdfCreatorOutDrugQueryVariables
>;
export const GetApiPdfCreatorInDrugDocument = gql`
  query getApiPdfCreatorInDrug($ptId: BigInt, $sinDate: Int, $raiinNo: BigInt) {
    getApiPdfCreatorInDrug(raiinNo: $raiinNo, ptId: $ptId, sinDate: $sinDate) {
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorInDrugQuery__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorInDrugQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorInDrugQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorInDrugQuery({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *   },
 * });
 */
export function useGetApiPdfCreatorInDrugQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorInDrugQuery,
    GetApiPdfCreatorInDrugQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorInDrugQuery,
    GetApiPdfCreatorInDrugQueryVariables
  >(GetApiPdfCreatorInDrugDocument, options);
}
export function useGetApiPdfCreatorInDrugLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorInDrugQuery,
    GetApiPdfCreatorInDrugQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorInDrugQuery,
    GetApiPdfCreatorInDrugQueryVariables
  >(GetApiPdfCreatorInDrugDocument, options);
}
export function useGetApiPdfCreatorInDrugSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorInDrugQuery,
    GetApiPdfCreatorInDrugQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorInDrugQuery,
    GetApiPdfCreatorInDrugQueryVariables
  >(GetApiPdfCreatorInDrugDocument, options);
}
export type GetApiPdfCreatorInDrugQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorInDrugQuery
>;
export type GetApiPdfCreatorInDrugLazyQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorInDrugLazyQuery
>;
export type GetApiPdfCreatorInDrugSuspenseQueryHookResult = ReturnType<
  typeof useGetApiPdfCreatorInDrugSuspenseQuery
>;
export type GetApiPdfCreatorInDrugQueryResult = Apollo.QueryResult<
  GetApiPdfCreatorInDrugQuery,
  GetApiPdfCreatorInDrugQueryVariables
>;
export const GetApiPdfCreatorExportKarte2PrintSettingDocument = gql`
  query getApiPdfCreatorExportKarte2PrintSetting(
    $deletedOdrVisibilitySetting: Int
    $emptyMode: Boolean
    $endDate: Int
    $hpId: Int
    $isCheckedApproved: Boolean
    $isCheckedDoctor: Boolean
    $isCheckedEndTime: Boolean
    $isCheckedHideOrder: Boolean
    $isCheckedHoken: Boolean
    $isCheckedHokenJibai: Boolean
    $isCheckedHokenJihi: Boolean
    $isCheckedHokenRousai: Boolean
    $isCheckedInputDate: Boolean
    $isCheckedJihi: Boolean
    $isCheckedJihiRece: Boolean
    $isCheckedSetName: Boolean
    $isCheckedStartTime: Boolean
    $isCheckedSyosai: Boolean
    $isCheckedVisitingTime: Boolean
    $isIncludeTempSave: Boolean
    $isIppanNameChecked: Boolean
    $isUketsukeNameChecked: Boolean
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
    $startDate: Int
    $includeDraft: Boolean
    $isGetVersionData: Boolean
  ) {
    getApiPdfCreatorExportKarte2(
      sinDate: $sinDate
      raiinNo: $raiinNo
      ptId: $ptId
      deletedOdrVisibilitySetting: $deletedOdrVisibilitySetting
      emptyMode: $emptyMode
      endDate: $endDate
      hpId: $hpId
      isCheckedApproved: $isCheckedApproved
      isCheckedDoctor: $isCheckedDoctor
      isCheckedEndTime: $isCheckedEndTime
      isCheckedHideOrder: $isCheckedHideOrder
      isCheckedHoken: $isCheckedHoken
      isCheckedHokenJibai: $isCheckedHokenJibai
      isCheckedHokenJihi: $isCheckedHokenJihi
      isCheckedHokenRousai: $isCheckedHokenRousai
      isCheckedInputDate: $isCheckedInputDate
      isCheckedJihi: $isCheckedJihi
      isCheckedJihiRece: $isCheckedJihiRece
      isCheckedSetName: $isCheckedSetName
      isCheckedStartTime: $isCheckedStartTime
      isCheckedSyosai: $isCheckedSyosai
      isCheckedVisitingTime: $isCheckedVisitingTime
      isIncludeTempSave: $isIncludeTempSave
      isIppanNameChecked: $isIppanNameChecked
      isUketsukeNameChecked: $isUketsukeNameChecked
      startDate: $startDate
      includeDraft: $includeDraft
      isGetVersionData: $isGetVersionData
    ) {
      fileUrl
      message
      status
    }
  }
`;

/**
 * __useGetApiPdfCreatorExportKarte2PrintSettingQuery__
 *
 * To run a query within a React component, call `useGetApiPdfCreatorExportKarte2PrintSettingQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetApiPdfCreatorExportKarte2PrintSettingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetApiPdfCreatorExportKarte2PrintSettingQuery({
 *   variables: {
 *      deletedOdrVisibilitySetting: // value for 'deletedOdrVisibilitySetting'
 *      emptyMode: // value for 'emptyMode'
 *      endDate: // value for 'endDate'
 *      hpId: // value for 'hpId'
 *      isCheckedApproved: // value for 'isCheckedApproved'
 *      isCheckedDoctor: // value for 'isCheckedDoctor'
 *      isCheckedEndTime: // value for 'isCheckedEndTime'
 *      isCheckedHideOrder: // value for 'isCheckedHideOrder'
 *      isCheckedHoken: // value for 'isCheckedHoken'
 *      isCheckedHokenJibai: // value for 'isCheckedHokenJibai'
 *      isCheckedHokenJihi: // value for 'isCheckedHokenJihi'
 *      isCheckedHokenRousai: // value for 'isCheckedHokenRousai'
 *      isCheckedInputDate: // value for 'isCheckedInputDate'
 *      isCheckedJihi: // value for 'isCheckedJihi'
 *      isCheckedJihiRece: // value for 'isCheckedJihiRece'
 *      isCheckedSetName: // value for 'isCheckedSetName'
 *      isCheckedStartTime: // value for 'isCheckedStartTime'
 *      isCheckedSyosai: // value for 'isCheckedSyosai'
 *      isCheckedVisitingTime: // value for 'isCheckedVisitingTime'
 *      isIncludeTempSave: // value for 'isIncludeTempSave'
 *      isIppanNameChecked: // value for 'isIppanNameChecked'
 *      isUketsukeNameChecked: // value for 'isUketsukeNameChecked'
 *      ptId: // value for 'ptId'
 *      raiinNo: // value for 'raiinNo'
 *      sinDate: // value for 'sinDate'
 *      startDate: // value for 'startDate'
 *      includeDraft: // value for 'includeDraft'
 *      isGetVersionData: // value for 'isGetVersionData'
 *   },
 * });
 */
export function useGetApiPdfCreatorExportKarte2PrintSettingQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetApiPdfCreatorExportKarte2PrintSettingQuery,
    GetApiPdfCreatorExportKarte2PrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetApiPdfCreatorExportKarte2PrintSettingQuery,
    GetApiPdfCreatorExportKarte2PrintSettingQueryVariables
  >(GetApiPdfCreatorExportKarte2PrintSettingDocument, options);
}
export function useGetApiPdfCreatorExportKarte2PrintSettingLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetApiPdfCreatorExportKarte2PrintSettingQuery,
    GetApiPdfCreatorExportKarte2PrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetApiPdfCreatorExportKarte2PrintSettingQuery,
    GetApiPdfCreatorExportKarte2PrintSettingQueryVariables
  >(GetApiPdfCreatorExportKarte2PrintSettingDocument, options);
}
export function useGetApiPdfCreatorExportKarte2PrintSettingSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetApiPdfCreatorExportKarte2PrintSettingQuery,
    GetApiPdfCreatorExportKarte2PrintSettingQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetApiPdfCreatorExportKarte2PrintSettingQuery,
    GetApiPdfCreatorExportKarte2PrintSettingQueryVariables
  >(GetApiPdfCreatorExportKarte2PrintSettingDocument, options);
}
export type GetApiPdfCreatorExportKarte2PrintSettingQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorExportKarte2PrintSettingQuery>;
export type GetApiPdfCreatorExportKarte2PrintSettingLazyQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorExportKarte2PrintSettingLazyQuery>;
export type GetApiPdfCreatorExportKarte2PrintSettingSuspenseQueryHookResult =
  ReturnType<typeof useGetApiPdfCreatorExportKarte2PrintSettingSuspenseQuery>;
export type GetApiPdfCreatorExportKarte2PrintSettingQueryResult =
  Apollo.QueryResult<
    GetApiPdfCreatorExportKarte2PrintSettingQuery,
    GetApiPdfCreatorExportKarte2PrintSettingQueryVariables
  >;
export const PostApiPdfCreatorOutDrugDocument = gql`
  mutation postApiPdfCreatorOutDrug(
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
    $hokenGp: Int
    $epsPrintType: Int
    $delayPrint: Boolean
    $windowType: WindowType
    $registrationChecked: Boolean
    $isPrescriptionCanCancel: Boolean
    $printEpsReference: Int
    $issueType: Int
  ) {
    postApiPdfCreatorOutDrug(
      raiinNo: $raiinNo
      ptId: $ptId
      sinDate: $sinDate
      hokenGp: $hokenGp
      epsPrintType: $epsPrintType
      delayPrint: $delayPrint
      windowType: $windowType
      registrationChecked: $registrationChecked
      isPrescriptionCanCancel: $isPrescriptionCanCancel
      printEpsReference: $printEpsReference
      issueType: $issueType
    ) {
      base64Pdfs
      fileUrl
      message
      status
    }
  }
`;
export type PostApiPdfCreatorOutDrugMutationFn = Apollo.MutationFunction<
  PostApiPdfCreatorOutDrugMutation,
  PostApiPdfCreatorOutDrugMutationVariables
>;

/**
 * __usePostApiPdfCreatorOutDrugMutation__
 *
 * To run a mutation, you first call `usePostApiPdfCreatorOutDrugMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiPdfCreatorOutDrugMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiPdfCreatorOutDrugMutation, { data, loading, error }] = usePostApiPdfCreatorOutDrugMutation({
 *   variables: {
 *      ptId: // value for 'ptId'
 *      sinDate: // value for 'sinDate'
 *      raiinNo: // value for 'raiinNo'
 *      hokenGp: // value for 'hokenGp'
 *      epsPrintType: // value for 'epsPrintType'
 *      delayPrint: // value for 'delayPrint'
 *      windowType: // value for 'windowType'
 *      registrationChecked: // value for 'registrationChecked'
 *      isPrescriptionCanCancel: // value for 'isPrescriptionCanCancel'
 *      printEpsReference: // value for 'printEpsReference'
 *      issueType: // value for 'issueType'
 *   },
 * });
 */
export function usePostApiPdfCreatorOutDrugMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiPdfCreatorOutDrugMutation,
    PostApiPdfCreatorOutDrugMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiPdfCreatorOutDrugMutation,
    PostApiPdfCreatorOutDrugMutationVariables
  >(PostApiPdfCreatorOutDrugDocument, options);
}
export type PostApiPdfCreatorOutDrugMutationHookResult = ReturnType<
  typeof usePostApiPdfCreatorOutDrugMutation
>;
export type PostApiPdfCreatorOutDrugMutationResult =
  Apollo.MutationResult<PostApiPdfCreatorOutDrugMutation>;
export type PostApiPdfCreatorOutDrugMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiPdfCreatorOutDrugMutation,
    PostApiPdfCreatorOutDrugMutationVariables
  >;
