import { useCallback, useRef, useState } from "react";

import dayjs from "dayjs";
import { notification } from "antd";

import { System } from "@/utils/socket-helper";
import { createXmlFileResult02 } from "@/utils/socket-helper/onlineQualification";
import { SystemHub, SystemScreenCode } from "@/constants/confirm-online";
import { RenderIf } from "@/utils/common/render-if";
import { handleConvertType } from "@/utils/common-helper";
import {
  usePostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation,
  usePostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation,
  usePostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation,
  usePostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation,
  usePostApiOnlineProcessXmlOqSmuhvq02resMutation,
  usePostApiOnlineProcessXmlOqSmuonq02resMutation,
  usePostApiOnlineProcessXmlOqSmuquc02resMutation,
  usePostApiOnlineProcessXmlOqSmutic02resMutation,
} from "@/apis/gql/operations/__generated__/batch-online";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { STORAGE_KEYS } from "@/constants/local-storage";
import { getLocalStorage } from "@/utils/local-storage";
import { useCheckConctionAgent } from "@/hooks/useCheckConctionAgent";

import { BatchOnlineErrorModal } from "../../../ui/modals/BatchOnlineCheck/BatchOnlineErrorModal";
import { BatchOnlineLoadingModal } from "../../../ui/modals/BatchOnlineCheck/BatchOnlineLoadingModal";

type ValidateState = {
  type: "success" | "error";
  content: {
    messageTitle: string;
    messageContent: string;
    messageContent2?: string;
  };
};

export const useResultInquiry = (
  refetchApiGetBatchOnlineCheckQuery: () => void,
) => {
  const [isLoading, setLoading] = useState<boolean>(false);
  const waitingOQController = useRef<AbortController>();
  const [openValidateModal, setOpenValidateModal] = useState<ValidateState>();
  const { handleError } = useErrorHandler();
  const initialSinDate = getLocalStorage<string>(
    STORAGE_KEYS.INIT_RECEPTION_SIN_DATE,
  );
  const { checkConnection } = useCheckConctionAgent();

  const [convertXmlToOqSmuquc02XmlMsg] =
    usePostApiOnlineConvertXmlToOqSmuquc02XmlMsgMutation({
      onError: (error) => handleError({ error }),
    });

  const [convertXmlToOqSmutic02XmlMsg] =
    usePostApiOnlineConvertXmlToOqSmutic02XmlMsgMutation({
      onError: (error) => handleError({ error }),
    });

  const [convertXmlToOqSmuhvq02XmlMsg] =
    usePostApiOnlineConvertXmlToOqSmuhvq02XmlMsgMutation({
      onError: (error) => handleError({ error }),
    });

  const [convertXmlToOqSmuonq02XmlMsg] =
    usePostApiOnlineConvertXmlToOqSmuonq02XmlMsgMutation({
      onError: (error) => handleError({ error }),
    });

  const [postApiOnlineProcessXMLOQSmuquc02res] =
    usePostApiOnlineProcessXmlOqSmuquc02resMutation({
      onError: (error) => handleError({ error }),
    });

  const [postApiOnlineProcessXMLOQSmutic02res] =
    usePostApiOnlineProcessXmlOqSmutic02resMutation({
      onError: (error) => handleError({ error }),
    });

  const [postApiOnlineProcessXMLOQSmuhvq02res] =
    usePostApiOnlineProcessXmlOqSmuhvq02resMutation({
      onError: (error) => handleError({ error }),
    });

  const [postApiOnlineProcessXMLOQSmuonq02res] =
    usePostApiOnlineProcessXmlOqSmuonq02resMutation({
      onError: (error) => handleError({ error }),
    });

  const handleResponseFile = async (
    xmlString: string,
    requestType: number,
    receptionNo: string,
  ) => {
    switch (requestType) {
      case 1:
        convertXmlToOqSmuquc02XmlMsg({
          variables: {
            payload: {
              xmlString,
            },
          },
          onCompleted: (data) => {
            const dataResponse =
              data.postApiOnlineConvertXmlToOQSmuquc02XmlMsg?.data
                ?.oqSmuquc02Response?.messageHeader;
            if (!dataResponse?.segmentOfResult) return;
            postApiOnlineProcessXMLOQSmuquc02res({
              variables: {
                payload: {
                  receptionNo,
                  sinDate: initialSinDate
                    ? Number(initialSinDate)
                    : Number(dayjs().format("YYYYMMDD")),
                  xmlString,
                },
              },
              onCompleted: (dataSave) => {
                if (Number(dataResponse?.segmentOfResult) === 9) {
                  handleShowModalErrorWithMessageAndCode(
                    dataResponse?.errorCode,
                    dataResponse?.errorMessage,
                  );
                } else {
                  if (
                    dataSave.postApiOnlineProcessXmlOQSmuquc02res?.status === 0
                  ) {
                    refetchApiGetBatchOnlineCheckQuery();
                  } else {
                    notification.error({
                      message:
                        dataSave.postApiOnlineProcessXmlOQSmuquc02res?.message,
                    });
                  }
                }
              },
            });
          },
        });
        break;
      case 2:
        convertXmlToOqSmutic02XmlMsg({
          variables: {
            payload: {
              xmlString,
            },
          },
          onCompleted: (data) => {
            const dataResponse =
              data.postApiOnlineConvertXmlToOQSmutic02XmlMsg?.data
                ?.oqSmutic02Response?.messageHeader;
            if (!dataResponse?.segmentOfResult) return;
            postApiOnlineProcessXMLOQSmutic02res({
              variables: {
                payload: {
                  receptionNo,
                  sinDate: initialSinDate
                    ? Number(initialSinDate)
                    : Number(dayjs().format("YYYYMMDD")),
                  xmlString,
                },
              },
              onCompleted: (dataSave) => {
                if (Number(dataResponse?.segmentOfResult) === 9) {
                  handleShowModalErrorWithMessageAndCode(
                    dataResponse?.errorCode,
                    dataResponse?.errorMessage,
                  );
                } else {
                  if (
                    dataSave.postApiOnlineProcessXmlOQSmutic02res?.status === 0
                  ) {
                    refetchApiGetBatchOnlineCheckQuery();
                  } else {
                    notification.error({
                      message:
                        dataSave.postApiOnlineProcessXmlOQSmutic02res?.message,
                    });
                  }
                }
              },
            });
          },
        });
        break;
      case 3:
        convertXmlToOqSmuhvq02XmlMsg({
          variables: {
            payload: {
              xmlString,
            },
          },
          onCompleted: (data) => {
            const dataResponse =
              data.postApiOnlineConvertXmlToOQSmuhvq02XmlMsg?.data
                ?.oqSmuhvq02Response?.messageHeader;
            if (!dataResponse?.segmentOfResult) return;
            postApiOnlineProcessXMLOQSmuhvq02res({
              variables: {
                payload: {
                  receptionNo,
                  sinDate: initialSinDate
                    ? Number(initialSinDate)
                    : Number(dayjs().format("YYYYMMDD")),
                  xmlString,
                },
              },
              onCompleted: (dataSave) => {
                if (Number(dataResponse?.segmentOfResult) === 9) {
                  handleShowModalErrorWithMessageAndCode(
                    dataResponse?.errorCode,
                    dataResponse?.errorMessage,
                  );
                } else {
                  if (
                    dataSave.postApiOnlineProcessXmlOQSmuhvq02res?.status === 0
                  ) {
                    refetchApiGetBatchOnlineCheckQuery();
                  } else {
                    notification.error({
                      message:
                        dataSave.postApiOnlineProcessXmlOQSmuhvq02res?.message,
                    });
                  }
                }
              },
            });
          },
        });
        break;
      case 4:
        convertXmlToOqSmuonq02XmlMsg({
          variables: {
            payload: {
              xmlString,
            },
          },
          onCompleted: (data) => {
            const dataResponse =
              data.postApiOnlineConvertXmlToOQSmuonq02XmlMsg?.data
                ?.oqSmuonq02Response?.messageHeader;
            if (!dataResponse?.segmentOfResult) return;
            postApiOnlineProcessXMLOQSmuonq02res({
              variables: {
                payload: {
                  receptionNo,
                  sinDate: initialSinDate
                    ? Number(initialSinDate)
                    : Number(dayjs().format("YYYYMMDD")),
                  xmlString,
                },
              },
              onCompleted: (dataSave) => {
                if (Number(dataResponse?.segmentOfResult) === 9) {
                  handleShowModalErrorWithMessageAndCode(
                    dataResponse?.errorCode,
                    dataResponse?.errorMessage,
                  );
                } else {
                  if (
                    dataSave.postApiOnlineProcessXmlOQSmuonq02res?.status === 0
                  ) {
                    refetchApiGetBatchOnlineCheckQuery();
                  } else {
                    notification.error({
                      message:
                        dataSave.postApiOnlineProcessXmlOQSmuonq02res?.message,
                    });
                  }
                }
              },
            });
          },
        });
        break;
      default:
        break;
    }
  };

  const handleShowModalErrorWithMessageAndCode = (
    errorCode?: string,
    errorMessage?: string,
  ) => {
    setOpenValidateModal({
      type: "error",
      content: {
        messageTitle: "資格確認結果の取得に失敗しました",
        messageContent: `${errorCode && "処理結果コード:"} ${errorCode}`,
        messageContent2: `${errorMessage}`,
      },
    });
  };

  /// lay hook
  const handleSendFileToAgentFile2 = async (
    receptionNo: string,
    requestType: number,
  ) => {
    try {
      const isConnected = await checkConnection();
      if (!isConnected) {
        return;
      }

      setLoading(true);
      waitingOQController.current?.abort();
      waitingOQController.current = new AbortController();

      const { fileName, msg } = createXmlFileResult02(receptionNo, requestType);

      const res = await onlineVisiting.createFile(
        msg.XmlMsg,
        fileName,
        { signal: waitingOQController.current?.signal },
        "CreateXmlFile",
      );

      if (!res || waitingOQController.current.signal.aborted) return;
      await onlineVisiting.moveFile({ files: [res.data.fileName] });
      if (res.data.fileName.includes(handleConvertType(requestType))) {
        const requestFileName = res.data.fileName.replace("err", "xml");
        await onlineVisiting.moveFile({ files: [requestFileName] });
        const match = res.data.content.match(/\[(.*?)\] (.*)/);
        if (match) {
          const processingResultCode = match[1];
          const errorMessage = match[2];
          setOpenValidateModal({
            type: "error",
            content: {
              messageTitle: "オンライン資格確認に失敗しました",
              messageContent: `処理結果コード: ${processingResultCode}`,
              messageContent2: `${errorMessage}`,
            },
          });
        }
      }

      if (res.data?.content)
        handleResponseFile(res.data.content, requestType, receptionNo);
    } catch (err) {
      if (err instanceof Error) {
        setOpenValidateModal({
          type: "error",
          content: {
            messageTitle: "オンライン資格確認に失敗しました",
            messageContent: "タイムアウト",
          },
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const ValidateCancelRequestComponentFile2 = useCallback(() => {
    return (
      <>
        <RenderIf condition={openValidateModal?.type === "error"}>
          <BatchOnlineErrorModal
            onClose={() => {
              setOpenValidateModal(undefined);
              refetchApiGetBatchOnlineCheckQuery();
            }}
            content={openValidateModal?.content}
          />
        </RenderIf>
        <RenderIf condition={isLoading}>
          <BatchOnlineLoadingModal
            content={{
              title: "処理中",
              heading: "オンライン資格確認システムからの結果を待っています",
            }}
            onClose={() => {
              setLoading(false);
              waitingOQController.current?.abort();
            }}
          />
        </RenderIf>
      </>
    );
  }, [isLoading, openValidateModal?.content, openValidateModal?.type]);

  return {
    isLoading,
    handleSendFileToAgentFile2,
    ValidateCancelRequestComponentFile2,
  };
};

class OnlineVisiting extends System {
  constructor() {
    super(SystemHub.PatientInf, [{}], {
      screenCode: SystemScreenCode.PatientInfo,
    });
  }
}

const onlineVisiting = new OnlineVisiting();
