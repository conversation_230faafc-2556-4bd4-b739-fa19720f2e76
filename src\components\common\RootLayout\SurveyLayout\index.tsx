import React from "react";

import styled from "styled-components";

import { Meta } from "@/components/functional/Meta";

import type { ReactNode } from "react";

type SurveyLayoutProps = {
  children: ReactNode;
};

const SurveyContentWrapper = styled.div`
  background-color: #e9f0f7;
`;

const StyledMain = styled.main`
  overflow-y: auto;
`;

export const SurveyLayout: React.FC<SurveyLayoutProps> = ({ children }) => {
  return (
    <SurveyContentWrapper>
      <Meta
        title="問診票入力フォーム"
        description=""
        viewport="width=device-width,initial-scale=1,maximum-scale=1"
      />
      <StyledMain>{children}</StyledMain>
    </SurveyContentWrapper>
  );
};
