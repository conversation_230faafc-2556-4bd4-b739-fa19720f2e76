import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetReservationDetailsByConditionsQueryVariables = Types.Exact<{
  input: Types.GetReservationDetailsByConditions;
}>;

export type GetReservationDetailsByConditionsQuery = {
  __typename?: "query_root";
  getReservationDetailsByConditions: Array<{
    __typename?: "ReservationDetail";
    reserveId: number;
    reserveDetailId: number;
    status: number;
    treatmentType?: number;
    reserveType?: number;
    updatedAt: string;
    memo?: string;
    isSuspendedReservation: boolean;
    paymentStatus: number;
    isSurveyAnswered: boolean;
    examTimeSlot: {
      __typename?: "ExamTimeSlot";
      examTimeSlotID: number;
      examStartDate: string;
      examEndDate: string;
      treatmentType: number;
      slotLimitReserveNum: number;
      calendar: {
        __typename?: "Calendar";
        calendarID: number;
        label?: string;
        calendarName?: string;
        isActive: boolean;
        doctor?: {
          __typename?: "StaffInfo";
          staffId: number;
          staffName: string;
        };
      };
    };
    patient?: {
      __typename?: "Patient";
      patientName?: string;
      patientNameKana?: string;
      patientID: number;
      patientNumber?: number;
      phoneNumber1?: string;
      phoneNumber2?: string;
      birthdate?: string;
    };
    paymentDetail?: {
      __typename?: "PaymentDetail";
      paymentClinicDetailId: string;
    };
    pharmacyReserveDetail?: {
      __typename?: "PharmacyReserveDetail";
      pharmacyReserveDetailId: number;
      paymentStatus: number;
    };
    reservation: {
      __typename?: "Reservation";
      patientId?: number;
      meeting?: { __typename?: "Meeting"; meetingId: number; status: number };
    };
    calendarTreatment: {
      __typename?: "CalendarTreatment";
      calendarTreatmentID: number;
      treatmentDepartment?: {
        __typename?: "TreatmentDepartment";
        treatmentType: number;
        title: string;
        firstConsultationTime: number;
        nextConsultationTime: number;
        treatmentDepartmentStatus: number;
        isDeleted: number;
        treatmentCategory?: { __typename?: "TreatmentCategory"; name: string };
      };
    };
    reservationDetailHistories?: Array<{
      __typename?: "ReservationDetailHistory";
      reserveDetailHistoryId: number;
      status: number;
      createdAt: string;
      calendarTreatment: {
        __typename?: "CalendarTreatment";
        treatmentDepartment?: {
          __typename?: "TreatmentDepartment";
          title: string;
          treatmentType: number;
          treatmentCategory?: {
            __typename?: "TreatmentCategory";
            name: string;
          };
        };
      };
      examTimeSlot: {
        __typename?: "ExamTimeSlot";
        examTimeSlotID: number;
        examStartDate: string;
        examEndDate: string;
        treatmentType: number;
        calendar: {
          __typename?: "Calendar";
          calendarID: number;
          label?: string;
          calendarName?: string;
          doctor?: {
            __typename?: "StaffInfo";
            staffId: number;
            staffName: string;
          };
        };
      };
    }>;
    raiinInfo?: { __typename?: "RaiinInfo"; status: number };
  }>;
};

export type GetReservationDetailByIdQueryVariables = Types.Exact<{
  input: Types.GetReservationDetailByIdInput;
}>;

export type GetReservationDetailByIdQuery = {
  __typename?: "query_root";
  getReservationDetailById: {
    __typename?: "ReservationDetail";
    reserveId: number;
    reserveDetailId: number;
    isSuspendedReservation: boolean;
    treatmentType?: number;
    reserveType?: number;
    memo?: string;
    status: number;
    fincodeCustomerId?: string;
    paymentCardId?: string;
    isSurveyAnswered: boolean;
    paymentStatus: number;
    examTimeSlot: {
      __typename?: "ExamTimeSlot";
      examTimeSlotID: number;
      examStartDate: string;
      examEndDate: string;
      treatmentType: number;
      calendar: {
        __typename?: "Calendar";
        calendarID: number;
        reservationMethodType?: number;
        label?: string;
        calendarName?: string;
        doctor?: {
          __typename?: "StaffInfo";
          staffId: number;
          staffName: string;
        };
      };
    };
    reservation: {
      __typename?: "Reservation";
      patientId?: number;
      prescriptionReceiveMethod: number;
      meeting?: { __typename?: "Meeting"; meetingId: number };
      reservationDetails?: Array<{
        __typename?: "ReservationDetail";
        patient?: {
          __typename?: "Patient";
          patientID: number;
          patientName?: string;
          patientNameKana?: string;
        };
      }>;
      pharmacyReserve?: {
        __typename?: "PharmacyReserve";
        pharmacyReserveId: number;
        desiredDateStatus: number;
        pharmacyDeliveryAddress: {
          __typename?: "PharmacyDeliveryAddress";
          pharmacyDeliveryAddressId: number;
          pharmacyReserveId: number;
          address1: string;
          address2?: string;
          postCode: string;
          phoneNumber: string;
        };
      };
      portalCustomerPharmacy?: {
        __typename?: "PortalCustomerPharmacy";
        portalCustomerPharmacyId: number;
        pharmacyName: string;
        pharmacyStoreName?: string;
        address1: string;
        address2?: string;
        faxNumber: string;
        postCode: string;
        phoneNumber: string;
      };
    };
    patient?: {
      __typename?: "Patient";
      patientID: number;
      patientName?: string;
      patientNameKana?: string;
      patientNumber?: number;
      phoneNumber1?: string;
      phoneNumber2?: string;
      birthdate?: string;
      gender?: number;
    };
    calendarTreatment: {
      __typename?: "CalendarTreatment";
      calendarTreatmentID: number;
      treatmentDepartment?: {
        __typename?: "TreatmentDepartment";
        treatmentDepartmentId: number;
        treatmentType: number;
        title: string;
        firstConsultationTime: number;
        nextConsultationTime: number;
      };
    };
    paymentDetail?: {
      __typename?: "PaymentDetail";
      paymentClinicDetailId: string;
      paymentType: number;
      billingAmount: number;
      paymentStatus: number;
    };
  };
};

export type ReserveMutationVariables = Types.Exact<{
  input: Types.ReservationCreateInput;
}>;

export type ReserveMutation = {
  __typename?: "mutation_root";
  reserve: {
    __typename?: "ReservationCreateRes";
    reserveId: number;
    reserveDetailId: number;
    registerRaiinInput: {
      __typename?: "RegisterRaiinInput";
      raiinComment: string;
      receptionModel: {
        __typename?: "ReceptionModel";
        hpId: number;
        ptId: number;
        sinDate: number;
        yoyakuId: number;
        yoyakuTime: string;
        yoyakuEndTime: string;
        treatmentDepartmentId: number;
        reserveDetailId: number;
        tantoId: number;
      };
    };
  };
};

export type UpdateReservationMutationVariables = Types.Exact<{
  reserveId: Types.Scalars["Int"]["input"];
  reserveDetailId: Types.Scalars["Int"]["input"];
  calendarId: Types.Scalars["Int"]["input"];
  input: Types.ReservationUpdateInput;
  resendMessage?: Types.InputMaybe<Types.Scalars["Boolean"]["input"]>;
}>;

export type UpdateReservationMutation = {
  __typename?: "mutation_root";
  updateReservation: {
    __typename?: "ReservationUpdateRes";
    reserveId: number;
    reserveDetailId: number;
    calendarId: number;
  };
};

export type CancelReservationMutationVariables = Types.Exact<{
  input: Types.CancelReservationByIdInput;
}>;

export type CancelReservationMutation = {
  __typename?: "mutation_root";
  cancelReservation: boolean;
};

export type UpdateTreatmentStatusToCompletedMutationVariables = Types.Exact<{
  reserveDetailIds:
    | Array<Types.Scalars["Int"]["input"]>
    | Types.Scalars["Int"]["input"];
}>;

export type UpdateTreatmentStatusToCompletedMutation = {
  __typename?: "mutation_root";
  updateTreatmentStatusToCompleted: boolean;
};

export type SuspendReservationMutationVariables = Types.Exact<{
  input: Types.SuspendReservationInput;
}>;

export type SuspendReservationMutation = {
  __typename?: "mutation_root";
  suspendReservation: boolean;
};

export const GetReservationDetailsByConditionsDocument = gql`
  query getReservationDetailsByConditions(
    $input: GetReservationDetailsByConditions!
  ) {
    getReservationDetailsByConditions(input: $input) {
      reserveId
      reserveDetailId
      status
      treatmentType
      reserveType
      updatedAt
      memo
      isSuspendedReservation
      paymentStatus
      examTimeSlot {
        examTimeSlotID
        examStartDate
        examEndDate
        treatmentType
        slotLimitReserveNum
        calendar {
          calendarID
          label
          calendarName
          doctor {
            staffId
            staffName
          }
          isActive
        }
      }
      patient {
        patientName
        patientNameKana
        patientID
        patientNumber
        phoneNumber1
        phoneNumber2
        birthdate
      }
      paymentDetail {
        paymentClinicDetailId
      }
      pharmacyReserveDetail {
        pharmacyReserveDetailId
        paymentStatus
      }
      reservation {
        patientId
        meeting {
          meetingId
          status
        }
      }
      calendarTreatment {
        calendarTreatmentID
        treatmentDepartment {
          treatmentType
          title
          firstConsultationTime
          nextConsultationTime
          treatmentCategory {
            name
          }
          treatmentDepartmentStatus
          isDeleted
        }
      }
      reservationDetailHistories {
        reserveDetailHistoryId
        status
        createdAt
        calendarTreatment {
          treatmentDepartment {
            title
            treatmentType
            treatmentCategory {
              name
            }
          }
        }
        examTimeSlot {
          examTimeSlotID
          examStartDate
          examEndDate
          treatmentType
          calendar {
            calendarID
            label
            calendarName
            doctor {
              staffId
              staffName
            }
          }
        }
      }
      isSurveyAnswered
      raiinInfo {
        status
      }
    }
  }
`;

/**
 * __useGetReservationDetailsByConditionsQuery__
 *
 * To run a query within a React component, call `useGetReservationDetailsByConditionsQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetReservationDetailsByConditionsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetReservationDetailsByConditionsQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetReservationDetailsByConditionsQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetReservationDetailsByConditionsQuery,
    GetReservationDetailsByConditionsQueryVariables
  > &
    (
      | {
          variables: GetReservationDetailsByConditionsQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetReservationDetailsByConditionsQuery,
    GetReservationDetailsByConditionsQueryVariables
  >(GetReservationDetailsByConditionsDocument, options);
}
export function useGetReservationDetailsByConditionsLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetReservationDetailsByConditionsQuery,
    GetReservationDetailsByConditionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetReservationDetailsByConditionsQuery,
    GetReservationDetailsByConditionsQueryVariables
  >(GetReservationDetailsByConditionsDocument, options);
}
export function useGetReservationDetailsByConditionsSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetReservationDetailsByConditionsQuery,
    GetReservationDetailsByConditionsQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetReservationDetailsByConditionsQuery,
    GetReservationDetailsByConditionsQueryVariables
  >(GetReservationDetailsByConditionsDocument, options);
}
export type GetReservationDetailsByConditionsQueryHookResult = ReturnType<
  typeof useGetReservationDetailsByConditionsQuery
>;
export type GetReservationDetailsByConditionsLazyQueryHookResult = ReturnType<
  typeof useGetReservationDetailsByConditionsLazyQuery
>;
export type GetReservationDetailsByConditionsSuspenseQueryHookResult =
  ReturnType<typeof useGetReservationDetailsByConditionsSuspenseQuery>;
export type GetReservationDetailsByConditionsQueryResult = Apollo.QueryResult<
  GetReservationDetailsByConditionsQuery,
  GetReservationDetailsByConditionsQueryVariables
>;
export const GetReservationDetailByIdDocument = gql`
  query getReservationDetailById($input: GetReservationDetailByIdInput!) {
    getReservationDetailById(input: $input) {
      reserveId
      reserveDetailId
      isSuspendedReservation
      treatmentType
      reserveType
      memo
      status
      fincodeCustomerId
      paymentCardId
      examTimeSlot {
        examTimeSlotID
        examStartDate
        examEndDate
        treatmentType
        calendar {
          calendarID
          reservationMethodType
          label
          calendarName
          doctor {
            staffId
            staffName
          }
        }
      }
      reservation {
        meeting {
          meetingId
        }
      }
      patient {
        patientID
        patientName
        patientNameKana
        patientNumber
        phoneNumber1
        phoneNumber2
        birthdate
        gender
      }
      calendarTreatment {
        calendarTreatmentID
        treatmentDepartment {
          treatmentDepartmentId
          treatmentType
          title
          firstConsultationTime
          nextConsultationTime
        }
      }
      paymentDetail {
        paymentClinicDetailId
        paymentType
        billingAmount
        paymentStatus
      }
      reservation {
        patientId
        prescriptionReceiveMethod
        reservationDetails {
          patient {
            patientID
            patientName
            patientNameKana
          }
        }
        pharmacyReserve {
          pharmacyReserveId
          desiredDateStatus
          pharmacyDeliveryAddress {
            pharmacyDeliveryAddressId
            pharmacyReserveId
            address1
            address2
            postCode
            phoneNumber
          }
        }
        portalCustomerPharmacy {
          portalCustomerPharmacyId
          pharmacyName
          pharmacyStoreName
          address1
          address2
          faxNumber
          postCode
          phoneNumber
        }
      }
      isSurveyAnswered
      paymentStatus
    }
  }
`;

/**
 * __useGetReservationDetailByIdQuery__
 *
 * To run a query within a React component, call `useGetReservationDetailByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetReservationDetailByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetReservationDetailByIdQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetReservationDetailByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetReservationDetailByIdQuery,
    GetReservationDetailByIdQueryVariables
  > &
    (
      | { variables: GetReservationDetailByIdQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetReservationDetailByIdQuery,
    GetReservationDetailByIdQueryVariables
  >(GetReservationDetailByIdDocument, options);
}
export function useGetReservationDetailByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetReservationDetailByIdQuery,
    GetReservationDetailByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetReservationDetailByIdQuery,
    GetReservationDetailByIdQueryVariables
  >(GetReservationDetailByIdDocument, options);
}
export function useGetReservationDetailByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetReservationDetailByIdQuery,
    GetReservationDetailByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetReservationDetailByIdQuery,
    GetReservationDetailByIdQueryVariables
  >(GetReservationDetailByIdDocument, options);
}
export type GetReservationDetailByIdQueryHookResult = ReturnType<
  typeof useGetReservationDetailByIdQuery
>;
export type GetReservationDetailByIdLazyQueryHookResult = ReturnType<
  typeof useGetReservationDetailByIdLazyQuery
>;
export type GetReservationDetailByIdSuspenseQueryHookResult = ReturnType<
  typeof useGetReservationDetailByIdSuspenseQuery
>;
export type GetReservationDetailByIdQueryResult = Apollo.QueryResult<
  GetReservationDetailByIdQuery,
  GetReservationDetailByIdQueryVariables
>;
export const ReserveDocument = gql`
  mutation reserve($input: ReservationCreateInput!) {
    reserve(input: $input) {
      reserveId
      reserveDetailId
      registerRaiinInput {
        receptionModel {
          hpId
          ptId
          sinDate
          yoyakuId
          yoyakuTime
          yoyakuEndTime
          treatmentDepartmentId
          reserveDetailId
          tantoId
        }
        raiinComment
      }
    }
  }
`;
export type ReserveMutationFn = Apollo.MutationFunction<
  ReserveMutation,
  ReserveMutationVariables
>;

/**
 * __useReserveMutation__
 *
 * To run a mutation, you first call `useReserveMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useReserveMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [reserveMutation, { data, loading, error }] = useReserveMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useReserveMutation(
  baseOptions?: Apollo.MutationHookOptions<
    ReserveMutation,
    ReserveMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<ReserveMutation, ReserveMutationVariables>(
    ReserveDocument,
    options,
  );
}
export type ReserveMutationHookResult = ReturnType<typeof useReserveMutation>;
export type ReserveMutationResult = Apollo.MutationResult<ReserveMutation>;
export type ReserveMutationOptions = Apollo.BaseMutationOptions<
  ReserveMutation,
  ReserveMutationVariables
>;
export const UpdateReservationDocument = gql`
  mutation updateReservation(
    $reserveId: Int!
    $reserveDetailId: Int!
    $calendarId: Int!
    $input: ReservationUpdateInput!
    $resendMessage: Boolean
  ) {
    updateReservation(
      calendarId: $calendarId
      reserveId: $reserveId
      reserveDetailId: $reserveDetailId
      input: $input
      resendMessage: $resendMessage
    ) {
      reserveId
      reserveDetailId
      calendarId
    }
  }
`;
export type UpdateReservationMutationFn = Apollo.MutationFunction<
  UpdateReservationMutation,
  UpdateReservationMutationVariables
>;

/**
 * __useUpdateReservationMutation__
 *
 * To run a mutation, you first call `useUpdateReservationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateReservationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateReservationMutation, { data, loading, error }] = useUpdateReservationMutation({
 *   variables: {
 *      reserveId: // value for 'reserveId'
 *      reserveDetailId: // value for 'reserveDetailId'
 *      calendarId: // value for 'calendarId'
 *      input: // value for 'input'
 *      resendMessage: // value for 'resendMessage'
 *   },
 * });
 */
export function useUpdateReservationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateReservationMutation,
    UpdateReservationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateReservationMutation,
    UpdateReservationMutationVariables
  >(UpdateReservationDocument, options);
}
export type UpdateReservationMutationHookResult = ReturnType<
  typeof useUpdateReservationMutation
>;
export type UpdateReservationMutationResult =
  Apollo.MutationResult<UpdateReservationMutation>;
export type UpdateReservationMutationOptions = Apollo.BaseMutationOptions<
  UpdateReservationMutation,
  UpdateReservationMutationVariables
>;
export const CancelReservationDocument = gql`
  mutation cancelReservation($input: CancelReservationByIdInput!) {
    cancelReservation(input: $input)
  }
`;
export type CancelReservationMutationFn = Apollo.MutationFunction<
  CancelReservationMutation,
  CancelReservationMutationVariables
>;

/**
 * __useCancelReservationMutation__
 *
 * To run a mutation, you first call `useCancelReservationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCancelReservationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [cancelReservationMutation, { data, loading, error }] = useCancelReservationMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCancelReservationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CancelReservationMutation,
    CancelReservationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CancelReservationMutation,
    CancelReservationMutationVariables
  >(CancelReservationDocument, options);
}
export type CancelReservationMutationHookResult = ReturnType<
  typeof useCancelReservationMutation
>;
export type CancelReservationMutationResult =
  Apollo.MutationResult<CancelReservationMutation>;
export type CancelReservationMutationOptions = Apollo.BaseMutationOptions<
  CancelReservationMutation,
  CancelReservationMutationVariables
>;
export const UpdateTreatmentStatusToCompletedDocument = gql`
  mutation updateTreatmentStatusToCompleted($reserveDetailIds: [Int!]!) {
    updateTreatmentStatusToCompleted(reserveDetailIds: $reserveDetailIds)
  }
`;
export type UpdateTreatmentStatusToCompletedMutationFn =
  Apollo.MutationFunction<
    UpdateTreatmentStatusToCompletedMutation,
    UpdateTreatmentStatusToCompletedMutationVariables
  >;

/**
 * __useUpdateTreatmentStatusToCompletedMutation__
 *
 * To run a mutation, you first call `useUpdateTreatmentStatusToCompletedMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateTreatmentStatusToCompletedMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateTreatmentStatusToCompletedMutation, { data, loading, error }] = useUpdateTreatmentStatusToCompletedMutation({
 *   variables: {
 *      reserveDetailIds: // value for 'reserveDetailIds'
 *   },
 * });
 */
export function useUpdateTreatmentStatusToCompletedMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateTreatmentStatusToCompletedMutation,
    UpdateTreatmentStatusToCompletedMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateTreatmentStatusToCompletedMutation,
    UpdateTreatmentStatusToCompletedMutationVariables
  >(UpdateTreatmentStatusToCompletedDocument, options);
}
export type UpdateTreatmentStatusToCompletedMutationHookResult = ReturnType<
  typeof useUpdateTreatmentStatusToCompletedMutation
>;
export type UpdateTreatmentStatusToCompletedMutationResult =
  Apollo.MutationResult<UpdateTreatmentStatusToCompletedMutation>;
export type UpdateTreatmentStatusToCompletedMutationOptions =
  Apollo.BaseMutationOptions<
    UpdateTreatmentStatusToCompletedMutation,
    UpdateTreatmentStatusToCompletedMutationVariables
  >;
export const SuspendReservationDocument = gql`
  mutation suspendReservation($input: SuspendReservationInput!) {
    suspendReservation(input: $input)
  }
`;
export type SuspendReservationMutationFn = Apollo.MutationFunction<
  SuspendReservationMutation,
  SuspendReservationMutationVariables
>;

/**
 * __useSuspendReservationMutation__
 *
 * To run a mutation, you first call `useSuspendReservationMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useSuspendReservationMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [suspendReservationMutation, { data, loading, error }] = useSuspendReservationMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useSuspendReservationMutation(
  baseOptions?: Apollo.MutationHookOptions<
    SuspendReservationMutation,
    SuspendReservationMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    SuspendReservationMutation,
    SuspendReservationMutationVariables
  >(SuspendReservationDocument, options);
}
export type SuspendReservationMutationHookResult = ReturnType<
  typeof useSuspendReservationMutation
>;
export type SuspendReservationMutationResult =
  Apollo.MutationResult<SuspendReservationMutation>;
export type SuspendReservationMutationOptions = Apollo.BaseMutationOptions<
  SuspendReservationMutation,
  SuspendReservationMutationVariables
>;
