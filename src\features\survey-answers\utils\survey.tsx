import type { ReactNode } from "react";
import type { CustomAnswerType } from "@/types/survey";

export type Files = {
  s3Key: string;
  fileName: string;
  downloadUrl: string;
};

export const getCustomAnswersKeyValues = (
  customAnswers: CustomAnswerType[],
): {
  key: string;
  value: string;
}[] => {
  return customAnswers.map(
    ({ questionName, questionType, answerDisplayValue, answerValue }) => {
      if (questionType === "multipletext") {
        return {
          key: questionName,
          value: (answerValue ? Object.entries(answerValue) : [])
            .map(([key, value]) => `${key}: ${value}`)
            .join(),
        };
      }

      return {
        key: questionName,
        value: answerDisplayValue,
      };
    },
  );
};

export const getCustomAnswersField = (
  customAnswers: CustomAnswerType[],
  files: Files[],
): {
  key: string;
  value: string | ReactNode;
  url?: string | undefined;
}[] => {
  return customAnswers.map(
    ({ questionName, answerDisplayValue, questionType, answerValue }) => {
      if (questionType === "file") {
        return {
          key: questionName,
          value: answerDisplayValue,
          url: files.find((file) => file.s3Key === answerValue.s3Key)
            ?.downloadUrl,
        };
      }

      if (questionType === "multipletext") {
        if (!answerValue) {
          return {
            key: questionName,
            value: "",
          };
        }
        return {
          key: questionName,
          value: (answerValue ? Object.entries(answerValue) : []).map(
            ([key, value]) => {
              return (
                <p key={key}>
                  {key}　{value}
                </p>
              );
            },
          ),
        };
      }

      return {
        key: questionName,
        value: answerDisplayValue,
      };
    },
  );
};
