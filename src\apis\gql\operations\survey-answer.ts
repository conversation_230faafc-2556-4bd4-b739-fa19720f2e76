import { gql } from "@/apis/gql/apollo-client";

export const GET_SURVEY_ANSWER_NO_PATIENTS = gql`
  query getSurveyAnswerNoPatients($answerDate: String!, $surveyId: Int) {
    getSurveyAnswerNoPatients(answerDate: $answerDate, surveyId: $surveyId) {
      birthday
      createdAt
      name
      kanaName
      surveyAnswerNoPatientId
      surveyId
      surveyName
    }
  }
`;

export const GET_SURVEY_ANSWER_NO_PATIENT_BY_ID = gql`
  query getSurveyAnswerNoPatientById($id: Int64!) {
    getSurveyAnswerNoPatientById(id: $id) {
      birthday
      createdAt
      kanaName
      name
      surveyName
      surveyAnswer
      surveyId
      surveyAnswerNoPatientId
      files {
        fileName
        downloadUrl
        s3Key
      }
    }
  }
`;
