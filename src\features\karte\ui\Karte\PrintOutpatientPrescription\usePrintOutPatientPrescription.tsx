import { useEffect, useMemo, useRef, useState } from "react";

import { useSearchParams } from "next/navigation";
import router from "next/router";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";
import { useFormContext } from "react-hook-form";

import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { System } from "@/utils/socket-helper";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { usePostApiPdfCreatorOutDrugMutation } from "@/apis/gql/operations/__generated__/print-setting";
import {
  closeWindow,
  getListHokenGrpByHokenKbn,
  logTable,
  openBase64AsPdfPrint,
  openPdfKarteFile,
} from "@/features/karte/ui/Karte/PrintOutpatientPrescription/utils";
import { useEPrescriptionContext } from "@/features/karte/ui/Karte/PrintSetting/EPrescriptionContextProvider";
import { usePostApiEpsGetOutDrugCsvDataMutation } from "@/apis/gql/operations/__generated__/duplicate-medication";
import {
  useGetApiEpsValidateBeforePrintingOutPatientLazyQuery,
  useGetApiEpsValidateBeforePrintingOutPatientQuery,
} from "@/apis/gql/operations/__generated__/print-out-patient";
import { WindowType } from "@/apis/gql/generated/types";
import { formatGetOutDrugCsv } from "@/features/karte/utils/e-prescription";
import { useUpsertEpsRegister } from "@/hooks/useUpsertEpsRegister";
import { sleep } from "@/features/karte/ui/Karte/PrescriptionInformation/utils";

import type { ApolloError } from "@apollo/client";
import type { DomainModelsEpsReqEpsReqModel } from "@/apis/gql/generated/types";
import type { ISystemRequestConfig } from "@/utils/socket-helper/socket.type";
import type {
  KarteFormData,
  OrderRp,
} from "@/features/karte/types/karte-order";

export function usePrintOutPatientPrescription() {
  const { handleOpenModal } = useModal();
  const {
    statePrintSetting: { outpatientOption, isOutpatientPrescription },
    dataCheckPrescriptionChangeOrder,
    listDrugCSV,
    flagRegister,
    isClosePageKarte,
    isOpenAccounting,
  } = useEPrescriptionContext();
  const { handleError } = useErrorHandler();
  const searchParams = useSearchParams();
  const raiinNo = searchParams.get("raiinNo") ?? "";
  const { id } = router.query;
  const sinDate = parseInt(searchParams.get("sinDate") ?? "0");
  const ptId = id?.toString() ?? "0";

  const { watch } = useFormContext<KarteFormData>();

  const orderRpsWatch = watch("orderRps");

  const [loadingHook, setLoadingHook] = useState(true);

  const [registerResult, setRegisterResult] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);

  const [postApiPdfCreatorOutDrug] = usePostApiPdfCreatorOutDrugMutation({
    onError: (error) => {
      handleError({ error });
    },
  });

  const [postApiEpsGetOutDrugCsvData] = usePostApiEpsGetOutDrugCsvDataMutation({
    onError: (error) => {
      handleError({ error }).then();
    },
  });

  const { handleUpsertEpsRegister } = useUpsertEpsRegister();

  const {
    data: responseCheckBeforePrintPrescription,
    loading: loadingCheckBefore,
  } = useGetApiEpsValidateBeforePrintingOutPatientQuery({
    variables: {
      sinDate,
      raiinNo,
      ptId,
    },
    fetchPolicy: "no-cache",
    onError: (error) => {
      handleError({ error }).then();
    },
  });

  const [responseCheckBeforePrintPrescriptionLazy] =
    useGetApiEpsValidateBeforePrintingOutPatientLazyQuery({
      variables: {
        sinDate,
        raiinNo,
        ptId,
      },
      fetchPolicy: "no-cache",
      onError: (error) => {
        handleError({ error }).then();
      },
    });

  const controllerAbort = useRef<AbortController | null>(null);
  const { data: dataSystemConf, loading: loadingSystem } =
    useGetApiSystemConfGetListQuery();

  useEffect(() => {
    if (!loadingSystem && !loadingCheckBefore) {
      setLoadingHook(false);
    }
  }, [loadingSystem, loadingCheckBefore]);

  const getDetailEpsPrescription = useMemo(() => {
    return (prescriptionId: string) =>
      responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data?.epsPrescriptions?.find(
        (item) => item?.prescriptionId === prescriptionId,
      );
  }, [
    responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data
      ?.epsPrescriptions,
  ]);

  const isHeathInsurance = useMemo(() => {
    return responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data?.ptHokenPatterns?.some(
      (item) => item.hokenKbn === 0,
    );
  }, [
    responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data
      ?.ptHokenPatterns,
  ]);

  // const isEpsPrescriptionStatus = true;

  const isEpsPrescriptionStatus = useMemo(() => {
    return (
      responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data?.epsPrescriptions?.some(
        (item) => item.status === 0,
      ) ?? false
    );
  }, [
    responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data
      ?.epsPrescriptions,
  ]);

  async function handlePrintOutPatientPrescriptionBHYT(
    epsPrintType: number,
    hokenGp: number = -1,
    delayPrint: boolean = false,
    registrationChecked: boolean,
    isPrescriptionCanCancel: boolean,
    printEpsReference: number,
    windowType: WindowType = WindowType.MedicalPrint,
    issueType: number,
  ) {
    try {
      await postApiPdfCreatorOutDrug({
        variables: {
          ptId,
          sinDate,
          raiinNo,
          issueType,
          epsPrintType,
          hokenGp,
          windowType,
          delayPrint,
          registrationChecked,
          isPrescriptionCanCancel,
          printEpsReference,
        },
        onCompleted: (data) => {
          openPdfKarteFile(data?.postApiPdfCreatorOutDrug?.fileUrl ?? "");
          data?.postApiPdfCreatorOutDrug?.base64Pdfs?.map(
            (base64String, index) => {
              openBase64AsPdfPrint(
                base64String,
                `getApiPdfCreatorOutDrug.${index}.pdf`,
              );
            },
          );
        },
        onError: (error) => {
          handleError({ error });
        },
      });
      // openPdfKarteFile(response?.data?.getApiPdfCreatorOutDrug?.fileUrl ?? "");
      // response?.data?.getApiPdfCreatorOutDrug?.base64Pdfs?.map(
      //   (base64String, index) => {
      //     openBase64AsPdfPrint(
      //       base64String,
      //       `getApiPdfCreatorOutDrug.${index}.pdf`,
      //     );
      //   },
      // );
    } catch (error) {
      handleError({ error: error as ApolloError }).then();
    }
  }

  async function handlePrintOutPatientPrescription(epsPrintType: number) {
    try {
      await postApiPdfCreatorOutDrug({
        variables: {
          ptId: ptId,
          sinDate: sinDate,
          raiinNo: raiinNo,
          epsPrintType,
          hokenGp: -1,
        },
        onCompleted: (data) => {
          openPdfKarteFile(data?.postApiPdfCreatorOutDrug?.fileUrl ?? "");
        },
        onError: (error) => {
          handleError({ error });
        },
      });
    } catch (error) {
      handleError({ error: error as ApolloError }).then();
    }
  }

  const listMappingPrescriptionConvertDrugCsvByRefillCount = useMemo(() => {
    return (
      dataCheckPrescriptionChangeOrder?.map((item) => {
        return {
          ...item,
          drugCsv: listDrugCSV.find(
            (csv) => csv.refileCount === item?.refileCount,
          )?.prescriptionDocument,
        };
      }) ?? []
    );
  }, [dataCheckPrescriptionChangeOrder, listDrugCSV]);

  const createDrugCsvData = async ({
    data,
    refileCount,
  }: {
    data: OrderRp[];
    refileCount: number;
  }) => {
    const inputData = formatGetOutDrugCsv({
      refileCount,
      data,
      ptId: String(ptId),
      raiinNo: String(raiinNo),
      sinDate: Number(sinDate),
      fileType: outpatientOption === "3" ? 2 : 1,
    });

    const prescriptionResponseCsv = await postApiEpsGetOutDrugCsvData({
      variables: {
        input: inputData,
      },
      onError: (error) => {
        handleError({ error });
      },
    });

    return prescriptionResponseCsv.data?.postApiEpsGetOutDrugCsvData?.data
      ?.prescriptionDocument;
  };

  async function handlePrintOutPatientPrescriptionPaper(
    isCloseKarte: boolean = false,
  ) {
    try {
      // debugger;
      console.log("STEP PRINT PAPER", isOutpatientPrescription, isCloseKarte);

      if (isOutpatientPrescription) {
        await handlePrintOutPatientPrescription(1);
      }

      if (isClosePageKarte) {
        await sleep(3000);
        closeWindow(isClosePageKarte);
      }

      if (isOpenAccounting) {
        handleOpenModal("PAYMENT");
      }
    } catch (e) {
      console.log(e);
    }
  }

  async function processMedicalPrint(windowPrintType: WindowType) {
    logTable(
      isHeathInsurance ?? false,
      isEpsPrescriptionStatus ?? false,
      flagRegister,
    );

    const responseCheckValidateBeforePrint =
      await responseCheckBeforePrintPrescriptionLazy({
        onError: (error) => {
          handleError({ error });
        },
      });

    const isEpsPrescriptionStatusCheck =
      responseCheckValidateBeforePrint?.data?.getApiEpsValidateBeforePrinting?.data?.epsPrescriptions?.some(
        (item) => item.status === 0,
      ) ?? false;

    console.log(
      isEpsPrescriptionStatusCheck,
      "isEpsPrescriptionStatusCheck pattern A",
    );

    if (isEpsPrescriptionStatusCheck) {
      return handleOpenModal(
        "PRINT_OUTPATIENT_PRESCRIPTION",
        undefined,
        undefined,
        undefined,
        windowPrintType,
      );
    }

    if (flagRegister === "OFF") {
      await handlePrintOutPatientPrescriptionPaper(true);
      return;
    }
  }

  async function handleCheckProcessPatternAPrintOutPatient(
    windowPrintType: WindowType,
  ) {
    try {
      if (windowPrintType === WindowType.MedicalPrint) {
        await processMedicalPrint(windowPrintType);
        return;
      }
    } catch (error) {
      console.error(error);
    }
  }

  async function handleCheckProcessPatternBPrintOutPatient(
    windowPrintType: WindowType,
  ) {
    try {
      console.log(
        "Switch to step 5.8 handleCheckProcessPatternBPrintOutPatient",
      );
      if (windowPrintType === WindowType.MedicalPrint) {
        const responseCheckValidateBeforePrint =
          await responseCheckBeforePrintPrescriptionLazy({
            onError: (error) => {
              handleError({ error });
            },
          });

        const isEpsPrescriptionStatusCheck =
          responseCheckValidateBeforePrint?.data?.getApiEpsValidateBeforePrinting?.data?.epsPrescriptions?.some(
            (item) => item.status === 0,
          ) ?? false;

        console.log(
          isEpsPrescriptionStatusCheck,
          "isEpsPrescriptionStatusCheck Pattern B",
        );

        if (isEpsPrescriptionStatusCheck) {
          const element =
            responseCheckValidateBeforePrint?.data
              ?.getApiEpsValidateBeforePrinting?.data?.epsPrescriptions || [];
          await Promise.all(
            element?.map(async (element) => {
              const registrationChecked = flagRegister === "ON";
              const isPrescriptionCanCancel = isEpsPrescriptionStatus;
              const printEpsReference =
                responseCheckBeforePrintPrescription
                  ?.getApiEpsValidateBeforePrinting?.data?.raiinInf
                  ?.printEpsReference ?? 0;
              const hokenGps = getListHokenGrpByHokenKbn(
                element?.hokenKbns || [],
                orderRpsWatch || [],
              );

              await Promise.all(
                hokenGps.map(async (hokenGp) => {
                  await handlePrintOutPatientPrescriptionBHYT(
                    0,
                    hokenGp,
                    false,
                    registrationChecked,
                    isPrescriptionCanCancel,
                    printEpsReference,
                    windowPrintType,
                    element?.issueType ?? 2,
                  );
                }),
              );
            }),
          );
          await sleep(3000);
          // closeWindow(isClosePageKarte);
          return;
        }
        //todo in đơn thuốc giấy không có mã quy đổi
        console.log("IN ĐƠN THUỐC GIẤY KHÔNG CÓ MÃ QUY ĐỔI");
        await handlePrintOutPatientPrescription(1);
        await sleep(3000);
      }
    } catch (e) {
      console.log(e);
    } finally {
      closeWindow(isClosePageKarte);
      if (isOpenAccounting) {
        handleOpenModal("PAYMENT");
      }
    }
  }

  async function handleCheckProcessPrintPrescription(
    windowPrintType: WindowType,
  ) {
    if (!isOutpatientPrescription) {
      closeWindow(isClosePageKarte);
      if (isOpenAccounting) {
        handleOpenModal("PAYMENT");
      }
      return;
    }

    const isPatternA =
      flagRegister === "OFF" &&
      [
        WindowType.VisitingList,
        WindowType.Accounting,
        WindowType.MedicalPrint,
      ].includes(windowPrintType);

    const isPatternB =
      flagRegister === "ON" && windowPrintType === WindowType.MedicalPrint;

    if (isPatternA) {
      console.log("Vào pattern A", flagRegister, windowPrintType);
      await handleCheckProcessPatternAPrintOutPatient(windowPrintType);
      return;
    }

    if (isPatternB) {
      console.log(
        "Vào pattern B",
        flagRegister,
        windowPrintType,
        responseCheckBeforePrintPrescription,
      );
      await handleCheckProcessPatternBPrintOutPatient(windowPrintType);
      return;
    }
  }

  const checkIdExistsInBothArrays = (prescriptionId: string) => {
    const existsInEpsPrescriptions =
      responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data?.epsPrescriptions?.some(
        (item) => item?.prescriptionId === prescriptionId,
      );

    const existsInEpsReferences =
      responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data?.epsReferences?.some(
        (item) => item?.prescriptionId === prescriptionId,
      );

    return existsInEpsPrescriptions && existsInEpsReferences;
  };

  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;

  const system = new System("/medical", systemConf);

  const timeOutConfig = useMemo(() => {
    return (
      dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.find(
        (item) => item.grpCd === 100040 && item.grpEdaNo === 4,
      )?.val ?? 0
    );
  }, [dataSystemConf]);

  function handleDisconnectSocket() {
    if (controllerAbort.current) {
      controllerAbort.current.abort();
    }
  }

  async function requestPrescriptionCopy(prescriptionId: string) {
    controllerAbort.current = new AbortController();
    const signal = controllerAbort.current.signal;
    const configSignal: ISystemRequestConfig = { signal };

    const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;

    const registerResult = await handleUpsertEpsRegister({
      arbitraryFileIdentifier,
      dispensingResultId: "",
      prescriptionId: prescriptionId ?? "",
      raiinNo: raiinNo,
      ptId: ptId,
      sinDate: sinDate,
      reqType: 7,
      status: 1,
      resultCode: "",
      resultMessage: "",
      result: "",
      reqDate: +dayjs().format("YYYYMMDD"),
    });

    if (registerResult) {
      setRegisterResult(registerResult);
    }
    const messageBody = {
      PrescriptionId: prescriptionId,
    };

    return system.createFile(
      {
        messageHeader: { ArbitraryFileIdentifier: arbitraryFileIdentifier },
        messageBody: messageBody,
      },
      "EPSsiPIR06req",
      configSignal,
    );
  }

  return {
    timeOutSetting: timeOutConfig,
    loading: loadingHook,
    handleDisconnectSocket,
    requestPrescriptionCopy,
    responseCheckBeforePrintPrescription:
      responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting
        ?.data,
    isHeathInsurance,
    isEpsPrescriptionStatus,
    checkIdExistsInBothArrays,
    handlePrintOutPatientPrescription,
    handleCheckProcessPrintPrescription,
    handlePrintOutPatientPrescriptionPaper,
    handlePrintOutPatientPrescriptionBHYT,
    getDetailEpsPrescription,
    createDrugCsvData,
    sinDate,
    raiinNo,
    ptId,
    listMappingPrescriptionConvertDrugCsvByRefillCount,
    flagRegister,
    registerResult,
  };
}
