import styled from "styled-components";

import { SvgIconPersonCall } from "@/components/ui/Icon/IconPersonCall";
import { Button } from "@/components/ui/NewButton";
import { formatMDdddWithJapanese } from "@/utils/datetime-format";
import { getReservationTimeSlot } from "@/utils/reservation-helper";

import { useMeetingWaiting } from "../../hooks/useMeetingWaiting";
import { formatDesiredDate } from "../../utils";
import { AttendeeInfo } from "../attendee/AttendeeInfo";
import { AttendeeStatus } from "../attendee/AttendeeStatus";

import type { Meeting } from "@/apis/gql/generated/types";

const Wrapper = styled.div`
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
  text-align: center;
  position: absolute;
  inset: 0;
  background: #0b1725;
  color: white;
`;

const TimeWrapper = styled.div`
  > p:not(:last-child) {
    margin-bottom: 8px;
  }
`;

const PatientInfoWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;

  & > :nth-child(3) {
    margin-bottom: -12px;
  }
`;

const PrimaryText = styled.p`
  font-size: 24px;
  font-weight: bold;
`;

const SecondaryText = styled.p`
  font-size: 18px;
  font-weight: normal;
`;

const CustomButton = styled(Button)<{ $isPharmacy: boolean }>`
  font-size: ${({ $isPharmacy }) => ($isPharmacy ? "14px" : "18px")};
  height: 48px;
  width: 220px;
`;

const ConfirmConnectButton = styled(Button)`
  font-size: 16px;
  width: 240px;
  background: transparent !important;
  color: #007aff;
`;

type Props = {
  meeting: Meeting;
  requestTestDevice: () => void;
  requestJoinMeeting: () => void;
};

export const MeetingWaiting: React.FC<Props> = ({
  meeting,
  requestTestDevice,
  requestJoinMeeting,
}) => {
  const {
    isPharmacy,
    reservationDetail,
    familyReservations,
    treatmentPatient,
    displayName,
    pharmacyDesiredDate,
    isPatientJoined,
  } = useMeetingWaiting(meeting);

  return (
    <Wrapper>
      <TimeWrapper>
        {isPharmacy ? (
          <>
            {!pharmacyDesiredDate && <PrimaryText>時間指定なし</PrimaryText>}

            {pharmacyDesiredDate && (
              <>
                <SecondaryText>
                  {formatMDdddWithJapanese(pharmacyDesiredDate.desiredDate)}
                </SecondaryText>
                <PrimaryText>
                  {formatDesiredDate(
                    pharmacyDesiredDate.desiredDate,
                    pharmacyDesiredDate.desiredType,
                  )}
                </PrimaryText>
              </>
            )}
          </>
        ) : (
          <>
            <SecondaryText>
              {formatMDdddWithJapanese(
                `${reservationDetail?.examTimeSlot.examStartDate}`,
              )}
            </SecondaryText>
            <PrimaryText>
              {getReservationTimeSlot({
                examStartDate: new Date(
                  `${reservationDetail?.examTimeSlot.examStartDate}`,
                ),
                examEndDate: new Date(
                  `${reservationDetail?.examTimeSlot.examEndDate}`,
                ),
              })}
            </PrimaryText>
          </>
        )}
      </TimeWrapper>
      <PatientInfoWrapper>
        <SvgIconPersonCall />
        <SecondaryText>
          {reservationDetail?.calendarTreatment.treatmentDepartment?.title}
        </SecondaryText>
        <PrimaryText>{displayName}</PrimaryText>
        <AttendeeInfo
          patient={treatmentPatient}
          customer={meeting.pharmacyReserve?.customer}
          familyReservations={familyReservations}
        />
        <AttendeeStatus joined={isPatientJoined} />
      </PatientInfoWrapper>
      <CustomButton
        $isPharmacy={isPharmacy}
        varient="secondary"
        shape="round"
        onClick={requestJoinMeeting}
      >
        {isPharmacy ? "オンライン服薬指導開始" : "オンライン診療開始"}
      </CustomButton>
      <ConfirmConnectButton varient="custom" onClick={requestTestDevice}>
        カメラとマイクの接続テスト
      </ConfirmConnectButton>
    </Wrapper>
  );
};
