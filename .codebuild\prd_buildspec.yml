version: 0.2

env:
  shell: bash
  # TODO hattori パイプライン上で取得するように修正後消す
  parameter-store:
    SURVEYJS_LICENSE_KEY: "/DENKARU/PRD/USER_WEB/SURVEYJS_LICENSE_KEY"
    SENTRY_AUTH_TOKEN: "/DENKARU/PRD/USER_WEB/SENTRY_AUTH_TOKEN"
phases:
  install:
    runtime-versions:
      nodejs: 22
    commands:
      - n 22.16.0
      - apt-get update
      - apt-get install -y git
      - sudo apt-get install --reinstall -y libssl-dev
  pre_build:
    commands:
      # git tag
      - mkdir -p /root/.ssh
      - aws ssm get-parameter --name /COMMON/PRD/GITHUB_ACCESS_KEY --with-decryption --query Parameter.Value --output text > /root/.ssh/id_ed25519
      - chmod 600 /root/.ssh/id_ed25519
      - DATE_TAG=$(date +%Y%m%d%H%M) # 日時タグを生成（YYYYMMDDHHmm形式）
      - echo $DATE_TAG
      - eval "$(ssh-agent -s)"
      - ssh-add /root/.ssh/id_ed25519
      - git config --global user.email "<EMAIL>"
      - git config --global user.name "devhealthcare"
      - cd ..
      # - mv denkaru-user-web denkaru-user-web_org # CodeBuildから実行されるとすでに同名のディレクトリが存在するので変更しておく
      - <NAME_EMAIL>:bizleap-healthcare/denkaru-user-web.git
      - cd denkaru-user-web
      - git checkout prd # デフォルトブランチはdevなのでこれがないと想定外のリリースをしてしまう
      - |
        echo Checking if tag $DATE_TAG already exists...
        if git rev-parse ${DATE_TAG} >/dev/null 2>&1
        then
          echo "Tag ${DATE_TAG} already exists. Skipping tag creation and push."
        else
          echo "Creating Git tag ${DATE_TAG}..."
          git tag $DATE_TAG
          git push origin --tags
        fi

      ### PRE-CHECK
      - pwd
      - rm -rf .npmrc
      - aws --version
      - node -v
      - yarn -v
      - ls -la
      ### ADD-ENV VARIABLES
      - SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN}
      - NEXT_PUBLIC_SURVEYJS_LICENSE_KEY=${SURVEYJS_LICENSE_KEY}
      ### NODE_MODULE INSTALL
      - yarn install
      ### CODE CHECK
      - export NODE_OPTIONS="--max-old-space-size=8192"
      - yarn type-check
      - yarn lint
  build:
    commands:
      ### BUILD
      - yarn build:prd
    on-failure: ABORT
  post_build:
    commands:
      ### DEPLOY
      ### STEP 1) AWS S3 SYNC
      - aws s3 sync --region ap-northeast-1 ./out s3://${S3_BUCKET_NAME} --delete
      ### STEP 2) AWS CloudFront RESET
      - aws cloudfront create-invalidation --distribution-id ${CF_DISTRIBUTION_ID} --paths "/*"
    on-failure: ABORT

cache:
  paths:
    - /root/.cache/yarn/**/*
