import React, { useEffect, useState } from "react";

import { Flex } from "antd";
import { Controller, useForm } from "react-hook-form";
import dayjs from "dayjs";
import { useSearchParams } from "next/navigation";
import router from "next/router";

import { But<PERSON> } from "@/components/ui/NewButton";
import { Modal } from "@/components/ui/Modal";
import {
  CustomStyleButton,
  FilterWrapper,
  ModalContent,
  StyledLabel,
  TextHeader,
  WrapperInfomationHeader,
} from "@/features/karte/ui/Karte/PrescriptionInformation/style";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { DatePickerSuffixClear } from "@/components/ui/DatePicker";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { Form } from "@/components/functional/Form";
import { TextInput } from "@/components/ui/TextInput";
import { TableInformationPrescription } from "@/features/karte/ui/Karte/PrescriptionInformation/TableInformationPrescription";
import { useGetApiEpsGetPrescriptionInfListQuery } from "@/apis/gql/operations/__generated__/prescription-information";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import {
  isEmptyPayload,
  isValidPayload,
  isValidPayLoadStartDateBiggerEndDate,
  renderLastDispensingListEndDate,
  renderMessageError,
} from "@/features/karte/ui/Karte/PrescriptionInformation/utils";
import { ModalErrorPrescription } from "@/features/karte/ui/Karte/PrintSetting/ModalErrorPrescription";
import { RenderIf } from "@/utils/common/render-if";
import { useGetPatientInfo } from "@/components/common/KartePayment/hooks/useGetPatientInfo";
import { ModalGetBatchDispensingResult } from "@/features/karte/ui/Karte/PrescriptionInformation/ModalGetBatchDispensingResult";
import { checkConnectionSocket } from "@/utils/socket-helper/connection";

import { ModalPreparedMedicine } from "../ModalPreparedMedicine/ModalPreparedMedicine";
import { ModalPrescriptionDocument } from "../ModalPreparedMedicine/ModalPrescriptionDocument";
import { ModalDispensingInformationDetail } from "../DispensingInformation/DispensingInformationDetail";

import { ModalSearchPatient } from "./ModalSearchPatient";

import type { ApolloError } from "@apollo/client";

type FormType = {
  startSinDate: number;
  endSinDate: number;
  startDispensingDate: number;
  endDispensingDate: number;
  patientNum: string;
};

export function ModalInformationPrescription() {
  const {
    state: {
      isOpenInformationPrescription,
      isOpenSearchPatient,
      isOpenGetBatchDispensing,
    },
    isOpenFromDispensingResultModal,
    handleCloseModal,
    handleOpenModal,
  } = useModal();
  const { handleError } = useErrorHandler();

  const searchParams = useSearchParams();
  const { id } = router.query;
  const { data: dataInfoPatient } = useGetPatientInfo({
    ptId: id?.toString() ?? "0",
    skip: !id,
  });
  const sinDate = isOpenFromDispensingResultModal
    ? "0"
    : (searchParams.get("sinDate") ?? "0");

  const [errorMessage, setErrorMessage] = useState("");
  const [open, setOpen] = useState(false);
  const [openModal, setOpenModal] = useState<string>("");
  const [modalOther, setModalOther] = useState<string>("");
  const [cancelReason, setCancelReason] = useState<string>("");
  const [dispensingTimes, setDispensingTimes] = useState<number[]>([]);
  const [refillCount, setRefillCount] = useState<number>(0);
  const [base64Dispensing, setBase64Dispensing] = useState<string[]>([]);
  const [base64Prescription, setBase64Prescription] = useState<string>("");
  const [loadingCheckSocket, setLoadingCheckSocket] = useState(false);
  const defaultValues = {};

  const { control, setValue, reset, handleSubmit } = useForm<FormType>({
    defaultValues,
  });

  useEffect(() => {
    if (sinDate) {
      setValue("patientNum", dataInfoPatient?.ptNum ?? "0");
      setValue(
        "startSinDate",
        Number(
          dayjs(sinDate, "YYYYMMDD").subtract(1, "year").format("YYYYMMDD"),
        ),
      );
      setValue("endSinDate", Number(sinDate));
    }
  }, [sinDate, id, dataInfoPatient]);

  const {
    data: dataPrescriptionList,
    refetch,
    loading,
  } = useGetApiEpsGetPrescriptionInfListQuery({
    variables: {
      patientNum: dataInfoPatient?.ptNum ?? "0",
      startSinDate: Number(
        dayjs(sinDate, "YYYYMMDD").subtract(1, "year").format("YYYYMMDD"),
      ),
      startDispensingDate: undefined,
      endSinDate: Number(sinDate),
      endDispensingDate: undefined,
    },
    onError: (error) => {
      handleError({ error }).then();
    },
    fetchPolicy: "no-cache",
  });

  const onSubmit = async (values: FormType) => {
    try {
      const isErrorEmpty = isEmptyPayload(values);

      const isValidPayLoadBiggerSinDate = isValidPayLoadStartDateBiggerEndDate(
        values.startSinDate,
        values.endSinDate,
      );

      const isValidPayLoadBiggerDispensingDate =
        isValidPayLoadStartDateBiggerEndDate(
          values.startDispensingDate,
          values.endDispensingDate,
        );

      const isValidPayloadSinDate = isValidPayload(
        values.patientNum,
        values.startSinDate,
        values.endSinDate,
      );

      const isValidPayloadDispensingDate = isValidPayload(
        values.patientNum,
        values.startDispensingDate,
        values.endDispensingDate,
      );

      if (
        isErrorEmpty ||
        isValidPayLoadBiggerSinDate ||
        isValidPayLoadBiggerDispensingDate ||
        isValidPayloadSinDate ||
        isValidPayloadDispensingDate
      ) {
        const errorMessage = renderMessageError(
          isErrorEmpty,
          isValidPayLoadBiggerSinDate,
          isValidPayLoadBiggerDispensingDate,
          isValidPayloadSinDate,
          isValidPayloadDispensingDate,
        );
        setErrorMessage(errorMessage ?? "");
        setOpen(true);
        return;
      }

      await refetch({
        patientNum: !values.patientNum ? "0" : values.patientNum,
        startSinDate: values.startSinDate ?? "0",
        endSinDate: values.endSinDate ?? "0",
        startDispensingDate: values.startDispensingDate ?? "0",
        endDispensingDate: values.endDispensingDate ?? "0",
      });
    } catch (error) {
      handleError({ error: error as ApolloError }).then();
    }
  };

  return (
    <>
      <RenderIf condition={isOpenSearchPatient}>
        <ModalSearchPatient
          onSubmit={(value: string) => {
            setValue("patientNum", value);
          }}
        />
      </RenderIf>
      <RenderIf condition={!isOpenSearchPatient}>
        <Modal
          isOpen={isOpenInformationPrescription}
          title="処方箋情報"
          centered
          forceRender
          width={1280}
          centerFooterContent={true}
          footer={[
            <Button
              onClick={() => handleCloseModal("INFORMATION_PRESCRIPTION")}
              shape="round"
              varient="tertiary"
              key="cancel"
            >
              閉じる
            </Button>,
          ]}
        >
          <ModalContent>
            <RenderIf condition={isOpenGetBatchDispensing}>
              <ModalGetBatchDispensingResult
                startDateRequest={Number(
                  dataPrescriptionList?.getApiEpsGetPrescriptionInfList?.data
                    ?.prescriptionInfListModel?.lastDispensingListEndDate ?? 0,
                )}
              />
            </RenderIf>
            <WrapperInfomationHeader>
              {
                <span>
                  <TextHeader>前回一括取得日時：</TextHeader>
                  {renderLastDispensingListEndDate(
                    Number(
                      dataPrescriptionList?.getApiEpsGetPrescriptionInfList
                        ?.data?.prescriptionInfListModel
                        ?.lastDispensingListEndDate ?? 0,
                    ),
                  )}
                </span>
              }
              <CustomStyleButton
                disabled={loadingCheckSocket}
                loading={loadingCheckSocket}
                onClick={async () => {
                  try {
                    setLoadingCheckSocket(true);
                    await checkConnectionSocket({
                      onSuccess: () => handleOpenModal("GET_BATCH_DISPENSING"),
                      onError: () => handleOpenModal("CONNECTION_SOCKET"),
                    });
                  } finally {
                    setLoadingCheckSocket(false);
                  }
                }}
              >
                調剤結果一括取得
              </CustomStyleButton>
            </WrapperInfomationHeader>
            <Form id="search-prescription" onSubmit={handleSubmit(onSubmit)}>
              <FilterWrapper>
                <Flex gap={12}>
                  <div>
                    <StyledLabel label="診療日" />
                    <Flex align="center" gap={8}>
                      <Controller
                        name="startSinDate"
                        control={control}
                        render={({ field }) => (
                          <DatePickerSuffixClear
                            format="YYYY/MM/DD"
                            placeholder="開始日を指定"
                            suffixIcon={<SvgIconCalendar />}
                            allowClear={true}
                            onChange={(date) =>
                              field.onChange(
                                date
                                  ? Number(dayjs(date).format("YYYYMMDD"))
                                  : undefined,
                              )
                            }
                            value={
                              field.value
                                ? dayjs(String(field.value), "YYYYMMDD")
                                : undefined
                            }
                            disabledDate={(current) =>
                              current.isAfter(dayjs(), "day")
                            }
                          />
                        )}
                      />
                      <span>〜</span>
                      <Controller
                        name="endSinDate"
                        control={control}
                        render={({ field }) => {
                          return (
                            <DatePickerSuffixClear
                              format="YYYY/MM/DD"
                              placeholder="終了日を指定"
                              suffixIcon={<SvgIconCalendar />}
                              allowClear={true}
                              onChange={(date) =>
                                field.onChange(
                                  date
                                    ? Number(dayjs(date).format("YYYYMMDD"))
                                    : undefined,
                                )
                              }
                              value={
                                field.value
                                  ? dayjs(String(field.value), "YYYYMMDD")
                                  : undefined
                              }
                              disabledDate={(current) =>
                                current.isAfter(dayjs(), "day")
                              }
                            />
                          );
                        }}
                      />
                    </Flex>
                  </div>
                  <div>
                    <StyledLabel label="調剤日" />
                    <Flex align="center" gap={8}>
                      <Controller
                        name="startDispensingDate"
                        control={control}
                        render={({ field }) => (
                          <DatePickerSuffixClear
                            format="YYYY/MM/DD"
                            placeholder="開始日を指定"
                            suffixIcon={<SvgIconCalendar />}
                            allowClear={true}
                            onChange={(date) =>
                              field.onChange(
                                date
                                  ? Number(dayjs(date).format("YYYYMMDD"))
                                  : null,
                              )
                            }
                            value={
                              field.value
                                ? dayjs(String(field.value), "YYYYMMDD")
                                : null
                            }
                            disabledDate={(current) =>
                              current.isAfter(dayjs(), "day")
                            }
                          />
                        )}
                      />
                      <span>〜</span>
                      <Controller
                        name="endDispensingDate"
                        control={control}
                        render={({ field }) => {
                          return (
                            <DatePickerSuffixClear
                              format="YYYY/MM/DD"
                              placeholder="終了日を指定"
                              suffixIcon={<SvgIconCalendar />}
                              allowClear={true}
                              onChange={(date) =>
                                field.onChange(
                                  date
                                    ? Number(dayjs(date).format("YYYYMMDD"))
                                    : null,
                                )
                              }
                              value={
                                field.value
                                  ? dayjs(String(field.value), "YYYYMMDD")
                                  : null
                              }
                              disabledDate={(current) =>
                                current.isAfter(dayjs(), "day")
                              }
                            />
                          );
                        }}
                      />
                    </Flex>
                  </div>
                  <div>
                    <StyledLabel label="患者番号" />
                    <Controller
                      name={"patientNum"}
                      control={control}
                      render={({ field }) => (
                        <TextInput
                          style={{ width: "100px" }}
                          onChange={(e) => {
                            field.onChange(e.target.value);
                          }}
                          value={field.value as string}
                        />
                      )}
                    />
                  </div>

                  <Flex align={"flex-end"} gap={12}>
                    <Button
                      varient="inline"
                      style={{
                        width: 100,
                        height: 36,
                        marginBottom: "7px",
                      }}
                      shape="round"
                      onClick={() => {
                        handleOpenModal("SEARCH_PATIENT");
                      }}
                    >
                      患者検索
                    </Button>
                    <Button
                      varient="secondary"
                      style={{ width: 100, height: 36 }}
                      shape="round"
                      form="search-prescription"
                      htmlType="submit"
                    >
                      検索
                    </Button>
                    <Button
                      style={{ width: 100, height: 36 }}
                      key="close"
                      varient="standard"
                      shape="round"
                      onClick={() => {
                        setErrorMessage("");
                        reset({
                          patientNum: dataInfoPatient?.ptNum ?? "",
                          startSinDate: Number(
                            dayjs(sinDate, "YYYYMMDD")
                              .subtract(1, "year")
                              .format("YYYYMMDD"),
                          ),
                          endSinDate: Number(sinDate),
                          startDispensingDate: undefined,
                          endDispensingDate: undefined,
                        });
                      }}
                    >
                      クリア
                    </Button>
                  </Flex>
                </Flex>
              </FilterWrapper>
            </Form>
            <TableInformationPrescription
              data={
                dataPrescriptionList?.getApiEpsGetPrescriptionInfList?.data
                  ?.prescriptionInfListModel?.epsPrescriptionModels || []
              }
              loading={loading}
              setOpenModal={setOpenModal}
              setModal={setModalOther}
              setCancelReason={setCancelReason}
              setDispensingTimes={setDispensingTimes}
              setBase64Dispensing={setBase64Dispensing}
              setBase64Prescription={setBase64Prescription}
              setRefillCount={setRefillCount}
            />
            <ModalErrorPrescription
              open={open}
              title={"エラー"}
              subTitleContent={errorMessage}
              handleClose={() => setOpen(false)}
            />

            <RenderIf condition={openModal === "DISPENSING_INFO"}>
              <ModalDispensingInformationDetail
                open={openModal === "DISPENSING_INFO"}
                onClose={() => setOpenModal("")}
                dispensingTimes={dispensingTimes}
                base64Dispensing={base64Dispensing}
              />
            </RenderIf>
            <RenderIf condition={openModal === "PRESCRIPTION_MEDICINE"}>
              <ModalPreparedMedicine
                open={openModal === "PRESCRIPTION_MEDICINE"}
                onClose={() => setOpenModal("")}
                base64Prescription={base64Prescription}
                refillCount={refillCount}
              />
            </RenderIf>
            <RenderIf condition={modalOther === "error"}>
              <ModalPrescriptionDocument
                onClose={() => setModalOther("")}
                title="処方内容"
                subtitle="処方箋情報が、電子処方箋管理サービスに登録されていません"
              />
            </RenderIf>
            <RenderIf condition={openModal === "PrescriptionRecall"}>
              <ModalPrescriptionDocument
                onClose={() => setOpenModal("")}
                title="調剤結果"
                subtitle="処方箋が回収されました"
                description={`回収理由：${cancelReason}`}
              />
            </RenderIf>
          </ModalContent>
        </Modal>
      </RenderIf>
    </>
  );
}
