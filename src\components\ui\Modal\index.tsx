import React from "react";

import { Modal as AntdModal } from "antd";
import styled, { css } from "styled-components";

import { ZIndexOrder } from "@/constants/common";

import type { ModalProps } from "antd";

const OverlayModal = styled.div<{
  $isOpen?: boolean;
  $position?: string;
  $hasBackground?: boolean;
  $zIndex?: number;
}>`
  ${({ $isOpen, $zIndex }) =>
    $isOpen &&
    css`
      display: block;
      z-index: ${$zIndex || ZIndexOrder.ModalDefault};
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
    `}
  position: ${({ $position }) => $position || "fixed"};

  ${({ $hasBackground }) =>
    $hasBackground &&
    css`
      background-color: rgba(0, 0, 0, 0.3);
    `}
`;

const StyledModal = styled(AntdModal)<{
  errorModal?: boolean;
  centerFooterContent?: boolean;
  widthFitContent?: boolean;
  customHeight?: number;
  hasBorder?: boolean;
}>`
  * {
    font-family: NotoSansJP !important;
  }
  .ant-modal-content {
    padding: 0;
    border-radius: 0;
    background-color: unset;
    box-shadow: unset;
    ${({ widthFitContent }) =>
      widthFitContent &&
      css`
        width: fit-content;
      `}
  }

  .ant-modal-header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 52px;
    background-color: ${({ errorModal }) =>
      errorModal ? "#e74c3c" : "#005BAC"};
    border-radius: 26px 26px 0 0;
    margin: 0;

    > .ant-modal-title {
      text-align: center;

      font-weight: 700;
      font-size: 18px;
      color: #fff;
      line-height: 1;
    }
  }

  .ant-modal-body {
    background-color: #fff;
    ${({ hasBorder }) =>
      hasBorder &&
      css`
        border: 1px solid #e2e3e5;
      `};
  }

  .ant-modal-footer {
    display: flex;
    justify-content: ${({ centerFooterContent }) =>
      centerFooterContent ? "center" : "space-between"};
    margin: 0;
    padding: 24px;
    ${({ customHeight }) =>
      customHeight &&
      css`
        height: ${customHeight}px;
      `};
    border-radius: 0 0 28px 28px;
    background-color: #ffffff;
    border-top: 1px solid #e2e3e5;
    > .ant-btn + .ant-btn {
      margin: 0;
    }

    ${({ hasBorder }) =>
      hasBorder &&
      css`
        border: 1px solid #e2e3e5;
      `};
  }
`;

type Props = ModalProps & {
  children: React.ReactNode;
  isOpen?: boolean;
  errorModal?: boolean;
  centerFooterContent?: boolean;
  widthFitContent?: boolean;
  offAnimation?: boolean;
  forceRender?: boolean;
  zIndex?: number;
  height?: number;
  position?: string;
  hasBorder?: boolean;
  hasBackground?: boolean;
};

export const Modal: React.FC<Props> = (props: Props) => {
  const {
    children,
    centered = true,
    className,
    width = 480,
    isOpen,
    onOk,
    onCancel,
    footer,
    title,
    errorModal = false,
    destroyOnClose,
    centerFooterContent,
    mask = true,
    offAnimation = true,
    style,
    modalRender,
    widthFitContent,
    forceRender = false,
    zIndex,
    wrapClassName,
    height,
    closable = false,
    position,
    hasBorder = false,
    hasBackground = false,
  } = props;

  return (
    <>
      <OverlayModal
        $isOpen={isOpen}
        $position={position}
        $hasBackground={hasBackground}
        $zIndex={zIndex}
      />
      <StyledModal
        {...props}
        wrapClassName={wrapClassName}
        forceRender={forceRender}
        className={className}
        closable={closable}
        centered={centered}
        title={title}
        width={width}
        widthFitContent={widthFitContent}
        open={isOpen}
        onOk={onOk}
        onCancel={onCancel}
        footer={footer}
        errorModal={errorModal}
        centerFooterContent={centerFooterContent}
        destroyOnClose={destroyOnClose}
        mask={mask}
        transitionName={offAnimation ? "" : undefined}
        style={style}
        modalRender={modalRender}
        maskClosable={false}
        zIndex={zIndex || ZIndexOrder.ModalDefault}
        customHeight={height}
        hasBorder={hasBorder}
      >
        {children}
      </StyledModal>
    </>
  );
};
