import {
  createContext,
  use<PERSON>allback,
  useContext,
  useMemo,
  useState,
} from "react";

import dayjs from "dayjs";
import _, { omit } from "lodash";
import { useFormContext } from "react-hook-form";
import { v4 } from "uuid";

import {
  useGetApiEpsGetDuplicateMedicationCheckLazyQuery,
  usePostApiEpsGetOutDrugCsvDataMutation,
  usePostApiEpsGetPreRegistrationDataMutation,
  usePostApiEpsSaveDuplicateMedicationCheckMutation,
} from "@/apis/gql/operations/__generated__/duplicate-medication";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { ItemCdConst } from "@/features/karte/constants/order";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { useKarteFooter } from "@/features/karte/hooks/useKarteFooter";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { convertFormValueToUpsertParams } from "@/features/karte/ui/Karte/MedicineOrder/utils/orderForm";
import { extractBracketedText } from "@/features/karte/ui/Karte/PrescriptionInformation/utils";
import { usePrintSetting } from "@/features/karte/ui/Karte/PrintSetting/usePrintSetting";
import {
  createTodayOrderDetail,
  createTodayOrderInfo,
} from "@/features/karte/ui/KartePayment/utils/OrderDetail";
import {
  formatGetOutDrugCsv,
  useGetRefillData,
} from "@/features/karte/utils/e-prescription";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useReadFileXML } from "@/hooks/useReadFileXML";
import { useUpsertEpsRegister } from "@/hooks/useUpsertEpsRegister";
import { System } from "@/utils/socket-helper";
import { useSession } from "@/hooks/useSession";
import { checkConnectionSocket } from "@/utils/socket-helper/connection";

import type {
  Data,
  DomainModelsEpsChkEpsChkModel,
  DomainModelsEpsPreRegistrationCheckingModel,
  DomainModelsEpsPrescriptionEpsPrescriptionModel,
  DomainModelsEpsReqEpsReqModel,
  DomainModelsMedicalExaminationCheckedOrderModel,
  EmrCloudApiRequestsEpsSaveDuplicateMedicationCheckRequestInput,
  EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput,
  EmrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput,
  EmrCloudApiResponsesSystemConfSystemConfDto,
  OrdInfDetail,
  UseCaseInsuranceGetComboListGetInsuranceComboItemOuputData,
  UseCaseMedicalExaminationGetCheckedOrderOdrInfItemInput,
} from "@/apis/gql/generated/types";
import type { InsuranceDefaultType } from "@/features/karte/hooks/useGetOrderInfoContext";
import type {
  KarteFormData,
  OrderRp,
} from "@/features/karte/types/karte-order";
import type { TypeResponseCheckingDomainModelsEpsPrescriptionEpsPrescriptionModel } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/ModalProcessPrintOutPatient";
import type { FormStatePrintSetting } from "@/features/karte/ui/Karte/PrintSetting/ModalPrintSetting";
import type {
  ITodayOrderDetail,
  ITodayOrderInfo,
} from "@/features/karte/ui/KartePayment/utils/OrderDetail";
import type { IFile, IResponse } from "@/utils/socket-helper/socket.type";
import type { Dispatch, ReactNode, SetStateAction } from "react";

export type TFlag = "ON" | "OFF" | null;

interface IMessageSocket {
  PrescriptionInfo?: string;
  SameMedicalInstitutionAlertFlg?: number;
  OralBrowsingConsent?: number;
}

type Props = {
  children: ReactNode;
};

export type DataPrescriptionType = {
  refileCount?: number;
  prescriptionDocument?: string;
};

export type DataCheckPrescriptionChangeOrderType =
  DomainModelsEpsPrescriptionEpsPrescriptionModel & {
    isOrderChange: boolean;
  };
type ListPrescriptionType = DataCheckPrescriptionChangeOrderType & {
  drugCsv: string;
};

type ListPrescriptionDrugXml = ListPrescriptionType & {
  base64DrugXml: string;
};

export type PrescriptionDrugXml =
  DomainModelsEpsPrescriptionEpsPrescriptionModel & {
    base64DrugXml?: string;
    drugCsv?: string;
  };

export enum StatusModal {
  NONE = "NONE",
  ERROR = "ERROR",
  ERROR_FILE_NONE = "ERROR_FILE_NONE",
  ERROR_PROCESSING = "ERROR_PROCESSING",
  ERROR_OFF_PRESCRIPTION = "ERROR_OFF_PRESCRIPTION",
  INFO_MEDICAL = "INFO_MEDICAL",
  RESULT = "RESULT",
  LOADING = "LOADING",
}

type StatusModalType =
  | StatusModal.NONE
  | StatusModal.ERROR
  | StatusModal.ERROR_FILE_NONE
  | StatusModal.ERROR_PROCESSING
  | StatusModal.ERROR_OFF_PRESCRIPTION
  | StatusModal.INFO_MEDICAL
  | StatusModal.RESULT
  | StatusModal.LOADING;

type EPrescriptionContextType = {
  statePrintSetting: FormStatePrintSetting;
  setStatePrintSetting: React.Dispatch<
    React.SetStateAction<FormStatePrintSetting>
  >;
  payloadSaveMedical: EmrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput | null;
  setPayloadSaveMedical: React.Dispatch<
    React.SetStateAction<EmrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput | null>
  >;
  settingOutpatient: boolean;

  dataCheckPrescriptionChangeOrder: DataCheckPrescriptionChangeOrderType[];
  setDataCheckPrescriptionChangeOrder: React.Dispatch<
    React.SetStateAction<DataCheckPrescriptionChangeOrderType[]>
  >;

  listPrescription: ListPrescriptionType[];
  dataListPrescription: DataPrescriptionType[];
  listEpsPrescriptionAllowPrint: TypeResponseCheckingDomainModelsEpsPrescriptionEpsPrescriptionModel[];
  setListEpsPrescriptionAllowPrint: React.Dispatch<
    React.SetStateAction<
      TypeResponseCheckingDomainModelsEpsPrescriptionEpsPrescriptionModel[]
    >
  >;
  listDrugBase64SignXml: ListPrescriptionDrugXml[];
  setListDrugBase64SignXml: React.Dispatch<
    React.SetStateAction<ListPrescriptionDrugXml[]>
  >;
  setListPrescription: React.Dispatch<
    React.SetStateAction<ListPrescriptionType[]>
  >;
  precriptionNew: EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput;
  setPrecriptionNew: React.Dispatch<
    React.SetStateAction<EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput>
  >;
  listDrugCSV: {
    refileCount: number;
    prescriptionDocument: string;
  }[];
  setListDrugCSV: React.Dispatch<
    React.SetStateAction<
      {
        refileCount: number;
        prescriptionDocument: string;
      }[]
    >
  >;
  systemConf: EmrCloudApiResponsesSystemConfSystemConfDto[];
  flagRegisterChange: TFlag | null;
  setFlagRegisterChange: React.Dispatch<React.SetStateAction<TFlag | null>>;
  flagRegister: TFlag;
  flagCancel: TFlag;
  setFlagRegister: (value: TFlag) => void;
  setFlagCancel: (value: TFlag) => void;
  epsChkListData: DomainModelsEpsChkEpsChkModel[];
  statusModal: StatusModalType;
  errorMessage: string;
  errorMsg: string;
  isDisableAcquisitionDrug: boolean;
  isOpenDuplicateMedicationCheck: boolean;
  secondActive: number;
  isPendingCheck: boolean;
  handleConsentPatient: () => void;
  handleChangeStatusModal: (status: StatusModalType) => void;
  setErrorMessage: (message: string) => void;
  handleBackKarte: () => void;
  handleUpdateKarte: () => Promise<boolean>;
  handleUpdateKarteFlagOff: () => void;
  onClickDuplicate: () => Promise<void>;
  setIsPendingCheck: (value: boolean) => void;
  onSkipChecksDuplicateMedication: () => void;
  onConfirmSkipChecksDuplicateMedication: () => void;
  resultDataSocket: IResponse<IFile> | null;
  onUpdateSocket: () => void;
  setPrescriptionIdList: Dispatch<
    SetStateAction<DomainModelsEpsPrescriptionEpsPrescriptionModel[]>
  >;
  prescriptionIdList: DomainModelsEpsPrescriptionEpsPrescriptionModel[];
  isShowModalCreateRegisterInfo: boolean;
  setIsShowModalCreateRegisterInfo: Dispatch<SetStateAction<boolean>>;
  handleGetPrescriptionIdList: () => void;
  flow: "1" | "2";
  setFlow: (flow: "1" | "2") => void;
  orders: UseCaseMedicalExaminationGetCheckedOrderOdrInfItemInput[];
  sinDate: number;
  ptId: string;
  raiinNo: string;
  createOdrInf: (
    checkOrder: DomainModelsMedicalExaminationCheckedOrderModel,
    checkOrders: DomainModelsMedicalExaminationCheckedOrderModel[],
  ) => ITodayOrderInfo | null;
  params: EmrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput;
  insuranceDefault: InsuranceDefaultType | null;
  dataHeaderInfo: Data | null;
  insuranceComboList: UseCaseInsuranceGetComboListGetInsuranceComboItemOuputData[];
  getCheckAge: number;
  defaultOrderInf: UseCaseMedicalExaminationGetCheckedOrderOdrInfItemInput & {
    odrDetails?: ITodayOrderDetail[];
  };
  syosaiKbn: number | undefined;
  getSystemSetting: (params: { grpCd: number; grpEdaNo: number }) => number;

  updateEpsRegister: (
    registerResult: DomainModelsEpsReqEpsReqModel,
    createFileResult: IResponse<IFile> | null,
  ) => Promise<void>;
  setRegisterResult: (registerResult: DomainModelsEpsReqEpsReqModel) => void;
  registerResult: DomainModelsEpsReqEpsReqModel | null;
  isClosePageKarte: boolean;
  setIsClosePageKarte: (value: boolean) => void;
  isOpenAccounting: boolean;
  setIsOpenAccounting: (value: boolean) => void;
  isOpenEstimatePaymentModal: boolean;
  setIsOpenEstimatePaymentModal: (value: boolean) => void;
};

const defaultContextValue: EPrescriptionContextType = {
  statePrintSetting: {
    isCreatePrescription: true,
    isInstruction: false,
    isMedicalRecord1: false,
    isMedicalRecord2: false,
    isOutpatientPrescription: false,
    isHospitalPrescription: false,
    isDrugInformationSheet: false,
    outpatientOption: "3",
  },
  setStatePrintSetting: () => {},
  payloadSaveMedical: null,
  setPayloadSaveMedical: () => {},
  settingOutpatient: false,
  dataCheckPrescriptionChangeOrder: [],
  setDataCheckPrescriptionChangeOrder: () => {},

  listPrescription: [],
  dataListPrescription: [],
  listEpsPrescriptionAllowPrint: [],
  setListEpsPrescriptionAllowPrint: () => {},
  listDrugBase64SignXml: [],
  setListDrugBase64SignXml: () => {},
  setListPrescription: () => {},
  precriptionNew: {},
  setPrecriptionNew: () => {},
  listDrugCSV: [],
  setListDrugCSV: () => {},
  systemConf: [],
  flagRegisterChange: null,
  setFlagRegisterChange: () => {},
  flagRegister: null,
  flagCancel: null,
  setFlagRegister: (_value: TFlag) => {},
  setFlagCancel: (_value: TFlag) => {},
  epsChkListData: [],
  statusModal: StatusModal.NONE,
  errorMessage: "",
  errorMsg: "",
  isDisableAcquisitionDrug: false,
  isOpenDuplicateMedicationCheck: false,
  secondActive: 0,
  isPendingCheck: false,
  handleChangeStatusModal: () => {},
  setErrorMessage: () => {},
  handleBackKarte: () => {},
  handleUpdateKarte: async () => false,
  handleUpdateKarteFlagOff: () => {},
  onClickDuplicate: async () => {},
  handleConsentPatient: () => {},
  setIsPendingCheck: () => {},
  onSkipChecksDuplicateMedication: () => {},
  onConfirmSkipChecksDuplicateMedication: () => {},
  resultDataSocket: null,
  onUpdateSocket: () => {},
  setPrescriptionIdList: () => false,
  prescriptionIdList: [],
  isShowModalCreateRegisterInfo: false,
  setIsShowModalCreateRegisterInfo: () => {},
  handleGetPrescriptionIdList: () => {},
  flow: "1",
  setFlow: (_flow: "1" | "2") => {},
  orders: [],
  sinDate: 0,
  ptId: "",
  raiinNo: "",
  createOdrInf: (
    _checkOrder: DomainModelsMedicalExaminationCheckedOrderModel,
    _checkOrders: DomainModelsMedicalExaminationCheckedOrderModel[],
  ) => {
    return null;
  },
  params: {},
  insuranceDefault: null,
  dataHeaderInfo: null,
  insuranceComboList: [],
  getCheckAge: 0,
  defaultOrderInf: {},
  syosaiKbn: undefined,
  getSystemSetting: () => 0,
  updateEpsRegister: async () => {},
  setRegisterResult: () => {},
  registerResult: null,
  isClosePageKarte: false,
  setIsClosePageKarte: () => {},
  isOpenAccounting: false,
  setIsOpenAccounting: () => {},
  isOpenEstimatePaymentModal: false,
  setIsOpenEstimatePaymentModal: () => {},
};

export const defaultValueStateKloc = {
  isCreatePrescription: true,
  isInstruction: false,
  isMedicalRecord1: false,
  isMedicalRecord2: false,
  isOutpatientPrescription: false,
  isHospitalPrescription: false,
  isDrugInformationSheet: false,
  outpatientOption: "3",
};

const listFieldsOrdInf = [
  "inoutKbn",
  "odrKouiKbn",
  "daysCnt",
  "hokenPid",
  "hpId",
  "isDeleted",
  "ptId",
  "raiinNo",
  "rpEdaNo",
  "rpNo",
  "santeiKbn",
  "sikyuKbn",
  "sinDate",
  "sortNo",
  "syohoSbt",
];

const listFieldsOrdInfDetails = [
  "itemCd",
  "hpId",
  "ptId",
  "sinDate",
  "raiinNo",
  "rpNo",
  "rpEdaNo",
  "rowNo",
  "sinKouiKbn",
  "suryo",
  "unitName",
  "termVal",
  "syohoKbn",
  "drugKbn",
  "yohoKbn",
  "kokuji1",
  "kokuji2",
  "isNodspRece",
  "ipnCd",
  "ipnName",
  "cmtOpt",
  "itemName",
  "isDummy",
];

const EPrescriptionContext =
  createContext<EPrescriptionContextType>(defaultContextValue);

export const EPrescriptionProvider: React.FC<Props> = ({ children }) => {
  const { watch } = useFormContext<KarteFormData>();
  const {
    session: { hospitalId },
  } = useSession();
  const { handlePrintPDF } = usePrintSetting();

  const [flagRegister, setFlagRegister] = useState<TFlag>("OFF");

  const [flagCancel, setFlagCancel] = useState<TFlag>(null);

  const [isPendingCheck, setIsPendingCheck] = useState(false);

  const [statePrintSetting, setStatePrintSetting] =
    useState<FormStatePrintSetting>(defaultValueStateKloc);

  const [isClosePageKarte, setIsClosePageKarte] = useState<boolean>(false);

  const [isOpenAccounting, setIsOpenAccounting] = useState<boolean>(false);

  const [isOpenEstimatePaymentModal, setIsOpenEstimatePaymentModal] =
    useState<boolean>(false);

  const [payloadSaveMedical, setPayloadSaveMedical] =
    useState<EmrCloudApiRequestsMedicalExaminationUpsertTodayOdrRequestInput | null>(
      null,
    );

  const [registerResult, setRegisterResult] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);

  const [flagRegisterChange, setFlagRegisterChange] = useState<TFlag | null>(
    null,
  );

  const [prescriptionIdList, setPrescriptionIdList] = useState<
    DomainModelsEpsPrescriptionEpsPrescriptionModel[]
  >([]);

  const [statusModal, setStatusModal] = useState<StatusModalType>(
    StatusModal.NONE,
  );

  const [messageSocket, setMessageSocket] = useState<IMessageSocket>();

  const [errorMessage, setErrorMessage] = useState("");

  const [errorMsg, setErrorMsg] = useState("");

  const [
    dataCheckPrescriptionChangeOrder,
    setDataCheckPrescriptionChangeOrder,
  ] = useState<DataCheckPrescriptionChangeOrderType[]>([]);

  const [listPrescription, setListPrescription] = useState<
    ListPrescriptionType[]
  >([]);

  const [listEpsPrescriptionAllowPrint, setListEpsPrescriptionAllowPrint] =
    useState<
      TypeResponseCheckingDomainModelsEpsPrescriptionEpsPrescriptionModel[]
    >([]);

  const [listDrugBase64SignXml, setListDrugBase64SignXml] = useState<
    ListPrescriptionDrugXml[]
  >([]);

  const [precriptionNew, setPrecriptionNew] =
    useState<EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput>(
      {},
    );

  const [isOpenDuplicateMedicationCheck, setIsOpenDuplicateMedicationCheck] =
    useState(false);

  const [resultDataSocket, setResultDataSocket] =
    useState<IResponse<IFile> | null>(null);

  const [listDrugCSV, setListDrugCSV] = useState<
    {
      refileCount: number;
      prescriptionDocument: string;
    }[]
  >([]);

  const {
    state: { duplicateMedicationCheckOpen },
    handleCloseModal,
    handleOpenModal,
  } = useModal();
  const isCheckHeader = !duplicateMedicationCheckOpen;

  const {
    raiinNo,
    ptId,
    sinDate,
    insuranceDefault,
    dataHeaderInfo,
    insuranceComboList,
  } = useGetOrderInfoContext();

  const { data: dataSystemConf } = useGetApiSystemConfGetListQuery();

  const systemConf = useMemo(() => {
    return dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList || [];
  }, [dataSystemConf]);

  const settingOutpatient =
    systemConf?.find((item) => item?.grpCd === 92003 && item?.grpEdaNo === 0)
      ?.val === 1;

  const orderRpsWatch = watch("orderRps");
  const jikanKbn = watch("jikanKbn");
  const syosaiKbn = watch("syosaiKbn");

  const orderRps = useMemo(
    () =>
      orderRpsWatch.map((orderRp) => {
        return {
          ...orderRp,
          hokenPid: orderRp.hokenPid ?? insuranceDefault?.hokenPid,
        };
      }),
    [orderRpsWatch, insuranceDefault],
  );

  const { dataListPrescription } = useGetRefillData(
    orderRps,
    statePrintSetting,
  );

  const valueUpsertParams = {
    orderRps: orderRps,
    jikanKbn,
    syosaiKbn,
    schemaImages: [],
  };

  const params = convertFormValueToUpsertParams({
    value: valueUpsertParams,
    raiinNo,
    ptId,
    sinDate,
    defaultHokenPid: insuranceDefault?.hokenPid,
  });

  const system = new System("/medical", systemConf);

  const inputPreRegistration = {
    raiinNo,
    ptId,
    sinDate,
    odrInfs: params.odrInfs,
  };

  const { onFinishExamination } = useKarteFooter();

  const { handleError } = useErrorHandler();

  const [postApiEpsGetPreRegistration] =
    usePostApiEpsGetPreRegistrationDataMutation({
      onError: (error) => {
        handleError({ error });
      },
    });
  const [postApiEpsGetOutDrugCsvData] = usePostApiEpsGetOutDrugCsvDataMutation({
    onError: (error) => {
      handleError({ error });
    },
  });

  const [postApiEpsSaveDuplicateMedicationCheck] =
    usePostApiEpsSaveDuplicateMedicationCheckMutation({
      onError: (error) => {
        handleError({ error });
      },
    });
  const [getApiEpsGetDuplicateMedication, { data: dataTable }] =
    useGetApiEpsGetDuplicateMedicationCheckLazyQuery({
      onError: (error) => {
        handleError({ error });
      },
    });

  const { handleUpsertEpsRegister } = useUpsertEpsRegister();
  const [isShowModalCreateRegisterInfo, setIsShowModalCreateRegisterInfo] =
    useState<boolean>(false);

  const handleGetPrescriptionIdList = () => {
    if (raiinNo && ptId) {
      postApiEpsGetPreRegistration({
        variables: {
          input: {
            odrInfs: [],
            statusList: [1, 2],
            raiinNo: String(raiinNo),
            ptId: String(ptId),
          },
        },
        onCompleted: (dataCheck) => {
          const epsPrescriptionModel =
            dataCheck?.postApiEpsGetPreRegistrationData?.data
              ?.preRegistrationCheckingModel?.epsPrescriptionModel;

          if (epsPrescriptionModel?.length) {
            setPrescriptionIdList(epsPrescriptionModel);
            handleCloseModal("CREATE_REGISTER_INFO");
            handleOpenModal("CREATE_REGISTER_INFO");
          } else {
            handleCloseModal("CREATE_REGISTER_INFO");
            handleOpenModal("CREATE_REGISTER_INFO");
            setIsShowModalCreateRegisterInfo(true);
          }
        },
        onError: (error) => {
          handleError({ error }).then();
        },
      });
    }
  };

  const handleChangeStatusModal = (status: StatusModalType) => {
    setStatusModal(status);
  };

  const medicalSupportOptionVal = useMemo(() => {
    return systemConf.find(
      (item) => item.grpCd === 100029 && item.grpEdaNo === 100,
    )?.val;
  }, [systemConf]);

  const isDisableAcquisitionDrug = useMemo(() => {
    return (
      systemConf?.find((item) => item.grpCd === 100040 && item.grpEdaNo === 7)
        ?.val === 1
    );
  }, [systemConf]);

  const sameMedicalInstitutionAlertFlg = useMemo(() => {
    const val = systemConf?.find(
      (item) => item.grpCd === 100040 && item.grpEdaNo === 2,
    )?.val;
    if (val === 1) return 2;
    return 1;
  }, [systemConf]);

  const secondActive = useMemo(() => {
    return (
      systemConf.find((item) => item.grpCd === 100040 && item.grpEdaNo === 4)
        ?.val || 0
    );
  }, [systemConf]);

  const getErrorMessage = (
    data?: DomainModelsEpsPreRegistrationCheckingModel,
  ) => {
    if (orderRps.length === 0) {
      return "処方がオーダーされていません。";
    }
    if (data) {
      const ptHokenInfoStartDate = data?.odrInfs?.every(
        (item) =>
          item?.ptHokenPatternModel?.startDate &&
          sinDate &&
          item?.ptHokenPatternModel?.startDate > Number(sinDate),
      );
      const ptHokenInfoEndDate = data?.odrInfs?.every(
        (item) =>
          item?.ptHokenPatternModel?.endDate &&
          sinDate &&
          item?.ptHokenPatternModel?.endDate < Number(sinDate),
      );

      const hokenSbtCd = data?.odrInfs?.every(
        (item) =>
          item?.ptHokenPatternModel?.hokenSbtCd?.toString().slice(0, 1) === "5",
      );

      const allKohiCombineHoken = data?.odrInfs?.every((item) =>
        item?.kohiInfModel?.every(
          (i) => i.futansyaNo?.toString().slice(0, 2) === "12",
        ),
      );
      if (
        ptHokenInfoStartDate ||
        ptHokenInfoEndDate ||
        (medicalSupportOptionVal === 1 && hokenSbtCd && allKohiCombineHoken) ||
        (medicalSupportOptionVal !== 1 && hokenSbtCd)
      ) {
        return "有効な被保険者番号を確認できません。";
      }
      const allHokenKbn = data?.odrInfs?.every(
        (item) =>
          item?.ptHokenPatternModel?.hokenKbn !== 1 &&
          item?.ptHokenPatternModel?.hokenKbn !== 2,
      );
      if (allHokenKbn) {
        return "医療保険適用外の診療です。";
      }
      if (
        data.epsPrescriptionModel?.some((item) =>
          item?.epsDispensingModel?.some(
            (i) =>
              i.resultType === 1 || i.resultType === 3 || i.resultType === 4,
          ),
        )
      ) {
        return "薬局で受付済みです。";
      }
    }

    return;
  };

  const handleBackKarte = () => {
    handleChangeStatusModal(StatusModal.NONE);
    setResultDataSocket(null);
    handleCloseModal("CHECK_BEFORE_REGISTER");
    handleCloseModal("DUPLICATE_MEDICATION_CHECK");
  };

  const handleUpdateKarte = async () => {
    const isError = await onFinishExamination();
    if (!isError) {
      handleBackKarte();
    }
    return !isError;
  };

  const handleUpdateKarteFlagOff = () => {
    if (!statePrintSetting.isOutpatientPrescription) {
      setFlagCancel("ON");
      //TODO: Step 6
    }
    setFlagRegister("OFF");

    handleUpdateKarte();
  };

  const onSkipChecksDuplicateMedication = () => {
    setFlagRegister("OFF");
    handleUpdateKarte();
    //TODO: step 6
  };
  const onConfirmSkipChecksDuplicateMedication = async () => {
    setFlagRegister("OFF");
    setFlagCancel("ON");
    handleUpdateKarte();
  };
  const epsChkListData =
    dataTable?.getApiEpsGetDuplicateMedicationCheck?.data?.epsChkList || [];

  const onUpdateSocket = () => {
    handleUpdateSocket(resultDataSocket, messageSocket);
  };

  const handleUpdateSocket = (
    files: IResponse<IFile> | null,
    messageBody?: IMessageSocket,
  ) => {
    if (files?.data?.fileName?.includes(".err")) {
      const error = extractBracketedText(files?.data?.content ?? "").code ?? "";
      const error2 =
        extractBracketedText(files?.data?.content ?? "").message ?? "";
      setErrorMsg(`処理結果コード: ${error}\n${error2}`);
      if (isCheckHeader) {
        handleChangeStatusModal(StatusModal.ERROR_PROCESSING);
        return;
      }
      handleChangeStatusModal(StatusModal.ERROR_OFF_PRESCRIPTION);
      return;
    }
    const xmlContent = JSON.parse(files?.data?.content || "");

    const processingResultStatus =
      xmlContent?.XmlMsg?.MessageBody?.ProcessingResultStatus;
    const segmentOfResult = xmlContent?.XmlMsg?.MessageHeader?.SegmentOfResult;

    const processingResultCode =
      xmlContent?.XmlMsg?.MessageBody?.ProcessingResultCode || "";

    if (
      ((processingResultStatus &&
        Number(processingResultStatus) === 2 &&
        segmentOfResult &&
        Number(segmentOfResult) === 1) ||
        (segmentOfResult && Number(segmentOfResult) === 9)) &&
      processingResultCode !== "EPSB1010W"
    ) {
      const msgBody = xmlContent?.XmlMsg?.MessageBody;
      const errorMsg = xmlContent?.XmlMsg?.MessageHeader?.ErrorMessage || "";
      const errorCode = xmlContent?.XmlMsg?.MessageHeader?.ErrorCode || "";
      const processingResultMessage =
        xmlContent?.XmlMsg?.MessageBody?.ProcessingResultMessage || "";
      const csvCheckResultBody =
        xmlContent?.XmlMsg?.MessageBody?.CsvCheckResultBody;
      const resultCode =
        xmlContent?.XmlMsg?.MessageBody?.CsvCheckResultBody?.CsvCheckResultList
          ?.ResultCode || "";
      const resultMessage =
        xmlContent?.XmlMsg?.MessageBody?.CsvCheckResultBody?.CsvCheckResultList
          ?.ResultMessage || "";
      let error = "";
      if (!msgBody) {
        error = `エラーコード: ${errorCode}\n${errorMsg}\n`;
      } else if (msgBody) {
        error = `処理結果コード: ${processingResultCode}\n${processingResultMessage}\n`;
      }
      let error2 = "";
      if (csvCheckResultBody) {
        error2 = `チェック結果コード: ${resultCode}\n${resultMessage}`;
      }
      setErrorMsg(`${error}${error2}`);
      if (isCheckHeader) {
        handleChangeStatusModal(StatusModal.ERROR_PROCESSING);
        return;
      }
      handleChangeStatusModal(StatusModal.ERROR_OFF_PRESCRIPTION);
      return;
    }

    const checkResultList =
      xmlContent?.XmlMsg?.MessageBody?.CheckResultBody?.CheckResultList
        ?.CheckResult;
    const normalizedCheckResultList = Array.isArray(checkResultList)
      ? checkResultList
      : checkResultList
        ? [checkResultList]
        : [];

    const dataSaveDuplicateConfirmOnline = {
      checkResult: normalizedCheckResultList?.length ? 0 : 2,
      drugInfo: messageBody?.PrescriptionInfo,
      oralBrowsingConsent: messageBody?.OralBrowsingConsent,
      ptId: String(ptId),
      raiinNo: String(raiinNo),
      seqNo: 0,
      sinDate: Number(sinDate),
      epsChkDetailList:
        normalizedCheckResultList?.length > 0
          ? // eslint-disable-next-line @typescript-eslint/no-explicit-any
            normalizedCheckResultList?.map((CheckResult: any) => ({
              message: CheckResult?.Message || "",
              messageCategory: CheckResult?.MessageCategory || "",
              messageId: CheckResult?.MessageID || "",
              pastDate: CheckResult?.PastDate || "",
              pastDispensingQuantity: CheckResult?.PastDispensingQuantity || "",
              pastInsurancePharmacyName:
                CheckResult?.PastInsurancePharmacyName || "",
              pastMedicalInstitutionName:
                CheckResult?.PastMedicalInstitutionName || "",
              pastPharmaceuticalCode: CheckResult?.PastPharmaceuticalCode || "",
              pastPharmaceuticalCodeType:
                CheckResult?.PastPharmaceuticalCodeType || "",
              pastPharmaceuticalName: CheckResult?.PastPharmaceuticalName || "",
              pharmaceuticalsIngredientName:
                CheckResult?.PharmaceuticalsIngredientName || "",
              ptId: ptId || "",
              pastUsage: CheckResult?.PastUsage || "",
              raiinNo: raiinNo || "",
              targetDispensingQuantity:
                CheckResult?.TargetDispensingQuantity || "",
              targetDosageForm: CheckResult?.TargetDosageForm || "",
              targetPharmaceuticalCode:
                CheckResult?.TargetPharmaceuticalCode || "",
              targetPharmaceuticalCodeType:
                CheckResult?.TargetPharmaceuticalCodeType || "",
              targetPharmaceuticalName:
                CheckResult?.TargetPharmaceuticalName || "",
              targetUsage: CheckResult?.TargetUsage || "",
              pastDosageForm: CheckResult?.PastDosageForm || "",
              comment: "",
            }))
          : [],
    };

    // const arbitraryFileIdentifier =
    //   xmlContent?.XmlMsg?.MessageHeader?.ArbitraryFileIdentifier;

    // postApiEpsUpsertEpsRegister({
    //   variables: {
    //     input: {
    //       arbitraryFileIdentifier,
    //       // dateSeqNo: "0",
    //       dispensingResultId: "",
    //       raiinNo: raiinNo,
    //       prescriptionId: "",
    //       ptId: ptId,
    //       sinDate: sinDate,
    //       reqType: 2,
    //       status: 1,
    //       resultCode: "",
    //       resultMessage: "",
    //       result: "",
    //       reqDate: +dayjs().format("YYYYMMDD"),
    //     },
    //   },
    // });

    onSaveDuplicateMedicationCheck(
      dataSaveDuplicateConfirmOnline,
      !normalizedCheckResultList?.length,
    );
  };

  const { generatePayloadUpsertInsurance } = useReadFileXML();

  // js docs
  /**
   * @description Update eps register
   * @param registerResult - register result
   * @param createFileResult - create file result
   * @param reqType - request type 1 cancel, 2 done
   */
  const updateEpsRegister = useCallback(
    async (
      registerResult: DomainModelsEpsReqEpsReqModel,
      createFileResult: IResponse<IFile> | null,
    ) => {
      const payloadUpsertInsurance = generatePayloadUpsertInsurance(
        registerResult,
        createFileResult,
      );

      await handleUpsertEpsRegister({
        ...payloadUpsertInsurance,
      });
    },
    [generatePayloadUpsertInsurance, handleUpsertEpsRegister],
  );

  const onSocket = async (messageBody: IMessageSocket) => {
    handleChangeStatusModal(StatusModal.LOADING);
    const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${v4()}`;

    const registerResult = await handleUpsertEpsRegister({
      arbitraryFileIdentifier,
      dispensingResultId: "",
      raiinNo: raiinNo,
      prescriptionId: "",
      ptId: ptId,
      sinDate: sinDate,
      reqType: 2,
      status: 1,
      resultCode: "",
      resultMessage: "",
      result: "",
      reqDate: +dayjs().format("YYYYMMDD"),
    });

    const [createFileResult] = await Promise.all([
      system.createFile(
        {
          messageHeader: { ArbitraryFileIdentifier: arbitraryFileIdentifier },
          messageBody,
        },
        "EPSsiDMP02req",
      ),
    ]);

    if (createFileResult) {
      system.disconnect();
      if (registerResult) {
        setRegisterResult(registerResult);
        await updateEpsRegister(registerResult, createFileResult);
      }

      if (isCheckHeader) {
        handleUpdateSocket(createFileResult, messageBody);
      } else {
        setResultDataSocket(createFileResult);
      }
    }

    // await postApiEpsUpsertEpsRegister({
    //   variables: {
    //     input: {
    //       arbitraryFileIdentifier,
    //       dispensingResultId: "",
    //       raiinNo: raiinNo,
    //       prescriptionId: "",
    //       ptId: ptId,
    //       sinDate: sinDate,
    //       reqType: 2,
    //       status: 1,
    //       resultCode: "",
    //       resultMessage: "",
    //       result: "",
    //       reqDate: +dayjs().format("YYYYMMDD"),
    //     },
    //   },
    // });
  };

  const onSaveDuplicateMedicationCheck = (
    dataSaveDuplicateConfirmOnline?: EmrCloudApiRequestsEpsSaveDuplicateMedicationCheckRequestInput,
    isNormalizedCheckResultLength?: boolean,
  ) => {
    setResultDataSocket(null);
    postApiEpsSaveDuplicateMedicationCheck({
      variables: {
        input: dataSaveDuplicateConfirmOnline,
      },
      onCompleted: async (dataCheck) => {
        if (isNormalizedCheckResultLength && isCheckHeader) {
          handleChangeStatusModal(StatusModal.INFO_MEDICAL);
          return;
        }
        if (isNormalizedCheckResultLength) {
          const isError = await onFinishExamination();
          if (!isError) {
            const statePrint = {
              ...statePrintSetting,
              isOutpatientPrescription: false,
            };
            await handlePrintPDF(statePrint);
            handleChangeStatusModal(StatusModal.NONE);
            setResultDataSocket(null);
            handleCloseModal("CHECK_BEFORE_REGISTER");
            handleGetPrescriptionIdList();
          }

          return;
        }
        if (dataCheck.postApiEpsSaveDuplicateMedicationCheck?.data) {
          getApiEpsGetDuplicateMedication({
            variables: {
              ptId: String(ptId),
              raiinNo: String(raiinNo),
              sinDate: Number(sinDate),
            },
            onCompleted: () => setStatusModal(StatusModal.RESULT),
          });
        }
      },
    });
  };

  const handleConsentPatient = () => {
    const newMessageSocket = { ...messageSocket, OralBrowsingConsent: 1 };
    setMessageSocket(newMessageSocket);
    onSocket(newMessageSocket);
  };

  const createDrugCsvData = async ({
    refileCount,
    data,
  }: {
    refileCount: number;
    data: OrderRp[];
  }) => {
    const inputData = formatGetOutDrugCsv({
      refileCount,
      data,
      ptId: String(ptId),
      raiinNo: String(raiinNo),
      sinDate: Number(sinDate),
      fileType: 3,
    });

    const responseGetOutDrugCsvData = await postApiEpsGetOutDrugCsvData({
      variables: {
        input: inputData,
      },
      onError: (error) => {
        handleError({ error }).then();
        console.log("vao case scv lỗi");
        setIsOpenDuplicateMedicationCheck(false);
        handleCloseModal("DUPLICATE_MEDICATION_CHECK");
      },
    });

    if (
      responseGetOutDrugCsvData.data?.postApiEpsGetOutDrugCsvData?.data
        ?.prescriptionDocument?.length === 0
    ) {
      handleChangeStatusModal(StatusModal.ERROR_FILE_NONE);
      return;
    }

    await getApiEpsGetDuplicateMedication({
      variables: {
        ptId: String(ptId),
        raiinNo: String(raiinNo),
        sinDate: Number(sinDate),
      },
    });

    const isCheckConnectionSocket = await checkConnectionSocket({});

    if (!isCheckConnectionSocket) {
      handleOpenModal("CONNECTION_SOCKET");
      return;
    }

    const messageBody = {
      PrescriptionInfo:
        responseGetOutDrugCsvData?.data?.postApiEpsGetOutDrugCsvData?.data
          ?.prescriptionDocument,
      SameMedicalInstitutionAlertFlg: sameMedicalInstitutionAlertFlg,
      OralBrowsingConsent: 0,
    };
    setMessageSocket(messageBody);
    await onSocket(messageBody);

    // postApiEpsGetOutDrugCsvData({
    //   variables: {
    //     input: inputData,
    //   },
    //   onCompleted: (res) => {
    //     if (
    //       res.postApiEpsGetOutDrugCsvData?.data?.prescriptionDocument
    //         ?.length === 0
    //     ) {
    //       handleChangeStatusModal(StatusModal.ERROR_FILE_NONE);
    //       return;
    //     }
    //     getApiEpsGetDuplicateMedication({
    //       variables: {
    //         ptId: String(ptId),
    //         raiinNo: String(raiinNo),
    //         sinDate: Number(sinDate),
    //       },
    //       onCompleted: async () => {
    //         const isCheckConnectionSocket = await checkConnectionSocket({});
    //
    //         if (!isCheckConnectionSocket) {
    //           handleOpenModal("CONNECTION_SOCKET");
    //           return;
    //         }
    //
    //         const messageBody = {
    //           PrescriptionInfo:
    //             res.postApiEpsGetOutDrugCsvData?.data?.prescriptionDocument,
    //           SameMedicalInstitutionAlertFlg: sameMedicalInstitutionAlertFlg,
    //           OralBrowsingConsent: 0,
    //         };
    //         setMessageSocket(messageBody);
    //         onSocket(messageBody);
    //       },
    //     });
    //   },
    //   onError: (error) => {
    //     handleError({ error });
    //   },
    // });
  };

  const onClickDuplicate = async () => {
    setIsOpenDuplicateMedicationCheck(true);
    // const responseGetPreRegistration = await postApiEpsGetPreRegistration({
    //   variables: {
    //     input: inputPreRegistration,
    //   },
    // });
    //
    // if (isCheckHeader) {
    //   const errorMsg = getErrorMessage(
    //     responseGetPreRegistration?.data?.postApiEpsGetPreRegistrationData?.data
    //       ?.preRegistrationCheckingModel,
    //   );
    //
    //   if (errorMsg) {
    //     setErrorMessage(errorMsg);
    //     handleChangeStatusModal(StatusModal.ERROR);
    //     return;
    //   }
    // }

    // await createDrugCsvData({ refileCount: 1, data: orderRps });

    postApiEpsGetPreRegistration({
      variables: { input: inputPreRegistration },
      onCompleted: (data) => {
        if (isCheckHeader) {
          const errorMsg = getErrorMessage(
            data.postApiEpsGetPreRegistrationData?.data
              ?.preRegistrationCheckingModel,
          );

          if (errorMsg) {
            setErrorMessage(errorMsg);
            handleChangeStatusModal(StatusModal.ERROR);
            return;
          }
        }
        createDrugCsvData({ refileCount: 1, data: orderRps });
      },
      onError: (error) => {
        handleError({ error });
        setIsOpenDuplicateMedicationCheck(false);
        handleCloseModal("DUPLICATE_MEDICATION_CHECK");
      },
    });
  };

  const [flow, setFlow] = useState<"1" | "2">("1");

  const { data: systemConfigList } = useGetApiSystemConfGetListQuery({
    onError: (error) => {
      handleError({ error }).then();
    },
  });

  const systemConfigData = useMemo(() => {
    return systemConfigList?.getApiSystemConfGetList?.data?.systemConfList?.reduce(
      (
        acc: Record<number, Record<number, typeof systemSetting>>,
        systemSetting,
      ) => {
        const groupCode = systemSetting.grpCd ?? 0;
        const groupEdaNo = systemSetting.grpEdaNo ?? 0;

        if (!acc[groupCode]) {
          acc[groupCode] = {};
        }

        acc[groupCode][groupEdaNo] = systemSetting;

        return acc;
      },
      {} as Record<
        number,
        Record<number, EmrCloudApiResponsesSystemConfSystemConfDto[][number]>
      >,
    );
  }, [systemConfigList]);

  const orders = params?.odrInfs?.map((info) => {
    const odrDetails = info.odrDetails?.map((item) => ({
      hpId: hospitalId,
      ptId: item.ptId?.toString() ?? "",
      sinDate: Number(item.sinDate),
      raiinNo: item.raiinNo,
      rpNo: item.rpNo,
      rpEdaNo: item.rpEdaNo,
      rowNo: item.rowNo,
      sinKouiKbn: item.sinKouiKbn,
      itemCd: item.itemCd,
      suryo: item.suryo,
      unitName: item.unitName,
      termVal: item.termVal,
      syohoKbn: item.syohoKbn,
      drugKbn: item.drugKbn,
      yohoKbn: item.yohoKbn,
      kokuji1: item.kokuji1,
      kokuji2: item.kokuji2,
      isNodspRece: item.isNodspRece,
      ipnCd: item.ipnCd,
      ipnName: item.ipnName,
      cmtOpt: item.cmtOpt,
      itemName: item.itemName,
      isDummy: false,
    }));

    return {
      odrKouiKbn: info.odrKouiKbn,
      daysCnt: info.daysCnt,
      hokenPid: info.hokenPid,
      inoutKbn: info.inoutKbn,
      isDeleted: info.isDeleted,
      hpId: hospitalId,
      odrDetails,
      ptId: info.ptId?.toString() ?? "",
      raiinNo: info.raiinNo,
      rpEdaNo: info.rpEdaNo,
      rpNo: info.rpNo,
      santeiKbn: info.santeiKbn,
      sikyuKbn: info.sikyuKbn,
      sinDate: Number(info.sinDate),
      sortNo: info.sortNo,
      syohoSbt: info.syohoSbt,
      detailInfoList: odrDetails,
    };
  });

  const createOdrInf = (
    checkOrder: DomainModelsMedicalExaminationCheckedOrderModel,
    checkOrders: DomainModelsMedicalExaminationCheckedOrderModel[],
  ): ITodayOrderInfo => {
    const newCheckOrder = omit(checkOrder, [
      "checkingContent",
      "checkingType",
      "checkingTypeDisplay",
      "inOutKbn",
      "santei",
      "isEnableSantei",
    ]);

    const orderDetails = [
      {
        ...createTodayOrderDetail(),
        ...newCheckOrder,
        uniqId: v4(),
        // hpId: 0,
      },
    ];

    if (checkOrder.itemCd === ItemCdConst.yakuzaiJoho) {
      const yakuzaiJohoTeiyo = checkOrders.find(
        (item) => item.itemCd === ItemCdConst.yakuzaiJohoTeiyo,
      );

      if (yakuzaiJohoTeiyo) {
        orderDetails.push({
          ...createTodayOrderDetail(),
          ...newCheckOrder,
          uniqId: v4(),
          // hpId: 0,
        });
      }
    }

    return {
      ...createTodayOrderInfo(),
      odrKouiKbn: checkOrder.sinKouiKbn ?? 0,
      odrDetails: orderDetails,
      uniqId: v4(),
    };
  };

  const getSystemSetting = useCallback(
    ({
      grpCd,
      grpEdaNo = 0,
      defaultValue = 0,
      fromLastestDb = false,
    }: {
      grpCd: number;
      grpEdaNo?: number;
      defaultValue?: number;
      fromLastestDb?: boolean;
    }) => {
      const systemConfigGroup = systemConfigData?.[grpCd];

      if (!systemConfigGroup) return defaultValue;

      if (fromLastestDb) {
        const maxKey = Math.max(...Object.keys(systemConfigGroup).map(Number));
        return systemConfigGroup[maxKey]?.val ?? defaultValue;
      }
      return systemConfigGroup?.[grpEdaNo]?.val ?? defaultValue;
    },
    [systemConfigData],
  );

  const getCheckAge = getSystemSetting({ grpCd: 2017 });

  const convertTodayHeaderInfoOdrDetails = (
    ordInfDetails: OrdInfDetail[],
  ): ITodayOrderDetail[] => {
    return ordInfDetails.map((ordInfDetail) => {
      const todayOrderDetail = createTodayOrderDetail();
      const keys = Object.keys(todayOrderDetail);
      return Object.assign(todayOrderDetail, _.pick(ordInfDetail, keys));
    });
  };

  const convertTodayHeaderInfoType = (data: Data): ITodayOrderInfo => {
    const todayOrderInfo = createTodayOrderInfo();
    todayOrderInfo.odrDetails = convertTodayHeaderInfoOdrDetails(
      data.odrInfs?.ordInfDetails ?? [],
    );

    const keys = Object.keys(todayOrderInfo);
    return Object.assign(todayOrderInfo, _.pick(data.odrInfs, keys));
  };

  const defaultOrderInf = useMemo(() => {
    if (
      dataHeaderInfo?.odrInfs?.ordInfDetails &&
      dataHeaderInfo?.odrInfs?.ordInfDetails?.length > 0
    ) {
      const updatedDetails = dataHeaderInfo.odrInfs.ordInfDetails?.map(
        (detail) => {
          if (detail.itemCd === ItemCdConst.syosaiKihon) {
            return {
              ...detail,
              hpId: hospitalId,
              suryo: syosaiKbn ?? 0,
            };
          }
          if (detail.itemCd === ItemCdConst.jikanKihon) {
            return {
              ...detail,
              hpId: hospitalId,
              suryo: jikanKbn ?? 0,
            };
          }
          return detail;
        },
      );

      return {
        ...dataHeaderInfo.odrInfs,
        hpId: hospitalId,
        ptId: String(dataHeaderInfo.odrInfs.ptId),
        detailInfoList: updatedDetails,
        ordInfDetails: undefined,
      };
    } else {
      if (!dataHeaderInfo) return {};
      const headerOdrInf = convertTodayHeaderInfoType(dataHeaderInfo);

      const sinInfDetail: ITodayOrderDetail = {
        ...createTodayOrderDetail(),
        hpId: hospitalId,
        raiinNo: Number(raiinNo),
        rpNo: 1,
        rpEdaNo: 1,
        rowNo: 1,
        sinKouiKbn: 10,
        ptId: Number(ptId) || 0,
        sinDate: sinDate,
        itemCd: ItemCdConst.syosaiKihon,
        itemName: "診察料基本点数算定用",
        suryo: syosaiKbn ?? 0,
        syohoKbn: 2,
      };

      const jikanInfDetail: ITodayOrderDetail = {
        ...createTodayOrderDetail(),
        hpId: hospitalId,
        raiinNo: Number(raiinNo),
        rpNo: 1,
        rpEdaNo: 1,
        rowNo: 2,
        sinKouiKbn: 10,
        ptId: Number(ptId) || 0,
        sinDate: sinDate,
        itemCd: ItemCdConst.jikanKihon,
        itemName: "時間外算定用",
        suryo: jikanKbn ?? 0,
        syohoKbn: 2,
      };

      headerOdrInf.raiinNo = Number(raiinNo);
      headerOdrInf.rpNo = Number(1);
      headerOdrInf.rpEdaNo = Number(1);
      headerOdrInf.ptId = Number(ptId) || 0;
      headerOdrInf.sinDate = sinDate;
      headerOdrInf.odrKouiKbn = Number(10);
      headerOdrInf.daysCnt = Number(1);
      headerOdrInf.hokenPid = dataHeaderInfo.hokenPid ?? 0;

      headerOdrInf.odrDetails = [sinInfDetail, jikanInfDetail];

      const orderDetailNew = headerOdrInf.odrDetails.map((detail) =>
        _.pick(detail, listFieldsOrdInfDetails),
      );
      const orderInfNew = _.pick(headerOdrInf, listFieldsOrdInf);
      return {
        ...orderInfNew,
        detailInfoList: orderDetailNew,
        raiinNo: String(headerOdrInf.raiinNo),
        rpEdaNo: String(headerOdrInf.rpEdaNo),
        rpNo: String(headerOdrInf.rpNo),
        ptId: String(headerOdrInf.ptId),
        hpId: hospitalId,
      };
    }
  }, [dataHeaderInfo, hospitalId, syosaiKbn, jikanKbn, raiinNo, ptId, sinDate]);

  // useEffect(() => {
  //   const checkAuditTrail = async () => {
  //     try {
  //       const { data } = await getApiMedicalExaminationGetKensaAuditTrailLog({
  //         variables: {
  //           eventCd: "97024000000",
  //           ptId: ptId,
  //           raiinNo: raiinNo,
  //           sinDate: Number(sinDate),
  //           isOperator: 0,
  //           operatorName: "",
  //         },
  //       });

  //       const auditTrailLogItems =
  //         data?.getApiMedicalExaminationGetKensaAuditTrailLog?.data
  //           ?.auditTrailLogItems || [];
  //       setHasAuditTrailItems(auditTrailLogItems.length > 1);
  //     } catch (error) {
  //       console.error("Error fetching audit trail log:", error);
  //       setHasAuditTrailItems(false);
  //     }
  //   };

  //   checkAuditTrail();
  // }, [ptId, raiinNo, sinDate, getApiMedicalExaminationGetKensaAuditTrailLog]);

  // const defaultCheckBox = useMemo(() => {
  //   const isHasChangeKarte = hasChangeKarte();
  //   const hasRelate = getSystemSetting({ grpCd: 92003, grpEdaNo: 0 });
  //   const hasOutpatient = orderRpsWatch.some((item) => item?.inoutKbn === 1);

  //   return (
  //     !isHasChangeKarte &&
  //     (hasRelate === 1 || hasRelate === 2) &&
  //     hasOutpatient &&
  //     hasAuditTrailItems
  //   );
  // }, [hasChangeKarte, orderRpsWatch, getSystemSetting, hasAuditTrailItems]);

  const value = useMemo(
    () => ({
      statePrintSetting,
      setStatePrintSetting,
      payloadSaveMedical,
      setPayloadSaveMedical,
      listPrescription,
      settingOutpatient,
      setDataCheckPrescriptionChangeOrder,
      dataCheckPrescriptionChangeOrder,
      dataListPrescription,
      listEpsPrescriptionAllowPrint,
      setListEpsPrescriptionAllowPrint,
      listDrugBase64SignXml,
      setListDrugBase64SignXml,
      setListPrescription,
      setPrecriptionNew,
      precriptionNew,
      listDrugCSV,
      setListDrugCSV,
      systemConf,
      flagRegisterChange,
      setFlagRegisterChange,
      flagRegister,
      flagCancel,
      setFlagRegister,
      setFlagCancel,
      epsChkListData,
      statusModal,
      errorMessage,
      errorMsg,
      isDisableAcquisitionDrug,
      handleChangeStatusModal,
      setErrorMessage,
      handleBackKarte,
      handleUpdateKarte,
      handleUpdateKarteFlagOff,
      onClickDuplicate,
      isOpenDuplicateMedicationCheck,
      secondActive,
      handleConsentPatient,
      isPendingCheck,
      setIsPendingCheck,
      onSkipChecksDuplicateMedication,
      onConfirmSkipChecksDuplicateMedication,
      resultDataSocket,
      onUpdateSocket,
      setPrescriptionIdList,
      prescriptionIdList,
      isShowModalCreateRegisterInfo,
      setIsShowModalCreateRegisterInfo,
      handleGetPrescriptionIdList,
      flow,
      setFlow,
      orders:
        orders as UseCaseMedicalExaminationGetCheckedOrderOdrInfItemInput[],
      sinDate: Number(sinDate),
      ptId: ptId,
      raiinNo,
      createOdrInf,
      params: params,
      insuranceDefault,
      dataHeaderInfo,
      insuranceComboList,
      getCheckAge,
      defaultOrderInf,
      syosaiKbn,
      getSystemSetting,
      updateEpsRegister,
      registerResult,
      setRegisterResult,
      isClosePageKarte,
      setIsClosePageKarte,
      isOpenAccounting,
      setIsOpenAccounting,
      isOpenEstimatePaymentModal,
      setIsOpenEstimatePaymentModal,
    }),
    [
      statePrintSetting,
      payloadSaveMedical,
      listPrescription,
      settingOutpatient,
      dataCheckPrescriptionChangeOrder,
      dataListPrescription,
      listDrugBase64SignXml,
      listEpsPrescriptionAllowPrint,
      setListPrescription,
      setPrecriptionNew,
      precriptionNew,
      listDrugCSV,
      setListDrugCSV,
      systemConf,
      flagRegisterChange,
      setFlagRegisterChange,
      flagRegister,
      flagCancel,
      setFlagRegister,
      setFlagCancel,
      epsChkListData,
      statusModal,
      errorMessage,
      errorMsg,
      isDisableAcquisitionDrug,
      handleChangeStatusModal,
      setErrorMessage,
      handleBackKarte,
      handleUpdateKarte,
      handleUpdateKarteFlagOff,
      onClickDuplicate,
      isOpenDuplicateMedicationCheck,
      secondActive,
      handleConsentPatient,
      isPendingCheck,
      setIsPendingCheck,
      onSkipChecksDuplicateMedication,
      onConfirmSkipChecksDuplicateMedication,
      resultDataSocket,
      onUpdateSocket,
      setPrescriptionIdList,
      prescriptionIdList,
      isShowModalCreateRegisterInfo,
      setIsShowModalCreateRegisterInfo,
      handleGetPrescriptionIdList,
      flow,
      setFlow,
      orders,
      sinDate,
      ptId,
      raiinNo,
      createOdrInf,
      params,
      insuranceDefault,
      dataHeaderInfo,
      insuranceComboList,
      getCheckAge,
      defaultOrderInf,
      syosaiKbn,
      getSystemSetting,
      updateEpsRegister,
      registerResult,
      setRegisterResult,
      isClosePageKarte,
      setIsClosePageKarte,
      isOpenAccounting,
      setIsOpenAccounting,
      isOpenEstimatePaymentModal,
      setIsOpenEstimatePaymentModal,
    ],
  );
  return (
    <EPrescriptionContext.Provider value={value}>
      {children}
    </EPrescriptionContext.Provider>
  );
};

export const useEPrescriptionContext = () => {
  const context = useContext(EPrescriptionContext);
  if (!context) {
    throw new Error(
      "useEPrescriptionContext must be used within a EPrescriptionContextProvider",
    );
  }
  return context;
};
