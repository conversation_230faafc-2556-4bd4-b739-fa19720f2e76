import { useEffect, useState } from "react";

import { Connection, System } from "@/utils/socket-helper";
import {
  REN<PERSON>I_MEDICAL_ACCOUNTING_UPDATE_ORDER,
  RENKEI_MEDICAL_DISPLAY_END,
  RENKEI_MEDICAL_END_CONS_UPDATE_ORDER,
  RENKEI_MEDICAL_PRINT_UPDATE_ORDER,
  RENKEI_MEDICAL_SAVE_UPDATE_ORDER,
  RENKEI_MEDICAL_UPDATE_ORDER,
} from "@/constants/renkei";
import { checkConnectionSocket } from "@/utils/socket-helper/connection";

import type { GotoMedicalFrom } from "@/constants/renkei";

type RenkeiParams = {
  ptId: number;
  sinDate: number;
  raiinNo: number;
  eventCd?: string;
  misyu?: number;
  nyukinDate?: number;
  nyukin?: number;
  nyukinSortNo?: number;
};

type KensaIraiParams = {
  ptId: number;
  sinDate: number;
  raiinNo: number;
  eventCd?: string;
};

export const useRenkeiCloseMedical = () => {
  const renkeiConnection = new Connection("/renkei");

  useEffect(() => {
    void checkConnectionSocket({
      onSuccess: () => renkeiConnection.connect(),
    });
  }, []);

  const send = async (params: RenkeiParams) => {
    if (!params.eventCd || !renkeiConnection.isConnected()) return false;
    try {
      await renkeiConnection.request("Execute", {
        ...params,
      });
      return true;
    } catch (e) {
      return false;
    }
  };

  const sendRenkeiCloseMedical = (
    defaultParams: Pick<RenkeiParams, "ptId" | "sinDate" | "raiinNo">,
    gotoFrom: string,
  ) => {
    void send({
      ...defaultParams,
      eventCd: RENKEI_MEDICAL_DISPLAY_END["default"],
    });
    void send({
      ...defaultParams,
      eventCd: RENKEI_MEDICAL_DISPLAY_END[gotoFrom],
    });
  };

  useEffect(() => {
    return () => {
      if (renkeiConnection.isConnected()) {
        renkeiConnection.disconnect();
      }
    };
  }, []);

  return {
    sendRenkeiCloseMedical,
  };
};

export const useRenkei = () => {
  const renkeiSystem = new System("/renkei");
  const kensaIraiSystem = new System("/kensairai");

  const [agentAvailable, setAgentAvailable] = useState<boolean>(false);

  useEffect(() => {
    void checkConnectionSocket({
      onSuccess: () => setAgentAvailable(true),
      onError: () => setAgentAvailable(false),
    });
  }, []);

  const sendRenkei = async (params: RenkeiParams) => {
    if (!params.eventCd || !agentAvailable) return false;
    try {
      await renkeiSystem.renkei({
        ...params,
        nyukinDate: params.nyukinDate ?? 0,
        nyukinSortNo: params.nyukinSortNo ?? 0,
      });
      return true;
    } catch (e) {
      return false;
    }
  };

  const sendKensaIrai = async (params: KensaIraiParams) => {
    if (!params.eventCd || !agentAvailable) return false;
    try {
      await kensaIraiSystem.renkei(params);
      return true;
    } catch (e) {
      return false;
    }
  };

  const sendUpdateOrderEvents = async (
    initParams: Omit<KensaIraiParams, "eventCd">,
    gotoFrom: GotoMedicalFrom,
  ) => {
    await Promise.allSettled([
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_UPDATE_ORDER["default"],
      }),
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_UPDATE_ORDER[gotoFrom],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_UPDATE_ORDER["default"],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_UPDATE_ORDER[gotoFrom],
      }),
    ]);
  };

  const sendSaveEvents = async (
    initParams: Omit<KensaIraiParams, "eventCd">,
    gotoFrom: GotoMedicalFrom,
  ) => {
    await sendUpdateOrderEvents(initParams, gotoFrom);

    await Promise.allSettled([
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_SAVE_UPDATE_ORDER["default"],
      }),
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_SAVE_UPDATE_ORDER[gotoFrom],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_SAVE_UPDATE_ORDER["default"],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_SAVE_UPDATE_ORDER[gotoFrom],
      }),
    ]);
  };

  const sendPrintEvents = async (
    initParams: Omit<KensaIraiParams, "eventCd">,
    gotoFrom: GotoMedicalFrom,
  ) => {
    await sendUpdateOrderEvents(initParams, gotoFrom);

    await Promise.allSettled([
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_PRINT_UPDATE_ORDER["default"],
      }),
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_PRINT_UPDATE_ORDER[gotoFrom],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_PRINT_UPDATE_ORDER["default"],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_PRINT_UPDATE_ORDER[gotoFrom],
      }),
    ]);
  };

  const sendAccountingEvents = async (
    initParams: Omit<KensaIraiParams, "eventCd">,
    gotoFrom: GotoMedicalFrom,
  ) => {
    await sendUpdateOrderEvents(initParams, gotoFrom);

    await Promise.allSettled([
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_ACCOUNTING_UPDATE_ORDER["default"],
      }),
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_ACCOUNTING_UPDATE_ORDER[gotoFrom],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_ACCOUNTING_UPDATE_ORDER["default"],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_ACCOUNTING_UPDATE_ORDER[gotoFrom],
      }),
    ]);
  };

  const sendEndConsEvents = async (
    initParams: Omit<KensaIraiParams, "eventCd">,
    gotoFrom: GotoMedicalFrom,
  ) => {
    await sendUpdateOrderEvents(initParams, gotoFrom);

    await Promise.allSettled([
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_END_CONS_UPDATE_ORDER["default"],
      }),
      sendKensaIrai({
        ...initParams,
        eventCd: RENKEI_MEDICAL_END_CONS_UPDATE_ORDER[gotoFrom],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_END_CONS_UPDATE_ORDER["default"],
      }),
      sendRenkei({
        ...initParams,
        eventCd: RENKEI_MEDICAL_END_CONS_UPDATE_ORDER[gotoFrom],
      }),
    ]);
  };

  return {
    sendRenkei,
    sendKensaIrai,
    sendSaveEvents,
    sendPrintEvents,
    sendAccountingEvents,
    sendEndConsEvents,
  };
};
