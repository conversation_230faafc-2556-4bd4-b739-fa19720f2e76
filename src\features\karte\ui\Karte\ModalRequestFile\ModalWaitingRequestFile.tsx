import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
} from "react";

import styled from "styled-components";
import { Flex, Spin } from "antd";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/system-config";
import { System } from "@/utils/socket-helper";
import { RenderIf } from "@/utils/common/render-if";
import { usePostApiEpsCreateEpsReferenceMutation } from "@/apis/gql/operations/__generated__/print-out-patient";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { useUpsertEpsRegister } from "@/hooks/useUpsertEpsRegister";

import { useCreateDrugXml } from "../CreatePrescriptionInformation/useCreateDrugXml";
import { useEPrescriptionContext } from "../PrintSetting/EPrescriptionContextProvider";
import { useObserverWaitingModalContext } from "../StationPrescription/ObserverWaitingModalActionProvider";
import { useCancelRegister } from "../CancelRegister";

import { ModalConfirm } from "./ModalConfirm";

import type { PrescriptionDrugLocalFlow } from "../FlowLocalPresciption";
import type { Dispatch, SetStateAction } from "react";
import type {
  IFile,
  ISystemRequestConfig,
} from "@/utils/socket-helper/socket.type";
import type {
  DomainModelsEpsPrescriptionEpsPrescriptionModel,
  DomainModelsEpsReqEpsReqModel,
  EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput,
} from "@/apis/gql/generated/types";
import type { IResponse } from "@/utils/socket-helper/socket.type";

const StyleModal = styled(Modal)`
  .ant-modal-footer {
    justify-content: center;
  }
`;

const Wrapper = styled.div`
  padding: 24px 32px 24px 24px;
  height: 256px;
`;

type Props = {
  prescription: PrescriptionDrugLocalFlow;
  setProcessRecordPrescription: Dispatch<
    SetStateAction<PrescriptionDrugLocalFlow | null>
  >;
  prescriptionNew: EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput | null;
  setXmlError: Dispatch<SetStateAction<string>>;
};

export const ModalWaitingRequestFile = ({
  setProcessRecordPrescription,
  prescriptionNew,
  setXmlError,
}: Props) => {
  const { timeout } = useCreateDrugXml({});

  const { raiinNo, ptId, sinDate } = useGetOrderInfoContext();
  const [message] = useState("処方内容(控え)を取得しています");
  const [disabled, setDisabled] = useState(true);
  const [showModalConfirm, setShowModalConfirm] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [registerResult, setRegisterResult] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const {
    setResolveBreakModal,
    currentFlowRef,
    setResolveBreakModalFlowLocalPrescription,
    setError,
    setResolveWaitModal,
    setStopLoop,
  } = useObserverWaitingModalContext();
  const { handleUpsertEpsRegister } = useUpsertEpsRegister();
  const { handleError } = useErrorHandler();
  const { handleCloseModal } = useModal();
  const { data: dataSystemConf } = useGetApiSystemConfGetListQuery({
    onError: (error) => {
      handleError({ error });
    },
  });
  const controllerAbort = useRef<AbortController | null>(null);

  const { statePrintSetting, updateEpsRegister } = useEPrescriptionContext();

  const [postEpsReference] = usePostApiEpsCreateEpsReferenceMutation();

  const { handleCancelRegister } = useCancelRegister();

  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;

  const system = useMemo(
    () => new System("/medical", systemConf),
    [systemConf],
  );

  const hasCalledApi = React.useRef(false);

  const handleClickButton = useCallback(() => {
    setShowModalConfirm(true);
  }, [setShowModalConfirm]);

  const handleClickButtonSkip = useCallback(() => {
    controllerAbort.current?.abort();

    handleCloseModal("WAITING_REQUEST_FILE");
    setDisabled(false);
    setResolveWaitModal();
  }, []);
  useEffect(() => {
    if (timeout > 0) {
      setDisabled(true);
      const timer = setTimeout(() => {
        setDisabled(false);
      }, timeout * 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [timeout, message]);

  const handlePrescriptionResult = useCallback(
    async (
      createFileResult: IResponse<IFile>,
      prescriptionResult: DomainModelsEpsPrescriptionEpsPrescriptionModel,
    ) => {
      if (createFileResult.data.fileName.includes("err")) {
        if (statePrintSetting?.isOutpatientPrescription === false) {
          setResolveWaitModal();
          return;
        } else {
          if (currentFlowRef.current === "LOCAL") {
            setResolveBreakModalFlowLocalPrescription();
          } else {
            setResolveBreakModal();
          }
          setResolveWaitModal();
          handleCancelRegister(3);
          console.log("go to 5-6");

          setStopLoop();

          setError("ERROR");
          setXmlError(createFileResult?.data?.content);
        }
      } else {
        const xmlContentGetInfo = JSON.parse(
          createFileResult?.data?.content || "",
        );
        const msgBodyGetInfo = xmlContentGetInfo?.XmlMsg?.MessageBody;
        const msgHeaderGetInfo = xmlContentGetInfo?.XmlMsg?.MessageHeader;
        const arbitraryFileIdentifier =
          msgHeaderGetInfo?.ArbitraryFileIdentifier;

        await handleUpsertEpsRegister({
          arbitraryFileIdentifier,
          dispensingResultId: "",
          raiinNo: raiinNo,
          prescriptionId: prescriptionResult?.prescriptionId ?? "",
          ptId: ptId,
          sinDate: sinDate,
          reqType: 7,
          status: 1,
          resultCode: "",
          resultMessage: "",
          result: "",
        });
        // success
        if (
          msgBodyGetInfo?.ProcessingResultStatus === "1" &&
          msgHeaderGetInfo?.SegmentOfResult === "1"
        ) {
          await postEpsReference({
            variables: {
              emrCloudApiRequestsEpsCreateEpsReferenceRequestInput: {
                sinDate: prescriptionResult.sinDate,
                ptId: prescriptionResult.ptId,
                raiinNo: prescriptionResult.raiinNo,
                prescriptionId: prescriptionResult?.prescriptionId ?? "",
                prescriptionReferenceInformation:
                  msgBodyGetInfo?.PrescriptionReferenceInformationFile ?? "",
              },
            },
            onError: (error) => {
              handleError({ error });
              handleCloseModal("WAITING_REQUEST_FILE");
              if (currentFlowRef.current === "LOCAL") {
                setResolveBreakModalFlowLocalPrescription();
              } else {
                setResolveBreakModal();
              }
              setResolveWaitModal();
              setStopLoop();
            },
          });

          setResolveWaitModal();
          return;
        } else {
          if (statePrintSetting?.isOutpatientPrescription === false) {
            setResolveWaitModal();
            return;
          } else {
            if (currentFlowRef.current === "LOCAL") {
              setResolveBreakModalFlowLocalPrescription();
            } else {
              setResolveBreakModal();
            }
            setResolveWaitModal();
            handleCancelRegister(3);

            setStopLoop();

            setError("XML");
            setXmlError(createFileResult?.data?.content);
          }
        }
      }

      setProcessRecordPrescription((prev) => {
        if (!prev) return null;
        return {
          ...prev,
          refileCount: prev.refileCount,
          prescriptionDocument: prev.prescriptionDocument,
        };
      });

      return;
    },
    [
      postEpsReference,
      statePrintSetting,
      setProcessRecordPrescription,
      setResolveBreakModal,
      setResolveBreakModalFlowLocalPrescription,
      currentFlowRef,
      handleCancelRegister,
      setError,
      setXmlError,
      raiinNo,
      ptId,
      sinDate,
      handleUpsertEpsRegister,
      setStopLoop,
    ],
  );

  useEffect(() => {
    if (isProcessing || hasCalledApi.current) return;

    const processData = async () => {
      setIsProcessing(true);

      if (prescriptionNew) {
        const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;

        const registerResult = await handleUpsertEpsRegister({
          arbitraryFileIdentifier,
          dispensingResultId: "",
          prescriptionId: prescriptionNew?.prescriptionId ?? "",
          raiinNo: raiinNo,
          ptId: ptId,
          sinDate: sinDate,
          reqType: 7,
          status: 1,
          resultCode: "",
          resultMessage: "",
          result: "",
          reqDate: +dayjs().format("YYYYMMDD"),
        });

        if (registerResult) {
          setRegisterResult(registerResult);
        }
        controllerAbort.current = new AbortController();
        const signal = controllerAbort.current.signal;
        const configSignal: ISystemRequestConfig = { signal };
        const createFileResult = await system.createFile(
          {
            messageHeader: { ArbitraryFileIdentifier: arbitraryFileIdentifier },
            messageBody: {
              PrescriptionId: prescriptionNew?.prescriptionId,
            },
          },
          "EPSsiPIR06req",
          configSignal,
        );

        if (createFileResult) {
          if (registerResult) {
            updateEpsRegister(registerResult, null);
          }
          await handlePrescriptionResult(createFileResult, prescriptionNew);
        }
      }

      console.log("vao day roi 229");
      setIsProcessing(false);
      handleCloseModal("WAITING_REQUEST_FILE");
    };

    processData();
  }, [isProcessing]);

  const handleClose = useCallback(() => {
    handleCloseModal("WAITING_REQUEST_FILE");
    setDisabled(false);

    if (registerResult) {
      updateEpsRegister(registerResult, null);
    }

    if (currentFlowRef.current === "LOCAL") {
      setResolveBreakModalFlowLocalPrescription();
    } else {
      setResolveBreakModal();
    }
    setResolveWaitModal();
  }, [
    handleCloseModal,
    setDisabled,
    currentFlowRef,
    setResolveBreakModal,
    setResolveBreakModalFlowLocalPrescription,
    setResolveWaitModal,
  ]);

  const renderModal = useMemo(() => {
    const isOutpatientPrescription =
      statePrintSetting?.isOutpatientPrescription ?? true;
    if (isOutpatientPrescription) {
      return (
        <StyleModal
          width={480}
          isOpen={true}
          title={"処理中"}
          onClose={handleClose}
          footer={[
            <Button
              key="close"
              varient="tertiary"
              shape="round"
              disabled={true}
            >
              キャンセル
            </Button>,
          ]}
        >
          <Wrapper>
            <Flex
              style={{
                minHeight: 200,
                flexDirection: "column",
                justifyContent: "space-between",
              }}
            >
              <Flex
                align="center"
                justify="center"
                style={{
                  flexDirection: "column",
                  marginTop: "auto",
                  marginBottom: "auto",
                }}
              >
                <p style={{ textAlign: "center" }}>
                  処方内容(控え)を取得しています
                </p>
                <Flex align="center" justify="center" style={{ marginTop: 24 }}>
                  <Spin size="large" />
                </Flex>
              </Flex>

              <Flex justify="flex-end">
                <Button
                  varient="inline"
                  disabled={disabled}
                  onClick={handleClickButton}
                >
                  処方箋情報を登録しない
                </Button>
              </Flex>
            </Flex>
          </Wrapper>
          <RenderIf condition={showModalConfirm}>
            <ModalConfirm
              onClose={() => {
                setShowModalConfirm(false);
                handleCloseModal("WAITING_REQUEST_FILE");
                controllerAbort.current?.abort();
              }}
              registerResult={registerResult}
              updateEpsRegister={updateEpsRegister}
            />
          </RenderIf>
        </StyleModal>
      );
    }
    return (
      <StyleModal
        width={480}
        isOpen={true}
        title={"処理中"}
        footer={[
          <Button key="close" varient="tertiary" shape="round" disabled={true}>
            キャンセル
          </Button>,
        ]}
      >
        <Wrapper>
          <Flex
            style={{
              minHeight: 200,
              flexDirection: "column",
              justifyContent: "space-between",
            }}
          >
            <Flex
              align="center"
              justify="center"
              style={{
                flexDirection: "column",
                marginTop: "auto",
                marginBottom: "auto",
              }}
            >
              <p style={{ textAlign: "center" }}>
                処方内容(控え)を取得しています
              </p>
              <Flex align="center" justify="center" style={{ marginTop: 24 }}>
                <Spin size="large" />
              </Flex>
            </Flex>

            <Flex justify="flex-end">
              <Button
                varient="inline"
                disabled={disabled}
                onClick={handleClickButtonSkip}
              >
                処方箋情報を登録しない
              </Button>
            </Flex>
          </Flex>
        </Wrapper>
      </StyleModal>
    );
  }, [
    handleClose,
    disabled,
    handleClickButton,
    showModalConfirm,
    statePrintSetting?.isOutpatientPrescription,
  ]);

  return renderModal;
};
