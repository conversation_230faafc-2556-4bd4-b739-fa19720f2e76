import { useEffect, useMemo, useState } from "react";

import styled from "styled-components";
import { Spin, Typography } from "antd";
import { useFormContext } from "react-hook-form";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { Connection, System } from "@/utils/socket-helper";
import {
  usePostApiEpsGetPrescriptionFromCsvDataMutation,
  usePostApiEpsUpdatePrescriptionStatusByIdsMutation,
} from "@/apis/gql/operations/__generated__/karte-retry-cancel";
import { SvgIconError } from "@/components/ui/Icon/IconError";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import {
  usePostApiEpsGetOutDrugCsvDataMutation,
  usePostApiEpsGetPreRegistrationDataMutation,
  usePostApiEpsSavePrescriptionInfoMutation,
} from "@/apis/gql/operations/__generated__/duplicate-medication";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { usePrintOutPatientPrescription } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/usePrintOutPatientPrescription";
import { formatGetOutDrugCsv } from "@/features/karte/utils/e-prescription";
import { useUpsertEpsRegister } from "@/hooks/useUpsertEpsRegister";
import {
  Flow,
  PaymentAutoCalculationFrom,
  useModal,
} from "@/features/karte/providers/ModalProvider";

import { useEPrescriptionContext } from "../PrintSetting/EPrescriptionContextProvider";
import { ModalPrescriptionInfo } from "../ModalPrescriptionInfo";
import { convertFormValueToUpsertParams } from "../MedicineOrder/utils/orderForm";
import { closeWindow } from "../PrintOutpatientPrescription/utils";

import type { ApolloError } from "@apollo/client";
import type {
  DomainModelsEpsPrescriptionEpsPrescriptionModel,
  DomainModelsEpsReqEpsReqModel,
  EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse,
} from "@/apis/gql/generated/types";
import type {
  KarteFormData,
  OrderRp,
} from "@/features/karte/types/karte-order";
import type { Dispatch, SetStateAction } from "react";

type ModalRetryCancelProps = {
  isShowModalCreateRegisterInfo: boolean;
  ptId: string;
  raiinNo: string;
  sinDate: number;
  // prescriptionIdList: DomainModelsEpsPrescriptionEpsPrescriptionModel[];
  // openModalRetryCancel: boolean;
  handleCloseModal: () => void;
  setIsShowModalCreateRegisterInfo: Dispatch<SetStateAction<boolean>>;
};

export type RefillData = {
  refill?: EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse;
  refill2?: EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse;
  refill3?: EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse;
  noData?: number[];
};

enum MODAL_TYPE {
  REGISTER = "register",
  ERROR = "error",
  ERROR02 = "error02",
  ERROR06 = "error06",
  ERROR12 = "error12",
  LOADING = "loading",
  CONFIRM = "confirm",
  LOADING_GET_INFO = "loadingGetInfo",
  CONFIRM_GET_INFO = "confirmGetInfo",
  LOADING_CREATE_CSV = "loadingCreateCSV",
}

const { Title, Text } = Typography;

const StyledModal = styled(Modal)<{
  $errorModal?: boolean;
}>`
  .ant-modal-header {
    background-color: ${({ $errorModal }) =>
      $errorModal ? "#e74c3c" : "#005BAC"};
  }
  .ant-modal-body {
    min-height: 256px;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .ant-modal-footer {
    height: 84px;
    align-items: center;
  }
`;

const ModalContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30px;
  min-height: 256px;
  width: 100%;
`;

const ModalContentConfirm = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 24px;
`;

const ContentErrorWapper = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 24px 50px;
`;

const ConfirmButton = styled(Button)`
  position: absolute;
  bottom: 8px;
  right: 24px;
`;

export const ModalRetryCancel = ({
  isShowModalCreateRegisterInfo,
  setIsShowModalCreateRegisterInfo,
  ptId,
  raiinNo,
  sinDate,
  // prescriptionIdList,
  // openModalRetryCancel,
  handleCloseModal,
}: ModalRetryCancelProps) => {
  // const = useModal
  const [numberOfTabs, setNumberOfTabs] = useState(0);
  const [showModalError, setShowModalError] = useState(false);
  const [disabledBtn, setDisabledBtn] = useState(true);
  const [modalType, setModalType] = useState<MODAL_TYPE>(MODAL_TYPE.LOADING);
  const [contentError, setContentError] = useState<{
    title: string;
    content?: string;
    content2?: string;
  }>({
    title: "",
    content: "",
    content2: "",
  });

  const [registerResult, setRegisterResult] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const { insuranceDefault } = useGetOrderInfoContext();
  const [dataRefill, setDataRefill] = useState<RefillData>();
  const [registerResultDIM06req, setRegisterResultDIM06req] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const {
    setFlagRegisterChange,
    statePrintSetting: { isOutpatientPrescription, outpatientOption },
    setListDrugCSV,
    updateEpsRegister,
    isClosePageKarte,
    prescriptionIdList,
    setPrescriptionIdList,
  } = useEPrescriptionContext();

  const { handlePrintOutPatientPrescriptionPaper } =
    usePrintOutPatientPrescription();

  const { handleError } = useErrorHandler();

  const { handleCloseModal: handleCloseModalProvider } = useModal();

  const onError = (error: ApolloError | Error) => {
    handleCloseModalProvider("DUPLICATE_MEDICATION_CHECK");
    handleCloseModal();
    handleError({ error });
  };

  const handleGoToStep6AndPrint = () => {
    handleCloseModalProvider("DUPLICATE_MEDICATION_CHECK");
    handlePrintOutPatientPrescriptionPaper(true).then();
  };

  const [updateStatusPrescription] =
    usePostApiEpsUpdatePrescriptionStatusByIdsMutation({
      onError,
    });

  const onErrorWithClose = (error: ApolloError | Error) => {
    handleCloseModal();
    handleError({ error });
  };

  const onErrorWithCloseModal = (error: ApolloError | Error) => {
    setIsShowModalCreateRegisterInfo(false);
    handleCloseModalProvider("CREATE_REGISTER_INFO");
    onError(error);
  };

  const { data: dataSystemConf } = useGetApiSystemConfGetListQuery({
    onError: onErrorWithCloseModal,
  });
  const [postApiEpsGetOutDrugCsvData, { loading: loadingCSVData }] =
    usePostApiEpsGetOutDrugCsvDataMutation({
      onError: onErrorWithCloseModal,
    });

  const [postApiEpsGetPreRegistration] =
    usePostApiEpsGetPreRegistrationDataMutation({
      onError: onErrorWithCloseModal,
    });

  const [convertCSVTOData] = usePostApiEpsGetPrescriptionFromCsvDataMutation({
    onError,
  });

  const [updatePrescriptionInfo] = usePostApiEpsSavePrescriptionInfoMutation({
    onError,
  });

  const handleUpdateStatus = (id: string | undefined, status: number) => {
    updateStatusPrescription({
      variables: {
        emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput: {
          ptId,
          raiinNo,
          sinDate,
          prescriptionStatusItem: [
            {
              prescriptionId: id,
              status: status,
            },
          ],
        },
      },
      onError,
    });
  };

  const handleUpdateStatusByPrescriptionInfo = (
    prescription: DomainModelsEpsPrescriptionEpsPrescriptionModel | undefined,
    status: number,
  ) => {
    updatePrescriptionInfo({
      variables: {
        input: {
          prescriptionInfos: [
            {
              bango: prescription?.bango,
              deletedReason: prescription?.deletedReason,
              edaNo: prescription?.edaNo,
              hokensyaNo: prescription?.hokensyaNo,
              issueType: prescription?.issueType,
              kigo: prescription?.kigo,
              kohiFutansyaNo: prescription?.kohiFutansyaNo,
              kohiJyukyusyaNo: prescription?.kohiJyukyusyaNo,
              prescriptionDocument: prescription?.prescriptionDocument,
              ptId: prescription?.ptId,
              raiinNo: prescription?.raiinNo,
              refileCount: prescription?.refileCount,
              seqNo: prescription?.seqNo,
              sinDate: prescription?.sinDate,
              status,
              accessCode: prescription?.accessCode,
              prescriptionId: prescription?.prescriptionId,
            },
          ],
        },
      },
      onError,
    });
  };

  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;

  const system = new System("/medical", systemConf);
  const connect = new Connection("/medical");

  const timeGetResult = useMemo(() => {
    const dataSystemConfFiltered =
      dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.find(
        (item) => item.grpCd === 100040 && item.grpEdaNo === 4,
      );
    return Number(dataSystemConfFiltered?.val) * 1000;
  }, [dataSystemConf]);

  const submit = async () => {
    if (registerResult) {
      updateEpsRegister(registerResult, null);
    }

    if (registerResultDIM06req) {
      updateEpsRegister(registerResultDIM06req, null);
    }

    if (modalType === MODAL_TYPE.LOADING) {
      handleUpdateStatusToWaitingCancel();
    }
    setFlagRegisterChange("OFF");
    connect.disconnect();
    handleGoToStep6AndPrint();
    handleCloseKarteCaseSubmit();
  };

  const handleCloseKarte = () => {
    if (!isClosePageKarte) {
      handleCloseModal();
      if (stateModal.paymentAutoCalculationOpen) {
        handleOpenModal(
          "PAYMENT_AUTO_CALCULATION",
          undefined,
          Flow.Flow2,
          undefined,
          undefined,
          PaymentAutoCalculationFrom.Accounting,
        );
      }
    } else {
      closeWindow(isClosePageKarte);
    }
  };

  const handleCloseKarteCaseSubmit = () => {
    if (!isClosePageKarte) {
      handleCloseModal();
      if (stateModal.paymentAutoCalculationOpen) {
        handleOpenModal(
          "PAYMENT_AUTO_CALCULATION",
          undefined,
          Flow.Flow2,
          undefined,
          undefined,
          PaymentAutoCalculationFrom.Accounting,
        );
      }
    }

    handleCloseModal();
  };

  const handleUpdateStatusToWaitingCancel = () => {
    postApiEpsGetPreRegistration({
      variables: {
        input: {
          odrInfs: [],
          statusList: [2],
          raiinNo: String(raiinNo),
          ptId: String(ptId),
        },
      },
      onError,
    }).then((res) => {
      const epsPrescriptionModel =
        res.data?.postApiEpsGetPreRegistrationData?.data
          ?.preRegistrationCheckingModel?.epsPrescriptionModel;
      const prescriptionInfos = epsPrescriptionModel?.map((item) => ({
        bango: item?.bango,
        deletedReason: item?.deletedReason,
        edaNo: item?.edaNo,
        hokensyaNo: item?.hokensyaNo,
        issueType: item?.issueType,
        kigo: item?.kigo,
        kohiFutansyaNo: item?.kohiFutansyaNo,
        kohiJyukyusyaNo: item?.kohiJyukyusyaNo,
        prescriptionDocument: item?.prescriptionDocument,
        ptId: item?.ptId,
        raiinNo: item?.raiinNo,
        refileCount: item?.refileCount,
        seqNo: item?.seqNo,
        sinDate: item?.sinDate,
        status: 1,
        accessCode: item?.accessCode,
        prescriptionId: item?.prescriptionId,
      }));
      updatePrescriptionInfo({
        variables: {
          input: {
            prescriptionInfos,
          },
        },
        onError,
      });
    });
  };

  const handleCloseModalRetryCancel = () => {
    if (registerResult) {
      updateEpsRegister(registerResult, null);
    }

    if (registerResultDIM06req) {
      updateEpsRegister(registerResultDIM06req, null);
    }

    if (modalType === MODAL_TYPE.LOADING_GET_INFO) {
      setModalType(MODAL_TYPE.CONFIRM_GET_INFO);
    }

    if (modalType === MODAL_TYPE.LOADING) {
      handleUpdateStatusToWaitingCancel();
    }

    if (modalType === MODAL_TYPE.REGISTER) {
      handleCloseKarte();
    }

    if (
      modalType !== MODAL_TYPE.LOADING_GET_INFO &&
      modalType !== MODAL_TYPE.REGISTER
    ) {
      connect.disconnect();
      handleCloseModal();
    }
  };

  const handleShowConfirm = () => {
    if (modalType === MODAL_TYPE.CONFIRM_GET_INFO) {
      handleCloseKarte();
    }

    if (
      !isOutpatientPrescription ||
      modalType === MODAL_TYPE.LOADING_CREATE_CSV
    ) {
      submit();
    }

    if (!isOutpatientPrescription && modalType === MODAL_TYPE.LOADING) {
      handleUpdateStatusToWaitingCancel();
      handleCloseKarte();
    }

    if (modalType !== MODAL_TYPE.CONFIRM_GET_INFO && isOutpatientPrescription) {
      setModalType(MODAL_TYPE.CONFIRM);
    }
  };

  const { handleUpsertEpsRegister } = useUpsertEpsRegister();

  const { watch } = useFormContext<KarteFormData>();

  const orderRpsWatch = watch("orderRps");
  const jikanKbn = watch("jikanKbn");
  const syosaiKbn = watch("syosaiKbn");

  const valueUpsertParams = {
    orderRps: orderRpsWatch,
    jikanKbn,
    syosaiKbn,
    schemaImages: [],
  };

  const params = convertFormValueToUpsertParams({
    value: valueUpsertParams,
    raiinNo,
    ptId,
    sinDate,
    defaultHokenPid: insuranceDefault?.hokenPid,
  });

  const inputPreRegistration = {
    raiinNo,
    ptId,
    sinDate,
    odrInfs: params.odrInfs,
  };

  const createDrugCsvData = ({
    refileCount,
    data,
  }: {
    refileCount: number;
    data: OrderRp[];
  }) => {
    const inputData = formatGetOutDrugCsv({
      refileCount,
      data,
      ptId: String(ptId),
      raiinNo: String(raiinNo),
      sinDate: Number(sinDate),
      fileType: outpatientOption === "3" ? 2 : 1,
    });

    postApiEpsGetOutDrugCsvData({
      variables: {
        input: inputData,
      },
      onCompleted: (res) => {
        if (!res.postApiEpsGetOutDrugCsvData?.data?.prescriptionDocument) {
          setModalType(MODAL_TYPE.ERROR);
          setContentError({
            title: "処方箋情報の作成に失敗しました",
            content: "処方データが見つかりません。",
          });
        }
        if (res.postApiEpsGetOutDrugCsvData?.data?.prescriptionDocument) {
          convertCSVTOData({
            variables: {
              emrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput: {
                prescriptionCsvBase64Data:
                  res.postApiEpsGetOutDrugCsvData?.data?.prescriptionDocument,
              },
            },
            onError,
          }).then((prescriptionData) => {
            const dataConversed =
              prescriptionData.data?.postApiEpsGetPrescriptionFromCsvData?.data;

            if (dataConversed && refileCount === 1) {
              setDataRefill((prev) => ({ ...prev, refill: dataConversed }));
              setListDrugCSV((prev) => [
                ...prev,
                {
                  refileCount,
                  prescriptionDocument: String(
                    res.postApiEpsGetOutDrugCsvData?.data?.prescriptionDocument,
                  ),
                },
              ]);
            }
            if (dataConversed && refileCount === 2) {
              setDataRefill((prev) => ({ ...prev, refill2: dataConversed }));
              setListDrugCSV((prev) => [
                ...prev,
                {
                  refileCount,
                  prescriptionDocument: String(
                    res.postApiEpsGetOutDrugCsvData?.data?.prescriptionDocument,
                  ),
                },
              ]);
            }
            if (dataConversed && refileCount === 3) {
              setDataRefill((prev) => ({ ...prev, refill3: dataConversed }));
              setListDrugCSV((prev) => [
                ...prev,
                {
                  refileCount,
                  prescriptionDocument: String(
                    res.postApiEpsGetOutDrugCsvData?.data?.prescriptionDocument,
                  ),
                },
              ]);
            }
          });
        } else {
          setDataRefill((prev) => {
            const oldNodata = prev?.noData;
            if (oldNodata) {
              return { ...prev, noData: [...oldNodata, refileCount] };
            } else {
              return { ...prev, noData: [refileCount] };
            }
          });
        }
      },
      onError: onErrorWithCloseModal,
    });
  };

  useEffect(() => {
    if (
      !dataRefill?.refill &&
      !dataRefill?.refill2 &&
      !dataRefill?.refill3 &&
      !!dataRefill?.noData?.length &&
      !loadingCSVData
    ) {
      setShowModalError(true);
    }
  }, [dataRefill, loadingCSVData]);

  const handleCreateRegisterInfo = () => {
    setModalType(MODAL_TYPE.LOADING);
    postApiEpsGetPreRegistration({
      variables: { input: inputPreRegistration },
      onCompleted: () => {
        const refillNo = orderRpsWatch?.filter((item) =>
          item?.orderItems?.some((i) => i?.itemCd !== "@REFILL"),
        );
        const refillNo2 = orderRpsWatch?.filter((item) =>
          item?.orderItems?.some(
            (i) => i?.itemCd === "@REFILL" && i?.suryo === 2,
          ),
        );
        const refillNo3 = orderRpsWatch?.filter((item) =>
          item?.orderItems?.some(
            (i) => i?.itemCd === "@REFILL" && i?.suryo === 3,
          ),
        );
        let numberTabs = 0;

        if (refillNo?.length > 0) {
          createDrugCsvData({ refileCount: 1, data: refillNo });
          numberTabs++;
        }
        if (refillNo2?.length > 0) {
          createDrugCsvData({ refileCount: 2, data: refillNo2 });
          numberTabs++;
        }
        if (refillNo3?.length > 0) {
          createDrugCsvData({ refileCount: 3, data: refillNo3 });
          numberTabs++;
        }
        setNumberOfTabs(numberTabs);
      },
      onError: onErrorWithClose,
    });
  };

  const { state: stateModal, handleOpenModal } = useModal();

  const handleConfirmModalError = () => {
    if (isClosePageKarte) {
      closeWindow(isClosePageKarte);
    } else {
      handleCloseModal();
      if (stateModal.paymentAutoCalculationOpen) {
        handleOpenModal(
          "PAYMENT_AUTO_CALCULATION",
          undefined,
          Flow.Flow2,
          undefined,
          undefined,
          PaymentAutoCalculationFrom.Accounting,
        );
      }
    }
  };

  useEffect(() => {
    if (timeGetResult) {
      setTimeout(() => {
        setDisabledBtn(false);
      }, timeGetResult);
    }
  }, [timeGetResult]);

  const sendReqCancelFile02 = async (prescriptionId: string) => {
    let status = false;

    await new Promise<void>((resolve) => {
      const messageBody = {
        PrescriptionId: prescriptionId,
      };

      if (registerResult) {
        setRegisterResult(registerResult);
      }
      system
        .createFile(
          {
            messageHeader: {},
            messageBody,
          },
          "EPSsiPIR02req",
        )
        .then((files) => {
          let xmlContent;
          try {
            xmlContent = JSON.parse(files?.data?.content || "{}");
          } catch {
            const contentError = files?.data?.content;
            const codeError = `${contentError}`.match(/\[(.*?)\]/);
            setModalType(MODAL_TYPE.ERROR02);
            setContentError({
              title: "調剤結果を取得しています",
              content: `処理結果コード:${codeError && codeError[1] ? codeError[1] : ""}`,
              content2: contentError,
            });
            resolve();
            return;
          }
          // const xmlContent = JSON.parse(files?.data?.content || "{}");
          const msgBody = xmlContent?.XmlMsg?.MessageBody;
          const segmentOfResult =
            xmlContent?.XmlMsg?.MessageHeader?.SegmentOfResult;
          const processingResultStatus =
            xmlContent?.XmlMsg?.MessageBody?.ProcessingResultStatus;

          const processingResultMessage =
            xmlContent?.XmlMsg?.MessageBody?.ProcessingResultMessage;

          const errorCode = xmlContent?.XmlMsg?.MessageHeader?.ErrorCode;
          const errorMsg = xmlContent?.XmlMsg?.MessageHeader?.ErrorMessage;
          const processingResultCode =
            xmlContent?.XmlMsg?.MessageBody?.ProcessingResultCode;

          const isShowError =
            errorCode !== "EPSB1030W" &&
            processingResultCode !== "EPSB1030W" &&
            errorCode !== "EPSB1032W" &&
            processingResultCode !== "EPSB1032W";
          if (registerResult) {
            updateEpsRegister(registerResult, null);
          }
          if (!msgBody && Number(segmentOfResult) === 9 && isShowError) {
            setModalType(MODAL_TYPE.ERROR02);
            setContentError({
              title: "調剤結果を取得しています",
              content: `エラーコード: ${errorCode}\n${errorMsg}`,
            });
            resolve();
            return;
          }

          if (msgBody && Number(processingResultStatus) === 2 && isShowError) {
            setModalType(MODAL_TYPE.ERROR02);
            setContentError({
              title: "調剤結果を取得しています",
              content: `処理結果コード: ${processingResultCode}\n${processingResultMessage}`,
            });
            resolve();
            return;
          }

          if (
            (Number(segmentOfResult) === 1 &&
              Number(processingResultStatus) === 1) ||
            errorCode === "EPSB1030W" ||
            processingResultCode === "EPSB1030W"
          ) {
            handleUpdateStatus(prescriptionId, 3);
            status = true;
            resolve();
            return;
          }

          if (
            errorCode === "EPSB1032W" ||
            processingResultCode === "EPSB1032W"
          ) {
            connect.disconnect();

            const dataSystemConfFiltered = systemConf?.find(
              (item) => item.grpCd === 100040 && item.grpEdaNo === 8,
            );

            const arbitraryFileIdentifierDIM06req = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;

            handleUpsertEpsRegister({
              arbitraryFileIdentifier: arbitraryFileIdentifierDIM06req,
              reqDate: +dayjs().format("YYYYMMDD"),
              raiinNo: raiinNo,
              ptId: ptId,
              sinDate: sinDate,
              prescriptionId: prescriptionId,
              dispensingResultId: "",
              reqType: 8,
              status: 1,
              resultCode: "",
              resultMessage: "",
              result: "",
            }).then((registerResultDIM06req) => {
              if (registerResultDIM06req) {
                setRegisterResultDIM06req(registerResultDIM06req);
              }
            });

            const messageBody = {
              PrescriptionId: prescriptionId,
              RefillSupported: dataSystemConfFiltered?.val,
            };

            setModalType(MODAL_TYPE.LOADING_GET_INFO);

            system
              .createFile(
                {
                  messageHeader: {},
                  messageBody,
                },
                "EPSsiDIM06req",
              )
              .then((filesGetInfo) => {
                let xmlContentGetInfo;
                try {
                  xmlContentGetInfo = JSON.parse(
                    filesGetInfo?.data?.content || "{}",
                  );
                } catch {
                  const contentError = files?.data?.content;
                  const codeError = `${contentError}`.match(/\[(.*?)\]/);
                  setModalType(MODAL_TYPE.ERROR06);
                  setContentError({
                    title: "調剤結果を取得しています",
                    content: `処理結果コード:${codeError && codeError[1] ? codeError[1] : ""}`,
                    content2: contentError,
                  });
                }
                // const xmlContentGetInfo = JSON.parse(
                //   filesGetInfo?.data?.content || "{}",
                // );
                const msgBodyGetInfo = xmlContentGetInfo?.XmlMsg?.MessageBody;
                const processingResultStatusGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageBody
                    ?.ProcessingResultStatus;
                const processingResultCodeGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageBody?.ProcessingResultCode;
                const processingResultMessageGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageBody
                    ?.ProcessingResultMessage;

                const segmentOfResultGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageHeader?.SegmentOfResult;
                const errorCodeGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageHeader?.ErrorCode;
                const errorMsgGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageHeader?.ErrorMessage;

                const msgHeaderGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageHeader;

                handleUpsertEpsRegister({
                  arbitraryFileIdentifier:
                    msgHeaderGetInfo?.ArbitraryFileIdentifier,
                  dispensingResultId: "",
                  raiinNo: raiinNo,
                  prescriptionId: prescriptionId,
                  ptId: ptId,
                  sinDate: sinDate,
                  reqType: 8,
                  status: 1,
                  resultCode: "",
                  resultMessage: "",
                  result: "",
                  reqDate: +dayjs().format("YYYYMMDD"),
                });
                if (!msgBodyGetInfo && Number(segmentOfResultGetInfo) === 9) {
                  setModalType(MODAL_TYPE.ERROR06);
                  setContentError({
                    title: "調剤結果を取得しています",
                    content: `処理結果コード: ${errorCodeGetInfo}\n${errorMsgGetInfo}`,
                  });
                }

                if (
                  msgBodyGetInfo &&
                  Number(processingResultStatusGetInfo) === 2
                ) {
                  setModalType(MODAL_TYPE.ERROR06);
                  setContentError({
                    title: "調剤結果を取得しています",
                    content: `処理結果コード: ${processingResultCodeGetInfo}\n${processingResultMessageGetInfo}`,
                  });
                }

                // if (filesGetInfo?.data?.fileName?.includes(".err")) {
                //   setModalType(MODAL_TYPE.ERROR06);
                //   setContentError({
                //     title: "調剤結果を取得しています",
                //     content: `${xmlContentGetInfo}`,
                //   });
                // }

                const prescriptionStatusGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageBody?.PrescriptionStatus;

                const isSuccessFile =
                  Number(segmentOfResultGetInfo) === 1 &&
                  Number(processingResultStatusGetInfo) === 1;

                if (!isSuccessFile) {
                  handleUpdateStatus(prescriptionId, 1);
                  resolve();
                }

                if (
                  isSuccessFile &&
                  prescriptionStatusGetInfo ===
                    "当該処方箋は処方箋取消されています。"
                ) {
                  handleUpdateStatus(prescriptionId, 3);
                  status = true;
                  resolve();
                }

                if (
                  isSuccessFile &&
                  prescriptionStatusGetInfo === "薬局にて受付されていません。"
                ) {
                  handleUpdateStatus(prescriptionId, 1);
                  status = true;
                  resolve();
                }

                if (
                  isSuccessFile &&
                  prescriptionStatusGetInfo &&
                  prescriptionStatusGetInfo !==
                    "薬局にて受付されていません。" &&
                  prescriptionStatusGetInfo !==
                    "当該処方箋は処方箋取消されています。"
                ) {
                  handleUpdateStatus(prescriptionId, 0);
                  setModalType(MODAL_TYPE.REGISTER);
                }
              });
          }
        });
    });
    return status;
  };

  useEffect(() => {
    if (prescriptionIdList.length > 0 && modalType === MODAL_TYPE.LOADING) {
      const listPrescriptionIds = prescriptionIdList.map(
        (item) => `${item.prescriptionId}`,
      );

      const sendReq = async () => {
        let i = 0;
        while (i < prescriptionIdList.length) {
          const currentIndex = i;
          const prescriptionSelected = prescriptionIdList[currentIndex];
          const prescriptionIdSelected = prescriptionSelected?.prescriptionId;
          if (!prescriptionIdSelected) {
            const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;
            const registerResult = await handleUpsertEpsRegister({
              arbitraryFileIdentifier,
              reqDate: +dayjs().format("YYYYMMDD"),
              raiinNo: raiinNo,
              ptId: ptId,
              sinDate: sinDate,
              prescriptionId: prescriptionIdSelected,
              dispensingResultId: "",
              reqType: 11,
              status: 1,
              resultCode: "",
              resultMessage: "",
              result: "",
            });
            await new Promise<void>((resolve) => {
              const messageBody = {
                InsurerNumber: prescriptionSelected?.hokensyaNo
                  ? prescriptionSelected?.hokensyaNo
                  : prescriptionSelected?.kohiFutansyaNo,
                InsuredCardSymbol: prescriptionSelected?.hokensyaNo
                  ? prescriptionSelected?.kigo
                  : "",
                InsuredIdentificationNumber: prescriptionSelected?.hokensyaNo
                  ? prescriptionSelected?.bango
                  : prescriptionSelected?.kohiJyukyusyaNo,
                InsuredBranchNumber: prescriptionSelected?.hokensyaNo
                  ? prescriptionSelected?.edaNo
                  : "",
                IssueDateFrom: dayjs(prescriptionSelected?.createDate).format(
                  "YYYYMMDD",
                ),
                IssueDateTo: dayjs(prescriptionSelected?.createDate).format(
                  "YYYYMMDD",
                ),
              };

              system
                .createFile(
                  {
                    messageHeader: {},
                    messageBody,
                  },
                  "EPSsiPIR12req",
                )
                .then(async (files) => {
                  if (files?.data?.fileName?.includes("err")) {
                    const contentError = files?.data?.content;
                    const codeError = `${contentError}`.match(/\[(.*?)\]/);
                    setModalType(MODAL_TYPE.ERROR12);
                    setContentError({
                      title: "調剤結果を取得しています",
                      content: `処理結果コード:${codeError && codeError[1] ? codeError[1] : ""}`,
                      content2: contentError,
                    });
                    handleUpdateStatusByPrescriptionInfo(
                      prescriptionSelected,
                      1,
                    );
                    return;
                  }
                  const xmlContent = JSON.parse(files?.data?.content || "{}");

                  const msgBody = xmlContent?.XmlMsg?.MessageBody;
                  const errorCode =
                    xmlContent?.XmlMsg?.MessageHeader?.ErrorCode;
                  const processingResultCode = msgBody?.ProcessingResultCode;
                  const segmentOfResult =
                    xmlContent?.XmlMsg?.MessageHeader?.SegmentOfResult;
                  const processingResultStatus =
                    xmlContent?.XmlMsg?.MessageBody?.ProcessingResultStatus;

                  const processingResultMessage =
                    xmlContent?.XmlMsg?.MessageBody?.ProcessingResultMessage;

                  const errorMsg =
                    xmlContent?.XmlMsg?.MessageHeader?.ErrorMessage;

                  if (registerResult) {
                    updateEpsRegister(registerResult, null);
                  }
                  if (
                    errorCode === "EPSB0056W" ||
                    processingResultCode === "EPSB0056W"
                  ) {
                    handleUpdateStatusByPrescriptionInfo(
                      prescriptionSelected,
                      3,
                    );
                    resolve();
                  }
                  if (
                    errorCode !== "EPSB0056W" &&
                    processingResultCode !== "EPSB0056W"
                  ) {
                    handleUpdateStatusByPrescriptionInfo(
                      prescriptionSelected,
                      1,
                    );
                    const isShowError =
                      errorCode !== "EPSB1030W" &&
                      processingResultCode !== "EPSB1030W" &&
                      errorCode !== "EPSB1032W" &&
                      processingResultCode !== "EPSB1032W";

                    if (
                      !msgBody &&
                      Number(segmentOfResult) === 9 &&
                      isShowError
                    ) {
                      setModalType(MODAL_TYPE.ERROR02);
                      setContentError({
                        title: "調剤結果を取得しています",
                        content: `エラーコード: ${errorCode}\n${errorMsg}`,
                      });
                      handleUpdateStatusByPrescriptionInfo(
                        prescriptionSelected,
                        1,
                      );
                      return;
                    }

                    if (
                      msgBody &&
                      Number(processingResultStatus) === 2 &&
                      isShowError
                    ) {
                      setModalType(MODAL_TYPE.ERROR02);
                      setContentError({
                        title: "調剤結果を取得しています",
                        content: `処理結果コード: ${processingResultCode}\n${processingResultMessage}`,
                      });
                      handleUpdateStatusByPrescriptionInfo(
                        prescriptionSelected,
                        1,
                      );
                      return;
                    }
                  }

                  const prescriptionIdListXml = Array.isArray(
                    msgBody?.PrescriptionIdList,
                  )
                    ? msgBody?.PrescriptionIdList
                    : [msgBody?.PrescriptionIdList];

                  if (prescriptionIdListXml && prescriptionIdListXml.length) {
                    const filterOtherEpsPrescriptionId =
                      prescriptionIdListXml.filter(
                        (item: {
                          PrescriptionId: string;
                          CreateDateTime: string;
                        }) =>
                          !listPrescriptionIds.includes(item?.PrescriptionId) &&
                          Number(item.CreateDateTime) >
                            Number(
                              dayjs(prescriptionSelected?.createDate).format(
                                "YYYYMMDDHHmmss",
                              ),
                            ),
                      );
                    filterOtherEpsPrescriptionId?.sort(
                      (
                        a: {
                          PrescriptionId: string;
                          AccessCode: string;
                          CreateDateTime: string;
                        },
                        b: {
                          PrescriptionId: string;
                          AccessCode: string;
                          CreateDateTime: string;
                        },
                      ) => Number(a.CreateDateTime) - Number(b.CreateDateTime),
                    );

                    const prescriptionIdHaveMinTime =
                      filterOtherEpsPrescriptionId[0];

                    console.log(prescriptionIdHaveMinTime);

                    if (prescriptionIdHaveMinTime) {
                      await updatePrescriptionInfo({
                        variables: {
                          input: {
                            prescriptionInfos: [
                              {
                                bango: prescriptionSelected?.bango,
                                deletedReason:
                                  prescriptionSelected?.deletedReason,
                                edaNo: prescriptionSelected?.edaNo,
                                hokensyaNo: prescriptionSelected?.hokensyaNo,
                                issueType: prescriptionSelected?.issueType,
                                kigo: prescriptionSelected?.kigo,
                                kohiFutansyaNo:
                                  prescriptionSelected?.kohiFutansyaNo,
                                kohiJyukyusyaNo:
                                  prescriptionSelected?.kohiJyukyusyaNo,
                                prescriptionDocument:
                                  prescriptionSelected?.prescriptionDocument,
                                ptId: prescriptionSelected?.ptId,
                                raiinNo: prescriptionSelected?.raiinNo,
                                refileCount: prescriptionSelected?.refileCount,
                                seqNo: prescriptionSelected?.seqNo,
                                sinDate: prescriptionSelected?.sinDate,
                                status: prescriptionSelected?.status,
                                accessCode:
                                  prescriptionIdHaveMinTime?.AccessCode,
                                prescriptionId:
                                  prescriptionIdHaveMinTime?.PrescriptionId,
                              },
                            ],
                          },
                        },
                      });

                      await sendReqCancelFile02(
                        String(prescriptionIdHaveMinTime?.PrescriptionId),
                      ).then((status) => {
                        if (status) {
                          connect.disconnect();
                          resolve();
                        } else {
                          handleUpdateStatus(
                            prescriptionIdHaveMinTime?.PrescriptionId,
                            1,
                          );
                          connect.disconnect();
                          i = prescriptionIdList.length + 1;
                        }
                      });
                    } else {
                      handleUpdateStatusByPrescriptionInfo(
                        prescriptionSelected,
                        3,
                      );

                      resolve();
                    }
                  }
                });
            });
          } else {
            await sendReqCancelFile02(String(prescriptionIdSelected)).then(
              (status) => {
                if (status) {
                  connect.disconnect();
                } else {
                  handleUpdateStatus(prescriptionIdSelected, 1);
                  connect.disconnect();
                  i = prescriptionIdList.length + 1;
                }
              },
            );
          }
          i++;
        }

        // Check if we've processed all items and we're not in error state
        if (
          i === prescriptionIdList.length &&
          ![
            MODAL_TYPE.ERROR,
            MODAL_TYPE.ERROR02,
            MODAL_TYPE.ERROR06,
            MODAL_TYPE.ERROR12,
          ].includes(modalType)
        ) {
          connect.disconnect();
          setPrescriptionIdList([]);
          setIsShowModalCreateRegisterInfo(true);
        }
      };
      sendReq();
    }
  }, [prescriptionIdList, modalType]);

  useEffect(() => {
    if (isShowModalCreateRegisterInfo) {
      handleCreateRegisterInfo();
    } else {
      setDataRefill(undefined);
    }
  }, [isShowModalCreateRegisterInfo]);

  const textCloseModal = useMemo(() => {
    switch (modalType) {
      case MODAL_TYPE.REGISTER:
        return "閉じる";

      case MODAL_TYPE.LOADING_GET_INFO:
        return "中断";

      default:
        return "キャンセル";
    }
  }, [modalType]);

  const titleModal = useMemo(() => {
    switch (modalType) {
      case MODAL_TYPE.REGISTER:
        return "処方箋情報の変更";

      case MODAL_TYPE.CONFIRM:
        return "処方箋発行形態の確認";

      case MODAL_TYPE.CONFIRM_GET_INFO:
        return "処方箋情報の登録";

      case MODAL_TYPE.ERROR:
        return "エラー";

      case MODAL_TYPE.ERROR02:
        return "エラー";

      case MODAL_TYPE.ERROR06:
        return "エラー";

      case MODAL_TYPE.ERROR12:
        return "エラー";

      default:
        return "処理中";
    }
  }, [modalType]);

  const contentModal = useMemo(() => {
    switch (modalType) {
      case MODAL_TYPE.ERROR02:
        return (
          <ContentErrorWapper>
            <SvgIconError />
            <Title level={2} style={{ fontSize: 24 }}>
              {contentError.title}
            </Title>
            <div style={{ width: "100%" }}>
              {contentError.content}
              {contentError.content2 ? (
                <div>{contentError.content2}</div>
              ) : null}
              <div>
                処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？
              </div>
            </div>
          </ContentErrorWapper>
        );

      case MODAL_TYPE.ERROR06:
        return (
          <ContentErrorWapper>
            <SvgIconError />
            <Title level={2} style={{ fontSize: 24 }}>
              {contentError.title}
            </Title>
            <div style={{ width: "100%" }}>
              {contentError.content}
              {contentError.content2 ? (
                <div>{contentError.content2}</div>
              ) : null}
              <div>薬局で受付済または、取消済の処方箋です。</div>
            </div>
          </ContentErrorWapper>
        );
      case MODAL_TYPE.ERROR12:
        return (
          <ContentErrorWapper>
            <SvgIconError />
            <Title level={2} style={{ fontSize: 24 }}>
              {contentError.title}
            </Title>
            <div style={{ width: "100%" }}>{contentError.content}</div>
            {contentError.content2 ? <div>{contentError.content2}</div> : null}
            <div>
              処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？
            </div>
          </ContentErrorWapper>
        );
      case MODAL_TYPE.REGISTER:
        return (
          <ModalContentConfirm>
            <Title level={2} style={{ fontSize: 24 }}>
              処方箋情報を変更できません
            </Title>
            <Text>薬局で受付済または、取消済の処方箋です。</Text>
          </ModalContentConfirm>
        );

      case MODAL_TYPE.CONFIRM:
        return (
          <ModalContentConfirm>
            <Title level={2} style={{ fontSize: 24 }}>
              処方箋情報の登録が完了していません
            </Title>
            <Text>
              処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？
            </Text>
          </ModalContentConfirm>
        );

      case MODAL_TYPE.CONFIRM_GET_INFO:
        return (
          <ModalContentConfirm>
            <Title level={2} style={{ fontSize: 24 }}>
              処方箋情報を登録できません
            </Title>
            <Text>薬局で受付済または、取消済の処方箋です。</Text>
          </ModalContentConfirm>
        );

      case MODAL_TYPE.LOADING_GET_INFO:
        return (
          <ModalContentWrapper>
            <span>調剤結果を取得しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );

      case MODAL_TYPE.LOADING_CREATE_CSV:
        return (
          <ModalContentWrapper>
            <span>処方箋情報CSVを作成しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );

      default:
        return (
          <ModalContentWrapper>
            <span>処方箋情報を取り消しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );
    }
  }, [modalType, contentError]);

  const isModalError =
    modalType === MODAL_TYPE.ERROR06 ||
    modalType === MODAL_TYPE.ERROR02 ||
    modalType === MODAL_TYPE.ERROR12;

  const isLoadingPrescriptionInfo = useMemo(() => {
    if (
      numberOfTabs &&
      dataRefill &&
      Object.keys(dataRefill)?.length === numberOfTabs
    ) {
      return false;
    }
    if (
      numberOfTabs === 2 &&
      dataRefill &&
      Object.keys(dataRefill)?.length === 3 &&
      dataRefill?.noData?.length === 1
    ) {
      return false;
    }
    if (
      numberOfTabs === 1 &&
      dataRefill &&
      Object.keys(dataRefill)?.length === 2 &&
      dataRefill?.noData?.length === 2
    ) {
      return false;
    }
    return true;
  }, [numberOfTabs, dataRefill]);

  return isShowModalCreateRegisterInfo ? (
    <ModalPrescriptionInfo
      isOpen={isShowModalCreateRegisterInfo}
      handleCloseModal={() => {
        setIsShowModalCreateRegisterInfo(false);
        handleCloseModalProvider("CREATE_REGISTER_INFO");
      }}
      data={dataRefill}
      isLoading={loadingCSVData || isLoadingPrescriptionInfo}
      showModalError={showModalError}
      setShowModalError={setShowModalError}
    />
  ) : (
    <StyledModal
      centered
      $errorModal={isModalError}
      title={titleModal}
      width={480}
      isOpen={!!prescriptionIdList.length}
      onCancel={handleCloseModalRetryCancel}
      centerFooterContent={
        MODAL_TYPE.CONFIRM !== modalType &&
        MODAL_TYPE.ERROR02 !== modalType &&
        MODAL_TYPE.ERROR12 !== modalType
      }
      footer={
        MODAL_TYPE.CONFIRM === modalType ||
        MODAL_TYPE.ERROR02 === modalType ||
        MODAL_TYPE.ERROR12 === modalType
          ? [
              <Button
                key={"cancel"}
                varient="tertiary"
                onClick={() =>
                  MODAL_TYPE.CONFIRM === modalType
                    ? setModalType(MODAL_TYPE.LOADING)
                    : handleCloseModal()
                }
              >
                {MODAL_TYPE.ERROR02 === modalType ||
                MODAL_TYPE.ERROR12 === modalType
                  ? "キャンセル"
                  : "戻る"}
              </Button>,
              <Button key={"ok"} varient="primary" onClick={submit}>
                発行
              </Button>,
            ]
          : [
              <Button
                key={"cancel"}
                varient="tertiary"
                onClick={handleCloseModalRetryCancel}
                disabled={
                  isModalError || modalType === MODAL_TYPE.REGISTER
                    ? false
                    : disabledBtn
                }
              >
                {textCloseModal}
              </Button>,
            ]
      }
    >
      {contentModal}
      {modalType !== MODAL_TYPE.ERROR02 &&
      modalType !== MODAL_TYPE.LOADING_GET_INFO &&
      modalType !== MODAL_TYPE.REGISTER &&
      modalType !== MODAL_TYPE.ERROR12 &&
      modalType !== MODAL_TYPE.CONFIRM ? (
        <ConfirmButton
          varient="inline"
          onClick={
            isModalError
              ? () => handleConfirmModalError()
              : () => handleShowConfirm()
          }
          disabled={isModalError ? false : disabledBtn}
        >
          処方箋情報を登録しない
        </ConfirmButton>
      ) : null}
    </StyledModal>
  );
};
