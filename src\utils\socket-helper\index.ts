/* eslint-disable @typescript-eslint/no-explicit-any */

import { HttpStatusCode } from "axios";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

import { DOMAIN_WEB } from "@/constants/endpoint";
import { STORAGE_KEYS } from "@/constants/local-storage";
import { gqlClient } from "@/apis/gql/apollo-client";
import { SMART_KARTE_PORT_GET_LIST } from "@/apis/gql/operations/smart-karte-port-get-list";

import { SystemError } from "./socket.type";

import type {
  INRMessageBody,
  INRMessageHeader,
  INRXmlMsgRequest,
} from "@/types/online-qualification";
import type {
  DrugXmlResponse,
  GetTokenRequest,
  IDataGetFiles,
  IFile,
  InvokeFileResponse,
  IParamsFileMove,
  IResponse,
  ISystemConfig,
  ISystemRequestConfig,
  IXmlMsgRequest,
  LogOffHPKIRequest,
  PayloadAutoDispensing,
  PayloadCancelPrescription,
  PayloadCancelRegisteredPrescription,
  PayloadCreateDrugXML,
  PayloadSignLocally,
  PayloadSignRemote,
  RequestUpdateRefNo,
  ListPortItem,
} from "./socket.type";
import type { Session } from "@/types/session";
import type {
  DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
  EmrCloudApiResponsesSystemConfSystemConfDto,
} from "@/apis/gql/generated/types";

type PayloadOpenFile = {
  id: number;
  ptId: number;
  raiinNo: number;
};

type SignRemotelyResponse = {
  SignedXml: any;
};

type SignLocalyResponse = {
  SignedXml: any;
};

type CancelRegisteredPrescriptionResponse = {
  message: string;
  status: number;
};

const useSystemConfig = (
  sysConf?: EmrCloudApiResponsesSystemConfSystemConfDto[],
) => {
  const useGetSystemSetting = (
    groupCd: number,
    grpEdaNo = 0,
    fromLastestDb = false,
  ) => {
    let systemConf;
    if (!fromLastestDb) {
      const systemConfList = sysConf;
      systemConf = systemConfList?.find(
        (item) => item.grpCd === groupCd && item.grpEdaNo === grpEdaNo,
      );
    }

    return systemConf;
  };

  const useGetSettingParam = (
    groupCd: number,
    grpEdaNo = 0,
    defaultValue = "",
    fromLastestDb = false,
  ) => {
    const systemConf: any = useGetSystemSetting(
      groupCd,
      grpEdaNo,
      fromLastestDb,
    );
    return systemConf ? systemConf.param : defaultValue;
  };

  const hpCDParam = useGetSettingParam(100029, 20);

  return {
    useGetSystemSetting,
    useGetSettingParam,
    hpCDParam,
  };
};

const hospitalInfo = () => {
  const getPrefCD = () => {
    return 0;
  };

  const getHpCD = () => {
    return "";
  };

  return {
    getPrefCD,
    getHpCD,
  };
};

export const onlineQualificationConvert = {
  useGetMedicalInstitutionCode: (
    sysConf?: EmrCloudApiResponsesSystemConfSystemConfDto[],
  ) => {
    const { hpCDParam } = useSystemConfig(sysConf);
    return (
      hospitalInfo().getPrefCD()?.toString().padStart(2, "0") +
      "1" +
      (hpCDParam || hospitalInfo().getHpCD()?.toString().padStart(7, "0"))
    );
  },
};

export const PageUtils = {
  get tabKey(): string {
    if (window.name) return window.name;

    window.name = uuidv4();

    return window.name;
  },
};

const createXmlFile = (
  message: {
    messageHeader: object;
    messageBody: object;
  },
  fileName?: string,
  sysConf?: EmrCloudApiResponsesSystemConfSystemConfDto[],
) => {
  // Note: Use `PageUtils.tabKey` to match the response for the request from the tab
  const oQSsiimm01res_ArbitraryFileIdentifier =
    dayjs().format("YYYYMMDDHHmmssSSS") + uuidv4();

  const messageHeader: object = {
    MedicalInstitutionCode:
      onlineQualificationConvert.useGetMedicalInstitutionCode(sysConf),
    ArbitraryFileIdentifier: oQSsiimm01res_ArbitraryFileIdentifier,
    ...message.messageHeader,
  };

  const messageBody: object = {
    ...message.messageBody,
  };

  const msg: IXmlMsgRequest = {
    MessageHeader: messageHeader,
    MessageBody: messageBody,
  };

  const oQTimestamp = dayjs().format("YYYYMMDDHHmmss");
  const newFileName = fileName
    ? `${fileName}_${oQTimestamp}.xml`
    : `YZKsiquc01req_${oQTimestamp}.xml`;

  return {
    fileName: newFileName,
    msg: { XmlMsg: msg },
  };
};

const createXmlFileUpdateRefNumber = (
  ptNum: string,
  matchHokenInf: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
  isBiggerThan75YearsOld?: boolean,
  isKohi?: boolean,
) => {
  const oQSsiimm01res_ArbitraryFileIdentifier =
    dayjs().format("YYYYMMDDHHmmssSSS") + PageUtils.tabKey;

  const messageHeader: INRMessageHeader = {
    MedicalInstitutionCode:
      onlineQualificationConvert.useGetMedicalInstitutionCode(),
    ArbitraryFileIdentifier: oQSsiimm01res_ArbitraryFileIdentifier,
  };

  const messageBody: INRMessageBody = {
    ReferenceNumberRegistrationInfo: {
      ReferenceNumber: ptNum,
      InsurerNumber: matchHokenInf.insurerNumber || "",
      InsuredCardSymbol: matchHokenInf.insuredCardSymbol || "",
      InsuredIdentificationNumber:
        matchHokenInf.insuredIdentificationNumber || "",
      InsuredBranchNumber: matchHokenInf.insuredBranchNumber || "",
    },
  };

  if (isBiggerThan75YearsOld) {
    delete messageBody.ReferenceNumberRegistrationInfo.InsuredBranchNumber;
  }

  if (isKohi) {
    delete messageBody.ReferenceNumberRegistrationInfo.InsuredBranchNumber;
    delete messageBody.ReferenceNumberRegistrationInfo.InsuredCardSymbol;
  }

  const msg: INRXmlMsgRequest = {
    XmlMsg: {
      MessageHeader: messageHeader,
      MessageBody: messageBody,
    },
  };

  return {
    msg,
  };
};

const getLoggedInUser = () => {
  const defaultInfo = {
    userId: 0,
    hpId: 0,
  };

  try {
    const storageSession = window.localStorage.getItem(STORAGE_KEYS.SESSION);

    if (!storageSession) {
      return defaultInfo;
    }

    const decodedSessionStr = Buffer.from(storageSession, "base64").toString();
    const decodedSession: Session = JSON.parse(decodedSessionStr);

    return {
      userId: decodedSession.staffInfo?.staffId ?? 0,
      hpId: decodedSession.hospitalId,
    };
  } catch (e) {
    return defaultInfo;
  }
};

let globalFileName: string = "";

export class System {
  private connectURL: string;
  private sysConf?: EmrCloudApiResponsesSystemConfSystemConfDto[];
  private config?: ISystemConfig;
  private connectionManager: Connection | null = null;
  public fileName: string;

  constructor(
    connectURL: string,
    sysConf?: EmrCloudApiResponsesSystemConfSystemConfDto[],
    config?: ISystemConfig,
  ) {
    this.connectURL = connectURL;
    this.config = config;
    this.sysConf = sysConf;
    // this.connectionManager = new Connection(this.connectURL, this.config);
    this.fileName = "";
  }

  get request() {
    const instance = new Connection(this.connectURL, this.config);
    this.connectionManager = instance;
    return instance.request.bind(instance);
  }

  // Funcs
  public getFiles() {
    return this.request<object, IDataGetFiles>("GetListXmlFile");
  }

  public moveFile(params: IParamsFileMove) {
    return this.request("MoveXmlFile", params);
  }

  public watchFile(config?: ISystemRequestConfig) {
    return this.request<object, IFile>("DetectXmlFile", {}, config?.signal);
  }

  public requestUpdateRefNumber(
    ptNum: string,
    matchHokenInf?: DomainModelsOnlineQualificationConfirmationResultOfQualificationConfirmation,
    fileName?: string,
    config?: { signal?: AbortSignal },
    isBiggerThan75YearsOld?: boolean,
    isKohi?: boolean,
  ) {
    if (!matchHokenInf || !fileName) return;
    const { msg } = createXmlFileUpdateRefNumber(
      ptNum,
      matchHokenInf,
      isBiggerThan75YearsOld,
      isKohi,
    );

    return this.request<RequestUpdateRefNo, RequestUpdateRefNo>(
      "CreateXmlUpdateRefNo",
      {
        fileName,
        content: JSON.stringify(msg),
      },
      config?.signal,
    );
  }

  public createFile(
    message: {
      messageHeader: object;
      messageBody: object;
    },
    fileName?: string,
    config?: ISystemRequestConfig,
    methodName = "CreateFile",
  ) {
    const { fileName: fileNameConv, msg } = createXmlFile(
      message,
      fileName,
      this.sysConf,
    );

    globalFileName = fileNameConv;

    return this.request<IFile, IFile>(
      methodName,
      {
        fileName: fileNameConv,
        content: JSON.stringify(msg),
      },
      config?.signal,
    );
  }

  public openFileExe(message: PayloadOpenFile) {
    return this.request("RunCustomButton", message);
  }

  public getToken(params: GetTokenRequest) {
    return this.request("GetToken", params);
  }

  public logOffHpki(params: LogOffHPKIRequest) {
    console.log("LogOffHPKI", params);
    return this.request("LogOffHPKI", params);
  }

  public loadHpkiCard() {
    return this.request("LoadHpkiCard");
  }

  public signLocally(
    payload: PayloadSignLocally,
    config?: ISystemRequestConfig,
  ) {
    return this.request<PayloadSignLocally, SignLocalyResponse>(
      "SignLocally",
      payload,
      config?.signal,
    );
  }

  public signRemotely(
    payload: PayloadSignRemote,
    config?: ISystemRequestConfig,
  ) {
    return this.request<PayloadSignRemote, SignRemotelyResponse>(
      "SignRemotely",
      payload,
      config?.signal,
    );
  }

  public verifySignature(
    dispensingResult: string,
    config?: ISystemRequestConfig,
  ) {
    return this.request<
      { dispensingResult: string },
      { drugCsv: string; verify: boolean }
    >("SignatureVerification", { dispensingResult }, config?.signal);
  }

  public autoGetEpsDispensing(
    payload: PayloadAutoDispensing,
    config?: ISystemRequestConfig,
  ) {
    return this.request(
      "AutoGetEpsDispensings",
      {
        medicalInstitutionCode:
          onlineQualificationConvert.useGetMedicalInstitutionCode(this.sysConf),
        ...payload,
      },
      config?.signal,
    );
  }

  public cancelPrescription(
    payload: PayloadCancelPrescription,
    config?: ISystemRequestConfig,
  ) {
    return this.request(
      "CancelPrescription",
      {
        medicalInstitutionCode:
          onlineQualificationConvert.useGetMedicalInstitutionCode(this.sysConf),
        ...payload,
      },
      config?.signal,
    );
  }

  public createDrugXML(
    payload: PayloadCreateDrugXML,
    config?: ISystemRequestConfig,
  ) {
    return this.request<PayloadCreateDrugXML, DrugXmlResponse>(
      "CreateDrugXml",
      payload,
      config?.signal,
    );
  }

  public cancelRegisteredPrescription(
    payload: PayloadCancelRegisteredPrescription,
    config?: ISystemRequestConfig,
  ) {
    const MedicalInstitutionCode =
      onlineQualificationConvert.useGetMedicalInstitutionCode(this.sysConf);
    return this.request<
      PayloadCancelRegisteredPrescription,
      CancelRegisteredPrescriptionResponse
    >(
      "CancelRegisteredPrescription",
      {
        ...payload,
        medicalInstitutionCode: MedicalInstitutionCode,
      },
      config?.signal,
    );
  }

  public invokeFile() {
    if (!this.connectionManager) {
      this.connectionManager = new Connection(this.connectURL, this.config);
    }
    // console.log("Current instance in invokeFile:", globalFileName);
    return this.connectionManager.invoke(globalFileName);
  }

  public renkei(params: object) {
    return this.request("Execute", params);
  }

  // disconnect
  public disconnect() {
    this.connectionManager?.disconnect();
  }
}

export class Connection {
  private socket: WebSocket | null = null;
  private path: string;
  private config?: ISystemConfig;
  private hpId: number;
  private userId: number;
  private connected: boolean = false;
  private listPorts?: ListPortItem[];

  constructor(path: string, config?: ISystemConfig) {
    this.path = path;
    this.config = config;

    const { userId, hpId } = getLoggedInUser();
    this.userId = userId;
    this.hpId = hpId;
  }

  async connect(): Promise<{ success: boolean }> {
    const host = "127.0.0.1";
    const portDefault = "11000";

    const storedPort = localStorage.getItem("PORT_CONNECT_AGENT");
    if (
      storedPort &&
      storedPort !== portDefault &&
      (await this.tryConnection(host, storedPort))
    ) {
      this.connected = true;
      return { success: true };
    }

    const result = await this.tryConnection(host, portDefault);
    if (result) {
      this.connected = true;
      return { success: true };
    }

    const listPorts = await this.loadListPort();
    const privatePorts = listPorts.filter((item) => item.isCommon === 0);

    for (const item of privatePorts) {
      if (await this.tryConnection(host, item.port)) {
        this.connected = true;
        return { success: true };
      }
    }

    this.connected = false;
    return { success: false };
  }

  disconnect() {
    this.socket?.close();
    this.socket = null;
    this.connected = false;
  }

  isConnected(): boolean {
    return this.connected;
  }

  async request<TParams = object, TData = any>(
    methodName: string,
    params?: TParams,
    signal?: AbortSignal,
  ): Promise<IResponse<TData> | null> {
    if (!this.connected || !this.socket) {
      const connected = await this.connect();
      if (!connected || !this.socket) {
        throw new Error("NO_CONNECTION");
      }
    }

    const message = {
      type: methodName,
      header: {
        hpId: this.hpId,
        userId: this.userId,
      },
      domain: DOMAIN_WEB,
      screenCode: "02000000",
      ...this.config,
      ...params,
    };

    return new Promise((resolve, reject) => {
      const onAbort = () => {
        this.socket?.removeEventListener("message", onMessage);
        this.disconnect();
        resolve(null);
      };

      const onMessage = (event: MessageEvent) => {
        try {
          const data: IResponse<TData> = JSON.parse(event.data);
          if (data.status < HttpStatusCode.BadRequest) {
            resolve(data);
            this.disconnect();
            signal?.removeEventListener("abort", onAbort);
          } else {
            reject(new SystemError(SystemError.ERR_BAD_REQUEST, data.message));
            this.disconnect();
            signal?.removeEventListener("abort", onAbort);
          }
        } catch (err) {
          reject(err);
          this.disconnect();
          this.socket?.removeEventListener("message", onMessage);
          signal?.removeEventListener("abort", onAbort);
        }
      };

      signal?.addEventListener("abort", onAbort);
      this.socket?.addEventListener("message", onMessage);
      this.socket?.send(JSON.stringify(message));
      window.localStorage.setItem("SentRenkei", JSON.stringify(message));
    });
  }

  async invoke(fileName: string): Promise<InvokeFileResponse> {
    const response = await this.request("CancelCreateFile", {
      domain: DOMAIN_WEB,
      screenCode: "02000000",
      fileName,
      ...this.config,
    });

    if (!response) {
      throw new SystemError(SystemError.ERR_CONNECT, "NO_CONNECTION");
    }

    return response as InvokeFileResponse;
  }

  async loadListPort(): Promise<ListPortItem[]> {
    if (this.listPorts) return this.listPorts;
    try {
      const response = await gqlClient.query({
        query: SMART_KARTE_PORT_GET_LIST,
        variables: { onlyCommon: false },
        fetchPolicy: "network-only",
      });
      const listPortRes =
        response.data.getApiSmartKartePortGetList?.data?.agentSettings || [];
      this.listPorts = listPortRes;
      return listPortRes;
    } catch (_error) {
      console.error("Error get list port");
      return [];
    }
  }

  async tryConnection(host: string, port: string): Promise<boolean> {
    try {
      const urlConnect = `wss://${host}:${port}${this.path}`;
      const socket = new WebSocket(urlConnect);

      return await new Promise((resolve) => {
        const cleanup = () => {
          socket.onopen = null;
          socket.onerror = null;
          socket.onclose = null;
        };

        socket.onopen = () => {
          localStorage.setItem("PORT_CONNECT_AGENT", port);
          this.socket = socket;
          resolve(true);
        };

        socket.onerror = () => {
          cleanup();
          resolve(false);
        };
      });
    } catch (_error) {
      if (this.socket) {
        this.socket.close();
        this.socket = null;
      }
      return false;
    }
  }

  async tryDefaultConnection(): Promise<boolean> {
    const host = "127.0.0.1";
    const port = 11000;

    return new Promise((resolve) => {
      const urlConnect = `wss://${host}:${port}${this.path}`;
      const socket = new WebSocket(urlConnect);

      const cleanup = () => {
        socket.onopen = null;
        socket.onerror = null;
        socket.onclose = null;
      };

      socket.onopen = () => {
        cleanup();
        this.socket = socket;
        resolve(true);
      };

      socket.onerror = () => {
        resolve(false);
      };
    });
  }
}
