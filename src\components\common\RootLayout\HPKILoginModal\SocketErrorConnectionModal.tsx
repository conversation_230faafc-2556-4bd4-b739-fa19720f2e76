import React from "react";

import styled from "styled-components";

import { SvgIconError } from "@/components/ui/Icon/IconError";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";

import type { FC } from "react";

type Props = {
  content?: string;
  isOpen: boolean;
  onClose: () => void;
};

export const SocketErrorConnectionModal: FC<Props> = ({
  content,
  isOpen,
  onClose,
}) => {
  const handleOpenGuilde = () => {
    window.open(
      "https://smarkarte-app.s3.ap-southeast-1.amazonaws.com/guide-line/%5BSMART_KARTE_APP%5D-Check-Connection-Guideline-JP.pdf",
      "_blank",
      "noopener,noreferrer",
    );

    onClose();
  };
  return (
    <StyledModal
      title="エージェントプログラムが必要"
      isOpen={isOpen}
      onCancel={onClose}
      footer={[
        <Button key="cancel" varient="tertiary" onClick={onClose}>
          閉じる
        </Button>,
      ]}
    >
      <ModalWrapper>
        <ErrorIcon />
        <Heading>
          エージェントプログラムが
          <br />
          起動していません。
        </Heading>
        <Description>
          {content ||
            "エージェントが起動していることをご確認ください。インストールされていない場合は、"}
          <span
            onClick={handleOpenGuilde}
            style={{ color: "#007bff", cursor: "pointer" }}
          >
            インストールガイド
          </span>
          をご参照の上、インストールを行ってください。
        </Description>
      </ModalWrapper>
    </StyledModal>
  );
};

const ModalWrapper = styled.div`
  padding: 20px 24px;
  min-height: 256px;
`;

const StyledModal = styled(Modal)`
  .ant-modal-footer {
    justify-content: center !important;
  }
  .ant-modal-header {
    background-color: #e74c3c;
  }
`;

const Heading = styled.p`
  font-size: 24px;
  line-height: 24px;
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 34px;
  text-align: center;
`;

const ErrorIcon = styled(SvgIconError)`
  display: block;
  margin: 0 auto;
`;

const Description = styled.p``;
