import { type ReactNode } from "react";

import { useRouter } from "next/router";

import { useSession } from "@/hooks/useSession";

import { ClinicLayout } from "./ClinicLayout";
import { GuestLayout } from "./GuestLayout";
import { PharmacyLayout } from "./PharmacyLayout";
import { SignupLayout } from "./SignupLayout";
import { SurveyLayout } from "./SurveyLayout";

export const Layout: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { pathname } = useRouter();

  // 申し込みフォームと問診票フォームのみスマートフォン版あり
  const isSignup = /^\/signup($|\/.*)/.test(pathname);
  const isSurvey = /^\/survey($|\/.*)/.test(pathname);

  const {
    session: { isLoggedIn, isPharmacy: isPharmacyAccount },
  } = useSession();

  const isPharmacy = isLoggedIn && isPharmacyAccount;
  const isClinic = isLoggedIn && !isPharmacyAccount;

  // 申し込みフォームのみログイン不要・レスポンシブ対応あり
  // Layout層で分岐させるのは個別要件があるため（Meta, GlobalStyleなど）
  if (isSignup) {
    return <SignupLayout>{children}</SignupLayout>;
  }

  if (isPharmacy) {
    return <PharmacyLayout>{children}</PharmacyLayout>;
  }

  if (isClinic) {
    return <ClinicLayout>{children}</ClinicLayout>;
  }

  if (isSurvey) {
    return <SurveyLayout>{children}</SurveyLayout>;
  }

  // Signup以外の未ログイン
  return <GuestLayout>{children}</GuestLayout>;
};
