import { useEffect } from "react";

import { cloneDeep, isNil, partition } from "lodash";
import { useFieldArray, useFormContext } from "react-hook-form";

import {
  InoutKbn,
  OrderItemMenuType,
  OrderRpMenuType,
  SanteiKbn,
  SikyuKbn,
  SyohoKbn,
  SyohoLimitKbn,
  Tosekikbn,
  AddFrom,
  OrderRpType,
  InoutHospital,
  SyohoSbt,
  KohatuKbn,
} from "../types/karte-order";
import {
  canAddItemToRp,
  canAddSpecialItemToRp,
  canAddSpecialUsageItemToRp,
  isBunkatuItem,
  isRefillItem,
  isSecondaryUsageItem,
  isUsageItem,
} from "../ui/Karte/MedicineOrder/utils/orderItem";
import { createOrderItem, sempatuToSyoho } from "../utils/order";
import {
  createOrderRp,
  correctRpData,
  TenUtils,
} from "../ui/Karte/MedicineOrder/utils/orderRp";
import { LIST_DRUG } from "../ui/Karte/MedicineOrder/constants/in-out";
import { addAndSortOrderItems, sortRp } from "../utils/order-sort";
import { LockAreaType, useLock } from "../providers/LockInforProvider";

import { useGetOrderInfoContext } from "./useGetOrderInfoContext";
import { useSystemGenerationConfigs } from "./useSystemGenerationConfigs";
import { useUpdateOrderItemAdoptedMutation } from "./useUpdateOrderItemAdoptedMutation";
import { useSystemConfigs } from "./useSystemConfigs";

import type { IKarteValidateProperties } from "./karteValidations/types";
import type { KarteOrderContextState } from "@/features/karte/providers/KarteOrderProvider/types";
import type { InsuranceDefaultType } from "./useGetOrderInfoContext";
import type {
  KarteFormData,
  OrderItem,
  OrderRp,
  AddInfor,
  OrderSearchItem,
} from "../types/karte-order";
import type { MedicationUsageItemType } from "@/features/karte/types/medicine-usage";

type Props = Pick<
  KarteOrderContextState,
  "setSelectedRpId" | "selectedRpId"
> & {
  checkItem: () => Promise<boolean>;
  checkDrug: IKarteValidateProperties["checkDrug"];
  clearErrors: () => void;
  shouldValidate: boolean;
};

export const useHandleKarteOrderForm = ({
  selectedRpId,
  setSelectedRpId,
  checkItem,
  checkDrug,
  clearErrors,
  shouldValidate,
}: Props) => {
  const { sinDate, insuranceDefault } = useGetOrderInfoContext();
  const { getSystemGenerationConfigValue } = useSystemGenerationConfigs();
  const {
    AutoSetSyohoKbnKohatuDrug,
    AutoSetSyohoLimitKohatuDrug,
    AutoSetSyohoKbnSenpatuDrug,
    AutoSetSyohoLimitSenpatuDrug,
  } = useSystemConfigs();

  const { updateOrderItemAdopted } = useUpdateOrderItemAdoptedMutation();
  const { control } = useFormContext<KarteFormData>();
  const { handleLock } = useLock();

  // when unselect a temporary Rp, we change the Rp's type to Other
  useEffect(() => {
    const orderRps = cloneDeep(orderRPFieldArray.fields);
    orderRps.forEach((rp) => {
      if (rp.type === OrderRpType.Temporary && rp.customId !== selectedRpId) {
        rp.type = OrderRpType.Other;
        rp.odrKouiKbn = TenUtils.OTHER_KOUI_KBN;
      }
    });

    sortAndSaveRps(orderRps, false);
  }, [selectedRpId]);

  const orderRPFieldArray = useFieldArray({
    name: `orderRps`,
    control,
    keyName: "_reactHookFormArrayId",
  });

  const getDefaultInoutKbn = (sinKouiKbn: number) => {
    if (LIST_DRUG.includes(sinKouiKbn) || sinKouiKbn === 28) {
      const defaultInOutDrug = getSystemGenerationConfigValue(
        2001,
        0,
        sinDate,
        0,
      );
      return defaultInOutDrug as InoutKbn;
    }

    return InoutKbn.Hospital;
  };

  const getDefaultSyoho = (orderItem: OrderItem) => {
    if (orderItem.sinKouiKbn === 20 && orderItem.drugKbn > 0) {
      switch (orderItem.kohatuKbn) {
        case KohatuKbn.PreReleaseOnly:
          // 先発品
          orderItem.syohoKbn = SyohoKbn.None;
          orderItem.syohoLimitKbn = SyohoLimitKbn.None;
          break;
        case KohatuKbn.LaterReleaseWithPre:
          // 後発品
          orderItem.syohoKbn = AutoSetSyohoKbnKohatuDrug() + 1;
          orderItem.syohoLimitKbn = AutoSetSyohoLimitKohatuDrug();
          break;
        case KohatuKbn.PreReleaseWithLater:
          // 後発品のある先発品
          orderItem.syohoKbn = AutoSetSyohoKbnSenpatuDrug() + 1;
          orderItem.syohoLimitKbn = AutoSetSyohoLimitSenpatuDrug();
          break;
      }
      if (
        orderItem.syohoKbn === SyohoKbn.WriteGenericName &&
        !orderItem.ipnName
      ) {
        // 一般名マスタに登録がない
        orderItem.syohoKbn = SyohoKbn.Changeable;
      }
    }
    return orderItem;
  };

  const getDefaultIsNodspRece = (): InoutKbn => {
    // IsNodspRece defaultValue = InoutKbn value
    const currentRps = cloneDeep(orderRPFieldArray.fields);
    const currentRp = currentRps.find((i) => i.customId === selectedRpId);
    const inoutKbn = currentRp?.inoutKbn || 0;
    return inoutKbn;
  };

  // todo: add item to selectedRP
  const addNewItem = (orderItem: OrderItem, rp?: OrderRp, from?: AddFrom) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const currentRps = cloneDeep(orderRPFieldArray.fields);

    const currentRpIndex = currentRps.findIndex(
      (i) => i.customId === (rp?.customId ?? selectedRpId),
    );
    const currentRp = currentRps[currentRpIndex] as OrderRp;

    const canAdd = canAddItemToRp(orderItem, currentRp);
    const isAddSpecialUsageItemToRp = canAddSpecialUsageItemToRp(
      orderItem,
      currentRp,
    );
    const isAddSpecialItemToRp = canAddSpecialItemToRp(
      orderItem,
      currentRp,
      from,
    );

    // we calculate the RP inputKbn according to the new adding item
    const inoutKbn = getDefaultInoutKbn(orderItem.sinKouiKbn);

    // we calculate SyohoKbn, SyohoLimitKbn according to the new adding item
    const newOrderItem = getDefaultSyoho(orderItem);

    if (
      !currentRp ||
      !canAdd ||
      !isAddSpecialItemToRp ||
      !isAddSpecialUsageItemToRp
    ) {
      const orderRP = createOrderRp([newOrderItem]);
      orderRP.inoutKbn = inoutKbn;
      currentRps.push(orderRP);
      sortAndSaveRps(currentRps, true, orderRP.customId);
      setSelectedRpId(orderRP.customId);
      return;
    }

    const rpWithNewItem = addAndSortOrderItems(newOrderItem, currentRp, {
      from: AddFrom.SEARCH_ORDER,
    });
    const finalRp = correctRpData(rpWithNewItem);

    currentRps[currentRpIndex] = finalRp;

    sortAndSaveRps(currentRps);
  };

  const sortAndSaveRps = async (
    orderRps: OrderRp[],
    triggerValidate = true,
    checkingRpId = selectedRpId,
  ) => {
    const sortedRps = sortRp(orderRps, insuranceDefault?.hokenPid ?? -1);
    orderRPFieldArray.replace(sortedRps);

    if (shouldValidate && triggerValidate) {
      const [checkingOrderRps, currentOrderRps] = partition(
        orderRps,
        (rp) => rp.customId === checkingRpId,
      );

      clearErrors();

      const isCheckItemSuccess = await checkItem();

      const checkingRps =
        checkingOrderRps.length > 0 ? checkingOrderRps : currentOrderRps;

      const isDrugItem = checkingRps?.some((rp) =>
        rp.orderItems.some(
          (item) =>
            item.drugKbn > 0 || isUsageItem(item) || isSecondaryUsageItem(item),
        ),
      );

      if (!isCheckItemSuccess || !isDrugItem) return;

      checkDrug({
        currentOrderRps: checkingOrderRps?.length > 0 ? currentOrderRps : [],
        checkingOrderRps: checkingRps,
      });
    }
  };

  const updateComment = (rpIndex: number, comment: OrderItem) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const currentRp = orderRPFieldArray.fields[rpIndex] as OrderRp;
    if (!currentRp) {
      // eslint-disable-next-line
      console.error("[updateCommentItem] currentRP not found");
      return;
    }

    const currentItems = currentRp.orderItems ?? [];
    const currentCommentIndex = currentItems.findIndex(
      (i) => i.customId === comment.customId,
    );

    updateItemInRp(currentCommentIndex, rpIndex, comment);
  };

  const addComment = (
    comments: OrderItem[],
    from: AddFrom,
    rpIndex: number,
    orderItemIndex?: number,
  ) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const currentRps = cloneDeep(orderRPFieldArray.fields);
    const currentRp = currentRps[rpIndex] as OrderRp;
    const orderItems = currentRp?.orderItems ?? [];
    const currentItem = !isNil(orderItemIndex)
      ? orderItems[orderItemIndex]
      : undefined;

    let newRp = currentRp;
    comments.forEach((comment) => {
      const addInfor: AddInfor = {
        from,
        itemCustomId: currentItem?.customId,
      };
      newRp = addAndSortOrderItems(comment, newRp, addInfor);
    });

    currentRps[rpIndex] = newRp;
    sortAndSaveRps(currentRps);
  };

  const removeOrderRp = (rpIndex: number) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const currentRps = cloneDeep(orderRPFieldArray.fields);
    const rpToRemove = currentRps[rpIndex];
    if (isNil(rpToRemove)) return;
    currentRps.splice(rpIndex, 1);
    sortAndSaveRps(currentRps);
    setSelectedRpId(undefined);
  };

  const removeOrderItem = (rpIndex: number, orderItemIndex: number) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const currentRps = cloneDeep(orderRPFieldArray.fields);
    const selectedRp = currentRps[rpIndex] as OrderRp;

    // Get current items
    const currentItems = selectedRp?.orderItems ?? [];

    // Remove the item at orderItemIndex
    const newItems = currentItems.filter(
      (_, index) => index !== orderItemIndex,
    );

    const newRp = createOrderRp(newItems, selectedRp);
    currentRps[rpIndex] = newRp;
    sortAndSaveRps(currentRps);
  };

  const addOrderRpLabel = (type: OrderRpMenuType, rpIndex: number) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const currentRps = cloneDeep(orderRPFieldArray.fields);
    const currentRp = currentRps[rpIndex] as OrderRp;
    let { santeiKbn, tosekiKbn, sikyuKbn } = currentRp;

    switch (type) {
      case OrderRpMenuType.NoPay:
        santeiKbn =
          santeiKbn === SanteiKbn.NoPay ? SanteiKbn.None : SanteiKbn.NoPay;
        break;
      case OrderRpMenuType.SelfPay:
        santeiKbn =
          santeiKbn === SanteiKbn.SelfPay ? SanteiKbn.None : SanteiKbn.SelfPay;
        break;
      case OrderRpMenuType.BeforeDialysis:
        tosekiKbn =
          tosekiKbn === Tosekikbn.BeforeDialysis
            ? Tosekikbn.None
            : Tosekikbn.BeforeDialysis;
        break;
      case OrderRpMenuType.AfterDialysis:
        tosekiKbn =
          tosekiKbn === Tosekikbn.AfterDialysis
            ? Tosekikbn.None
            : Tosekikbn.AfterDialysis;
        break;
      case OrderRpMenuType.Urgent:
        sikyuKbn =
          sikyuKbn === SikyuKbn.Urgent ? SikyuKbn.None : SikyuKbn.Urgent;
    }

    currentRps[rpIndex] = {
      ...currentRp,
      santeiKbn,
      tosekiKbn,
      sikyuKbn,
    };

    sortAndSaveRps(currentRps);
  };

  /**
   * 後発品変更不可: syohoKbn = 1
   * if 後発品変更不可, 一般名記載する, 選定療養(患者希望) are not selected, syohoKbn = 2
   * 一般名記載する: syohoKbn = 3
   * 選定療養(患者希望): syohoKbn = 4
   * if 剤形変更不可, 含量規格変更不可 are not selected, syohoLimitKbn = 0
   * 剤形変更不可: syohoLimitKbn= 1
   * 含量規格変更不可: syohoLimitKbn = 2
   * 剤形変更不可 + 含量規格変更不可: syohoLimitKbn = 3
   */
  const addOrderItemLabel = (
    type: OrderItemMenuType,
    rpIndex: number,
    itemIndex: number,
  ) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const orderRps = cloneDeep(orderRPFieldArray.fields);
    const orderRp = orderRps[rpIndex]!;
    const { syohoKbn, syohoLimitKbn } = orderRp.orderItems[itemIndex]!;

    let newType = type;
    switch (type) {
      case OrderItemMenuType.NoChangeMedicineType:
        if (syohoLimitKbn === SyohoLimitKbn.NoChangeMedicineSpecs) {
          newType = OrderItemMenuType.NoChangeMedicineTypeOrSpecs;
        }
        if (syohoLimitKbn === SyohoLimitKbn.NoChangeMedicineTypeOrSpecs) {
          newType = OrderItemMenuType.NoChangeMedicineSpecs;
        }
        break;
      case OrderItemMenuType.NoChangeMedicineSpecs:
        if (syohoLimitKbn === SyohoLimitKbn.NoChangeMedicineType) {
          newType = OrderItemMenuType.NoChangeMedicineTypeOrSpecs;
        }
        if (syohoLimitKbn === SyohoLimitKbn.NoChangeMedicineTypeOrSpecs) {
          newType = OrderItemMenuType.NoChangeMedicineType;
        }
        break;
    }

    const { syohoKbn: newSyohoKbn, syohoLimitKbn: newSyohoLimitKbn } =
      sempatuToSyoho(newType);

    const updatedOrderRp: OrderRp = {
      ...orderRp,
      orderItems: orderRp?.orderItems.map((item, index) =>
        index === itemIndex
          ? {
              ...item,
              ...(newSyohoKbn && {
                syohoKbn:
                  syohoKbn === newSyohoKbn ? SyohoKbn.Changeable : newSyohoKbn,
              }),
              ...(newSyohoLimitKbn && {
                syohoLimitKbn:
                  syohoLimitKbn === newSyohoLimitKbn
                    ? SyohoLimitKbn.None
                    : newSyohoLimitKbn,
              }),
            }
          : item,
      ),
    };

    orderRps[rpIndex] = updatedOrderRp;
    sortAndSaveRps(orderRps);
  };

  const updatesIsNodspRece = (
    checked: boolean,
    rpIndex: number,
    itemIndex: number,
  ) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const orderRps = cloneDeep(orderRPFieldArray.fields);
    const orderRp = orderRps[rpIndex]!;

    const updatedOrderRp: OrderRp = {
      ...orderRp,
      orderItems: orderRp?.orderItems.map((item, index) =>
        index === itemIndex
          ? {
              ...item,
              isNodspRece: checked ? 0 : 1,
            }
          : item,
      ),
    };

    orderRps[rpIndex] = updatedOrderRp;
    sortAndSaveRps(orderRps);
  };

  const updateInoutHospital = (value: InoutHospital, rpIndex: number) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;

    let inoutKbn;
    let syohoSbt;

    switch (value) {
      case InoutHospital.Hospital:
        inoutKbn = InoutKbn.Hospital;
        syohoSbt = SyohoSbt.NumberOfDay;
        break;
      case InoutHospital.OutOfHospital:
        inoutKbn = InoutKbn.OutOfHospital;
        syohoSbt = SyohoSbt.NumberOfDay;
        break;
      case InoutHospital.Temporary:
        inoutKbn = InoutKbn.OutOfHospital;
        syohoSbt = SyohoSbt.Temporary;
        break;
      case InoutHospital.Normal:
        inoutKbn = InoutKbn.OutOfHospital;
        syohoSbt = SyohoSbt.Normal;
        break;
    }

    const orderRps = cloneDeep(orderRPFieldArray.fields);
    orderRps[rpIndex] = {
      ...orderRps[rpIndex]!,
      inoutKbn,
      syohoSbt,
    };

    sortAndSaveRps(orderRps);
  };

  const updateInsuranceToRp = (item: InsuranceDefaultType, rpIndex: number) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const orderRps = cloneDeep(orderRPFieldArray.fields);
    orderRps[rpIndex] = {
      ...orderRPFieldArray.fields[rpIndex]!,
      hokenPid: item?.hokenPid,
      // hokenName: item?.hokenName,
      isExpiredPattern: item?.isExpired,
    };

    sortAndSaveRps(orderRps);
  };

  const updateDefaultInsuranceToAllRp = (data: InsuranceDefaultType) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const newListRps = orderRPFieldArray.fields.map((item) => ({
      ...item,
      hokenPid: data?.hokenPid,
      // hokenName: data?.hokenName,
      isExpiredPattern: data?.isExpired,
    }));

    sortAndSaveRps(newListRps);
  };

  const updateItemInRp = (
    itemIndex: number,
    rpIndex: number,
    orderItem: OrderItem,
  ) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const currentRps = cloneDeep(orderRPFieldArray.fields);
    if (!currentRps[rpIndex]) {
      return;
    }

    const orderRp = currentRps[rpIndex] as OrderRp;
    orderRp.orderItems[itemIndex] = orderItem;

    const finalRpData = correctRpData(orderRp);

    currentRps[rpIndex] = finalRpData;
    sortAndSaveRps(currentRps);
  };

  // implement add refill/bunkatu item
  const addRefillBunkatuItem = (item?: OrderSearchItem) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    if (!item) return;
    const orderItem = createOrderItem(item);
    addNewItem(orderItem);
  };

  // Update the new usage to temp_usage or existing usage of Medication RP
  const addMedicineUsageRp = (
    rpIndex: number,
    usage: MedicationUsageItemType,
  ) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const orderRp = orderRPFieldArray.fields[rpIndex];
    if (!orderRp) {
      return;
    }

    const usageIndex = orderRp.orderItems.findIndex(
      (item) => item.customType === "usage",
    );

    if (usageIndex === -1) {
      // eslint-disable-next-line
      console.error("usageIndex not available");
      return;
    }

    const orderItem = createOrderItem(usage);
    updateOrderItemAdopted(orderItem);
    updateItemInRp(usageIndex, rpIndex, orderItem);
  };

  const saveSecondaryUsage = (items: OrderItem[], rpIndex: number) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const orderRps = cloneDeep(orderRPFieldArray.fields);
    let currentRp = orderRps[rpIndex]!;
    items.reverse().forEach(
      (item) =>
        (currentRp = addAndSortOrderItems(item, currentRp, {
          from: AddFrom.SEARCH_ORDER,
        })),
    );

    orderRps[rpIndex] = currentRp;
    sortAndSaveRps(orderRps);
  };

  const updateRefill = (
    value: number | string,
    key: string,
    rpIndex: number,
  ) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const orderRps = cloneDeep(orderRPFieldArray.fields);
    const currentItem = orderRps[rpIndex]!;

    const updatedOrderRp: OrderRp = {
      ...currentItem,
      orderItems: currentItem?.orderItems.map((item) => {
        if (isRefillItem(item)) {
          return {
            ...item,
            [key]: Number(value), // key: suryo
          };
        }
        return item;
      }),
    };

    orderRps[rpIndex] = updatedOrderRp;
    sortAndSaveRps(orderRps);
  };

  const updateBunkatu = (
    value: {
      suryo?: number;
      bunkatu?: string;
    },
    rpIndex: number,
  ) => {
    if (!handleLock(LockAreaType.ACTION_AND_TREATMENT)) return;
    const orderRps = cloneDeep(orderRPFieldArray.fields);
    const currentItem = orderRps[rpIndex]!;

    const updatedOrderRp: OrderRp = {
      ...currentItem,
      orderItems: currentItem?.orderItems.map((item) => {
        if (isBunkatuItem(item)) {
          return {
            ...item,
            ...value,
          };
        }
        return item;
      }),
    };

    orderRps[rpIndex] = updatedOrderRp;
    sortAndSaveRps(orderRps);
  };

  const addRps = async (rps: OrderRp[]) => {
    const currentRps = cloneDeep(orderRPFieldArray.fields);
    const newRps = currentRps.concat(rps);

    // sortAndSaveRps just support validate the selected RP item
    // in this case, we custom own validate for multiple Rps
    sortAndSaveRps(newRps, false);

    if (shouldValidate) {
      clearErrors();

      const isCheckItemSuccess = await checkItem();

      const isDrugItem = rps?.some((rp) =>
        rp.orderItems.some(
          (item) =>
            item.drugKbn > 0 || isUsageItem(item) || isSecondaryUsageItem(item),
        ),
      );

      if (!isCheckItemSuccess || !isDrugItem) return;

      checkDrug({
        currentOrderRps: currentRps,
        checkingOrderRps: rps,
      });
    }
  };

  return {
    addRefillBunkatuItem,
    addOrderItemLabel,
    addOrderRpLabel,
    addNewItem,
    removeOrderRp,
    removeOrderItem,
    addComment,
    updateComment,
    updateBunkatu,
    updateRefill,
    updateInsuranceToRp,
    updateDefaultInsuranceToAllRp,
    orderRPFieldArray,
    updateItemInRp,
    addMedicineUsageRp,
    saveSecondaryUsage,
    updateInoutHospital,
    sortAndSaveRps,
    addRps,
    getDefaultIsNodspRece,
    updatesIsNodspRece,
  };
};
