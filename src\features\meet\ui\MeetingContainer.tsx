import { useEffect, useState } from "react";

import { useMeetingManager } from "amazon-chime-sdk-component-library-react";
import dayjs from "dayjs";
import { useRouter } from "next/router";

import { DesiredDateStatus } from "@/constants/pharmarcy";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { useMeetingState } from "@/hooks/useMeeting";
import { useSession } from "@/hooks/useSession";
import { logger } from "@/utils/sentry-logger";

import { AttendeeType, MeetingStatus } from "../../../constants/meeting";
import { useGetMeeting } from "../hooks/useGetMeeting";
import { useNotifyMeetingToPatient } from "../hooks/useNotifyMeetingToPatient";
import { useSubscriptionMeeting } from "../hooks/useSubcriptionMeeting";
import { useUpdateMeeting } from "../hooks/useUpdateMeeting";
import { getMeeting<PERSON><PERSON>dee, setUpMeeting } from "../utils";
import { createMeeting, getMeeting, joinMeeting } from "../utils/meeting";

import { ErrorNotification } from "./error/ErrorNotification";
import { LeaveMeetingModal } from "./leave/LeaveMeetingModal";
import { MeetingComponent } from "./MeetingComponent";
import { MeetingWaiting } from "./waiting/Waiting";

import type { Meeting } from "@/apis/gql/generated/types";

export const MeetingContainer: React.FC = () => {
  const {
    query: { id, detail },
    isReady,
    push,
  } = useRouter();

  const { notification } = useGlobalNotification();

  const meetingManager = useMeetingManager();
  const {
    setJoinInfo,
    setMeetingMode,
    setMeetingId,
    setJoinedMeeting,
    isLeavedMeeting,
    setReserveDtlId,
  } = useMeetingState();

  const [meetingError, setMeetingError] = useState("");
  const [meeting, setMeeting] = useState<Meeting>();
  const [meetingSubscription, setMeetingSubscription] = useState<Meeting>();
  const [isTestDeviceMode, setIsTestDeviceMode] = useState(false);

  const { handleUpdateMeeting } = useUpdateMeeting();

  const {
    session: { staffInfo, isPharmacy },
  } = useSession();

  const name = staffInfo?.staffName || "";
  const role = staffInfo?.staffType === 1 ? "医師" : "";

  meetingManager.getAttendee = async (
    _chimeAttendeeId: string,
    externalUserId?: string,
  ) => {
    const reservationDetail = meeting?.reservation?.reservationDetails?.find(
      ({ reserveDetailId }) => reserveDetailId === Number(detail),
    );

    const patient = reservationDetail
      ? reservationDetail.patient
      : meeting?.patient;

    return getMeetingAttendee(patient, `${name} ${role}`, externalUserId);
  };

  useGetMeeting({
    meetingId: Number(id),
    skip: !isReady || !id,
    setMeeting: async (meeting) => {
      const examTimeSlot =
        meeting.reservation?.reservationDetails?.[0]?.examTimeSlot;

      // 診療日まで有効にする
      if (examTimeSlot) {
        const examTimeSlotEndDate = dayjs(examTimeSlot.examEndDate);
        const isExpired = dayjs().isAfter(examTimeSlotEndDate.endOf("day"));

        if (isExpired && meeting.status !== MeetingStatus.END) {
          await handleUpdateMeeting(
            meeting.meetingId,
            meeting.chimeMeetingId,
            MeetingStatus.END,
          );

          meeting = { ...meeting, status: MeetingStatus.END };
        }
      }

      setMeeting(meeting);
      const reserveDtlId =
        meeting.reservation?.reservationDetails?.[0]?.reserveDetailId;
      if (reserveDtlId) setReserveDtlId(reserveDtlId);
    },
  });

  useSubscriptionMeeting({
    meetingId: Number(id),
    skip: !isReady || !id,
    setSubscriptionMeeting: setMeetingSubscription,
  });

  const { handleNotifyMeetingToPatient } = useNotifyMeetingToPatient();

  const handleJoinMeeting = async () => {
    if (meeting && meeting.status !== MeetingStatus.END && !meetingError) {
      setIsTestDeviceMode(false);

      try {
        const meetingResource = meeting.chimeMeetingId
          ? await getMeeting(Number(id))
          : await createMeeting(Number(id));

        const isClinicJoined = meetingResource.Attendees.some(
          ({ ExternalUserId }) =>
            ExternalUserId.startsWith(`${AttendeeType.CLINIC}_`),
        );

        if (!isClinicJoined) {
          const attendee = await joinMeeting(Number(id));
          meetingResource.Attendees.push(attendee.Attendee);
        }

        await setUpMeeting(
          `${id}`,
          meetingResource,
          meetingManager,
          setMeetingId,
          setJoinInfo,
          setMeetingMode,
        );

        setJoinedMeeting();

        if (
          ![MeetingStatus.DOCTOR_JOINED, MeetingStatus.BOTH_JOINED].includes(
            meeting.status,
          )
        ) {
          await handleUpdateMeeting(
            Number(id),
            meetingResource.Meeting.MeetingId,
            meeting.status === MeetingStatus.PATIENT_JOINED ||
              meetingSubscription?.status === MeetingStatus.PATIENT_JOINED
              ? MeetingStatus.BOTH_JOINED
              : MeetingStatus.DOCTOR_JOINED,
          );

          await handleNotifyMeetingToPatient(Number(id));
        }
      } catch (error) {
        logger({ error, message: "failed to join meeting" });
        notification.error({
          message: "参加に失敗しました",
          duration: 3,
        });

        push("/start");
      }
    }
  };

  const exitTestDeviceMode = async () => {
    await meetingManager.leave();
    setIsTestDeviceMode(false);
  };

  const getErrorMessage = (meeting: Meeting, isPharmacy: boolean) => {
    if ([MeetingStatus.END, MeetingStatus.DISABLED].includes(meeting.status))
      return "URLの有効期間が切れています。";

    if (
      isPharmacy &&
      meeting.pharmacyReserve?.desiredDateStatus !== DesiredDateStatus.SET
    ) {
      return "服薬指導希望日時を設定してください。";
    }

    if (!isPharmacy && !meeting?.reservation)
      return "権限がないため、参加できません。";

    return "";
  };

  useEffect(() => {
    if (!meeting) return;

    setMeetingError(getErrorMessage(meeting, isPharmacy));
  }, [isPharmacy, meeting]);

  if (!meeting) return null;

  if (meetingError) {
    return <ErrorNotification message={meetingError} />;
  }

  return isLeavedMeeting ? (
    isPharmacy && (
      <LeaveMeetingModal
        pharmacyReserve={meeting.pharmacyReserve!}
        customer={meeting.pharmacyReserve!.customer!}
        closeModal={() => push("/pharmacy/start")}
      />
    )
  ) : meetingManager.meetingId || isTestDeviceMode ? (
    <MeetingComponent
      isTestDeviceMode={isTestDeviceMode}
      exitTestDeviceMode={exitTestDeviceMode}
    />
  ) : (
    <MeetingWaiting
      meeting={meetingSubscription || meeting}
      requestJoinMeeting={handleJoinMeeting}
      requestTestDevice={() => setIsTestDeviceMode(true)}
    />
  );
};
