import React, {
  useEffect,
  useState,
  useMemo,
  useCallback,
  useRef,
} from "react";

import styled from "styled-components";
import { Flex, Spin } from "antd";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { usePostApiEpsGetPrescriptionFromCsvDataMutation } from "@/apis/gql/operations/__generated__/karte-retry-cancel";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/system-config";
import { System } from "@/utils/socket-helper";
import { RenderIf } from "@/utils/common/render-if";
import { usePostApiEpsSavePrescriptionInfoMutation } from "@/apis/gql/operations/__generated__/duplicate-medication";
import { useLock } from "@/features/karte/providers/LockInforProvider";
import { useUpsertEpsRegister } from "@/hooks/useUpsertEpsRegister";
import { useGetApiEpsValidateBeforePrintingOutPatientQuery } from "@/apis/gql/operations/__generated__/print-out-patient";
import { useReadFileXML } from "@/hooks/useReadFileXML";

import { useEPrescriptionContext } from "../PrintSetting/EPrescriptionContextProvider";
import { useCreateDrugXml } from "../CreatePrescriptionInformation/useCreateDrugXml";
import { useObserverWaitingModalContext } from "../StationPrescription/ObserverWaitingModalActionProvider";
import { useCancelRegister } from "../CancelRegister";
import { usePrintOutPatientPrescription } from "../PrintOutpatientPrescription/usePrintOutPatientPrescription";

import { ModalConfirm } from "./ModalConfirm";

import type { PrescriptionDrugLocalFlow } from "../FlowLocalPresciption";
import type { Dispatch, SetStateAction } from "react";
import type {
  IFile,
  ISystemRequestConfig,
} from "@/utils/socket-helper/socket.type";
import type {
  DomainModelsEpsDispensingGroupDetailModel,
  DomainModelsEpsPrescriptionEpsPrescriptionModel,
  DomainModelsEpsReqEpsReqModel,
  DomainModelsEpsSaveEpsPrescriptionInfoModel,
  EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput,
} from "@/apis/gql/generated/types";
import type { IResponse } from "@/utils/socket-helper/socket.type";

const StyleModal = styled(Modal)`
  .ant-modal-footer {
    justify-content: center;
  }
`;

const Wrapper = styled.div`
  padding: 24px 32px 24px 24px;
  height: 256px;
`;

type Props = {
  setProcessRecordPrescription: Dispatch<
    SetStateAction<PrescriptionDrugLocalFlow | null>
  >;
  prescription: PrescriptionDrugLocalFlow;
  setPrecriptionNew: Dispatch<
    SetStateAction<EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput | null>
  >;
  setXmlError: (value: string, isSkip?: boolean) => void;
};
export const ModalWaitingCreateOrUpdateFile = ({
  setProcessRecordPrescription,
  prescription,
  setPrecriptionNew,
  setXmlError,
}: Props) => {
  const { handleCloseModal } = useModal();
  const { timeout } = useCreateDrugXml({});

  const { raiinNo, ptId, sinDate } = useGetOrderInfoContext();
  const [message, setMessage] = useState("処方箋情報を登録しています");
  const [disabled, setDisabled] = useState(true);
  const [showModalConfirm, setShowModalConfirm] = useState(false);
  const [_openModal, setOpenModal] = useState(true);
  const [convertCSVTOData] = usePostApiEpsGetPrescriptionFromCsvDataMutation();
  const { statePrintSetting, updateEpsRegister } = useEPrescriptionContext();
  const { handleUpsertEpsRegister } = useUpsertEpsRegister();
  const { handlePrintOutPatientPrescriptionPaper } =
    usePrintOutPatientPrescription();
  const [registerResult, setRegisterResult] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const [createFileResult, setCreateFileResult] =
    useState<IResponse<IFile> | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const { handleError } = useErrorHandler();
  const { changeLockMode } = useLock();
  const controllerAbort = useRef<AbortController | null>(null);

  const { data: responseCheckBeforePrintPrescription } =
    useGetApiEpsValidateBeforePrintingOutPatientQuery({
      variables: {
        sinDate,
        raiinNo,
        ptId,
      },
      onError: (error) => {
        handleCloseModal("WAITING_CREATE_OR_DELETE_FILE");
        handleError({ error });
      },
      fetchPolicy: "no-cache",
    });

  const {
    setResolveBreakModalFlowLocalPrescription,
    currentFlowRef,
    setResolveBreakModal,
    setError,
    setResolveWaitModal,
  } = useObserverWaitingModalContext();
  const [postApiEpsSavePrescriptionInfo] =
    usePostApiEpsSavePrescriptionInfoMutation({
      onError: (error) => {
        handleCloseModal("WAITING_CREATE_OR_DELETE_FILE");
        handleError({ error });
      },
    });

  const { readFileXML } = useReadFileXML();

  const { data: dataSystemConf } = useGetApiSystemConfGetListQuery({
    onError: (error) => {
      handleCloseModal("WAITING_CREATE_OR_DELETE_FILE");
      handleError({ error });
    },
  });

  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;

  const system = useMemo(
    () => new System("/medical", systemConf),
    [systemConf],
  );

  const { handleCancelRegister } = useCancelRegister();

  const hasCalledApi = React.useRef(false);

  const handleClickButton = () => {
    if (statePrintSetting.isOutpatientPrescription === false) {
      if (currentFlowRef.current === "LOCAL") {
        setResolveBreakModalFlowLocalPrescription();
      } else {
        setResolveBreakModal();
      }
      setResolveWaitModal();
      handleCloseModal("WAITING_CREATE_OR_DELETE_FILE");
      handlePrintOutPatientPrescriptionPaper().then();
    } else {
      setShowModalConfirm(true);
    }
  };

  const onSaveAndUpdatePrescription = useCallback(
    async (
      prescriptionResult: EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput[],
    ) => {
      let isError = false;
      await postApiEpsSavePrescriptionInfo({
        variables: {
          input: {
            prescriptionInfos: prescriptionResult,
          },
        },
        onError: (error) => {
          isError = true;
          handleError({ error });
        },
      });
      return isError;
    },
    [postApiEpsSavePrescriptionInfo, handleError],
  );

  useEffect(() => {
    if (timeout > 0) {
      setDisabled(true); // Reset disabled state when message changes
      const timer = setTimeout(() => {
        setDisabled(false);
      }, timeout * 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [timeout, message]);

  const handlePrescriptionData = useCallback(
    async (
      prescriptionInfo: string,
      isUpdatePrescription: boolean,
      refileCount: number,
      modalPrescription?: DomainModelsEpsPrescriptionEpsPrescriptionModel,
    ) => {
      const prescriptionData = await convertCSVTOData({
        variables: {
          emrCloudApiRequestsEpsGetPrescriptionFromCsvRequestInput: {
            prescriptionCsvBase64Data: prescriptionInfo,
          },
        },
        onError: (error) => {
          handleError({ error });
        },
      });

      const arrayPrescription = [];
      let isError = false;
      if (prescriptionData.data?.postApiEpsGetPrescriptionFromCsvData?.data) {
        const insuranceInformation: DomainModelsEpsDispensingGroupDetailModel[] =
          [];

        prescriptionData.data.postApiEpsGetPrescriptionFromCsvData.data?.dispensingGroupDetailModels?.map(
          (item) => {
            if (item.groupTitle === "保険情報") {
              insuranceInformation.push(item);
            }
          },
        );

        const hokensyaNo =
          insuranceInformation.find((item) => item.item === "保険者番号")
            ?.data || "";

        const kigo =
          insuranceInformation.find((item) => item.item === "被保険者証記号")
            ?.data || "";

        const kohiFutansyaNo =
          hokensyaNo === ""
            ? insuranceInformation.find(
                (item) => item.item === "第一公費負担者番号",
              )?.data || ""
            : "";

        const kohiJyukyusyaNo =
          hokensyaNo === ""
            ? insuranceInformation.find(
                (item) => item.item === "第二公費負担者番号",
              )?.data || ""
            : "";

        const bango =
          insuranceInformation.find((item) => item.item === "被保険者証番号")
            ?.data || "";

        const edaNo =
          insuranceInformation.find((item) => item.item === "被保険者証枝番")
            ?.data || "";

        if (isUpdatePrescription && modalPrescription) {
          modalPrescription.status = 2;
          modalPrescription.deletedReason = 2;
          arrayPrescription.push(modalPrescription);
        }

        const precriptionNew: EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput =
          {
            ptId: String(ptId),
            raiinNo: String(raiinNo),
            sinDate: Number(sinDate),
            accessCode: "",
            deletedReason: 0,
            issueType: statePrintSetting.outpatientOption === "3" ? 2 : 1,
            prescriptionDocument: prescriptionInfo,
            prescriptionId: "",
            refileCount: refileCount ?? 1,
            status: 0,
            kohiFutansyaNo,
            kohiJyukyusyaNo,
            bango,
            edaNo,
            kigo,
            hokensyaNo,
          };

        arrayPrescription.push(precriptionNew);

        // debugger;
        const response = await postApiEpsSavePrescriptionInfo({
          variables: {
            input: {
              prescriptionInfos: arrayPrescription,
            },
          },
          onError: (error) => {
            console.log("error", error);
            handleError({ error });

            if (controllerAbort.current) {
              controllerAbort.current.abort();
            }

            if (currentFlowRef.current === "LOCAL") {
              setResolveBreakModalFlowLocalPrescription();
            } else {
              setResolveBreakModal();
            }

            setResolveWaitModal();
            if (createFileResult?.data?.fileName?.includes("err")) {
              setError("ERROR");
            } else {
              setError("XML");
            }

            setXmlError(
              "処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？",
              true,
            );

            handleCancelRegister(3);

            isError = true;
          },
        });

        const result =
          response?.data?.postApiEpsSavePrescriptionInfo?.data
            ?.epsPrescriptions?.[0] || null;

        return { precriptionNew, result, isError };
      }
      return { precriptionNew: null, result: null, isError: false };
    },
    [
      convertCSVTOData,
      statePrintSetting,
      ptId,
      raiinNo,
      sinDate,
      postApiEpsSavePrescriptionInfo,
      handleError,
    ],
  );

  const handlePrescriptionResult = useCallback(
    async (
      registerResult: DomainModelsEpsReqEpsReqModel | null,
      createFileResult: IResponse<IFile>,
      prescriptionResult: {
        precriptionNew: EmrCloudApiRequestsEpsSavePrescriptionInfoRequestPrescriptionInfoRequestInput;
        result: DomainModelsEpsSaveEpsPrescriptionInfoModel | null;
      } | null,
      isUpdatePrescription: boolean,
      modalPrescription?: DomainModelsEpsPrescriptionEpsPrescriptionModel,
    ) => {
      const { msgBodyGetInfo, msgHeaderGetInfo } =
        readFileXML(createFileResult);

      if (registerResult) {
        await updateEpsRegister(registerResult, createFileResult);
      }

      // Helper function to handle error cases
      const handleErrorCase = async (errorMessage: string, isSkip = true) => {
        if (controllerAbort.current) {
          controllerAbort.current.abort();
        }

        if (currentFlowRef.current === "LOCAL") {
          setResolveBreakModalFlowLocalPrescription();
        } else {
          setResolveBreakModal();
        }

        setResolveWaitModal();
        if (createFileResult?.data?.fileName?.includes("err")) {
          setError("ERROR");
        } else {
          setError("XML");
        }

        setXmlError(errorMessage, isSkip);
        handleCancelRegister(3);
      };

      // Helper function to update prescription list
      const updatePrescriptionList = (
        prescriptionId?: string,
        accessCode?: string,
        status?: number,
      ) => {
        const listPrescriptionUpdate = [];

        if (prescriptionResult) {
          if (prescriptionId)
            prescriptionResult.precriptionNew.prescriptionId = prescriptionId;
          if (accessCode)
            prescriptionResult.precriptionNew.accessCode = accessCode;
          if (status) prescriptionResult.precriptionNew.status = status;

          listPrescriptionUpdate.push({
            ...prescriptionResult.precriptionNew,
            seqNo: prescriptionResult.result?.seqNo ?? "0",
            deletedReason:
              status === 3
                ? 3
                : prescriptionResult.precriptionNew.deletedReason,
          });
        }

        if (isUpdatePrescription && modalPrescription) {
          modalPrescription.status = 3;
          listPrescriptionUpdate.push(modalPrescription);
        }

        return listPrescriptionUpdate;
      };

      // Helper function to handle successful prescription processing
      const handleSuccessCase = async () => {
        const listPrescriptionUpdate = updatePrescriptionList(
          msgBodyGetInfo?.PrescriptionId,
          msgBodyGetInfo?.AccessCode,
        );

        if (listPrescriptionUpdate.length > 0) {
          const result = await onSaveAndUpdatePrescription(
            listPrescriptionUpdate,
          );
          if (result) {
            await handleErrorCase(
              "処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？",
              true,
            );
            return;
          }
        }

        if (statePrintSetting.outpatientOption === "1") {
          setProcessRecordPrescription(
            (prev: PrescriptionDrugLocalFlow | null) => {
              if (!prev) return null;
              return {
                ...prev,
                refileCount: prev.refileCount,
                prescriptionDocument: prev.prescriptionDocument,
              };
            },
          );
          setPrecriptionNew(prescriptionResult?.precriptionNew ?? null);
        }

        setResolveWaitModal();
      };

      // Case 1: Success case
      const isSuccess =
        msgBodyGetInfo?.ProcessingResultStatus === "1" &&
        msgHeaderGetInfo?.SegmentOfResult === "1";

      // Case 2: Warning case but treat as success
      const isWarningAsSuccess =
        msgBodyGetInfo?.ProcessingResultCode === "EPSB1010W" ||
        msgHeaderGetInfo?.ErrorCode === "EPSB1010W";

      if (isSuccess || isWarningAsSuccess) {
        await handleSuccessCase();
        return;
      }

      // Case 3: Error case - handle other errors here
      const listPrescriptionUpdate = updatePrescriptionList(
        undefined,
        undefined,
        3,
      );

      if (listPrescriptionUpdate.length > 0) {
        const result = await onSaveAndUpdatePrescription(
          listPrescriptionUpdate,
        );
        if (result) {
          await handleErrorCase(
            "処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？",
            true,
          );
          return;
        }
      }

      // Handle general error case
      if (currentFlowRef.current === "LOCAL") {
        setResolveBreakModalFlowLocalPrescription();
      } else {
        setResolveBreakModal();
      }

      setResolveWaitModal();
      if (createFileResult?.data?.fileName?.includes("err")) {
        setError("ERROR");
      } else {
        setError("XML");
      }

      setXmlError(createFileResult?.data?.content, false);
      handleCancelRegister(3);
    },
    [
      statePrintSetting,
      onSaveAndUpdatePrescription,
      setPrecriptionNew,
      setProcessRecordPrescription,
      setError,
      setXmlError,
      currentFlowRef,
      handleCancelRegister,
      setResolveBreakModal,
      setResolveBreakModalFlowLocalPrescription,
      setResolveWaitModal,
      readFileXML,
      updateEpsRegister,
    ],
  );

  const hasProcessedRef = useRef(false);

  const epsPrescriptions =
    responseCheckBeforePrintPrescription?.getApiEpsValidateBeforePrinting?.data
      ?.epsPrescriptions;

  const hasEpsPrescriptionsData = useMemo(() => {
    return Boolean(epsPrescriptions?.length);
  }, [epsPrescriptions]);

  const prescriptionData = useMemo(
    () => ({
      refileCount: prescription.refileCount,
      prescriptionDocument: prescription.prescriptionDocument,
      base64DrugSignedXml: prescription.base64DrugSignedXml,
    }),
    [
      prescription.refileCount,
      prescription.prescriptionDocument,
      prescription.base64DrugSignedXml,
    ],
  );

  useEffect(() => {
    if (
      isProcessing ||
      hasCalledApi.current ||
      hasProcessedRef.current ||
      epsPrescriptions === undefined ||
      epsPrescriptions === null
    )
      return;

    console.log("Đã vào flow 5.5.3!");
    const processData = async () => {
      if (hasProcessedRef.current) return;
      hasProcessedRef.current = true;
      setIsProcessing(true);

      controllerAbort.current = new AbortController();
      const signal = controllerAbort.current.signal;
      const configSignal: ISystemRequestConfig = { signal };

      // debugger;
      const prescriptionUpdate = epsPrescriptions?.find(
        (item) => item.refileCount === prescriptionData.refileCount,
      );
      const prescriptionCreate = !prescriptionUpdate;

      setMessage("処方箋情報を登録しています");
      setOpenModal(true);

      const prescriptionInfo = prescriptionData.prescriptionDocument;

      if (prescriptionData.base64DrugSignedXml || prescriptionInfo) {
        const requestType = prescriptionCreate
          ? "EPSsiPIR01req"
          : "EPSsiPIR03req";
        const modalPrescription = prescriptionUpdate
          ? {
              ptId: String(prescriptionUpdate.ptId),
              raiinNo: String(prescriptionUpdate.raiinNo),
              sinDate: Number(prescriptionUpdate.sinDate),
              accessCode: prescriptionUpdate.accessCode || "",
              deletedReason: prescriptionUpdate.deletedReason || 0,
              issueType: prescriptionUpdate.issueType,
              prescriptionDocument: prescriptionUpdate.prescriptionDocument,
              prescriptionId: prescriptionUpdate.prescriptionId || "",
              refileCount: prescriptionUpdate.refileCount || 1,
              status: prescriptionUpdate.status || 0,
              kohiFutansyaNo: prescriptionUpdate.kohiFutansyaNo || "",
              kohiJyukyusyaNo: prescriptionUpdate.kohiJyukyusyaNo || "",
              bango: prescriptionUpdate.bango || "",
              edaNo: prescriptionUpdate.edaNo || "",
              kigo: prescriptionUpdate.kigo || "",
              hokensyaNo: prescriptionUpdate.hokensyaNo || "",
              seqNo: prescriptionUpdate.seqNo || "1",
            }
          : undefined;

        hasCalledApi.current = true;

        const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;

        const registerResult = await handleUpsertEpsRegister({
          arbitraryFileIdentifier,
          dispensingResultId: "",
          prescriptionId: prescriptionUpdate
            ? (prescriptionUpdate?.prescriptionId ?? "")
            : "",
          raiinNo: raiinNo,
          ptId: ptId,
          sinDate: sinDate,
          reqType: statePrintSetting.outpatientOption !== "3" ? 3 : 4,
          status: 1,
          resultCode: "",
          resultMessage: "",
          result: "",
          reqDate: +dayjs().format("YYYYMMDD"),
        });

        const [createFileResult, prescriptionResult] = await Promise.all([
          system.createFile(
            {
              messageHeader: {},
              messageBody: { PrescriptionInfo: prescriptionInfo },
            },
            requestType,
            configSignal,
          ),
          handlePrescriptionData(
            prescriptionInfo,
            prescriptionUpdate !== undefined,
            prescriptionData.refileCount,
            modalPrescription,
          ),
        ]);

        setOpenModal(false);
        // setOpenModal(false);
        setMessage("処方箋情報を登録結果を取得しています");
        setOpenModal(true);

        if (createFileResult) {
          if (registerResult) {
            setRegisterResult(registerResult);
          }

          if (!prescriptionResult.precriptionNew) {
            setIsProcessing(false);
            handleCloseModal("WAITING_CREATE_OR_DELETE_FILE");
            return;
          }

          setCreateFileResult(createFileResult);
          await handlePrescriptionResult(
            registerResult,
            createFileResult,
            {
              precriptionNew: prescriptionResult.precriptionNew,
              result: prescriptionResult.result,
            },
            prescriptionUpdate !== undefined,
            modalPrescription,
          );
        }
      }

      setIsProcessing(false);
      handleCloseModal("WAITING_CREATE_OR_DELETE_FILE");
    };

    processData();
  }, [
    isProcessing,
    system,
    prescriptionData,
    hasEpsPrescriptionsData,
    handlePrescriptionData,
    handlePrescriptionResult,
    handleCloseModal,
  ]);

  const handleClose = async () => {
    // debugger;
    changeLockMode(false);

    if (controllerAbort.current) {
      controllerAbort.current.abort();
    }
    // handleDisconnectSocket();
    // connect.disconnect();
    handleCancelRegister();
    setResolveWaitModal();
    if (currentFlowRef.current === "LOCAL") {
      setResolveBreakModalFlowLocalPrescription();
    } else {
      setResolveBreakModal();
    }

    if (registerResult) {
      await updateEpsRegister(registerResult, createFileResult);
    }

    handleCloseModal("WAITING_CREATE_OR_DELETE_FILE");
    setDisabled(false);
    setOpenModal(false);
  };

  return (
    <StyleModal
      width={480}
      isOpen={true}
      title={"処理中"}
      onCancel={handleClose}
      footer={[
        <Button
          key="close"
          varient="tertiary"
          shape="round"
          onClick={handleClose}
          disabled={disabled}
        >
          キャンセル
        </Button>,
      ]}
    >
      <Wrapper>
        <Flex
          style={{
            minHeight: 200,
            flexDirection: "column",
            justifyContent: "space-between",
          }}
        >
          <Flex
            align="center"
            justify="center"
            style={{
              flexDirection: "column",
              marginTop: "auto",
              marginBottom: "auto",
            }}
          >
            <p style={{ textAlign: "center" }}>{message}</p>
            <Flex align="center" justify="center" style={{ marginTop: 24 }}>
              <Spin size="large" />
            </Flex>
          </Flex>

          <Flex justify="flex-end">
            <Button
              varient="inline"
              disabled={disabled}
              onClick={handleClickButton}
            >
              処方箋情報を登録しない
            </Button>
          </Flex>
        </Flex>
      </Wrapper>
      <RenderIf condition={showModalConfirm}>
        <ModalConfirm
          onClose={() => {
            setShowModalConfirm(false);
            handleCloseModal("WAITING_CREATE_OR_DELETE_FILE");
          }}
          registerResult={registerResult}
          createFileResult={createFileResult}
          updateEpsRegister={updateEpsRegister}
        />
      </RenderIf>
    </StyleModal>
  );
};
