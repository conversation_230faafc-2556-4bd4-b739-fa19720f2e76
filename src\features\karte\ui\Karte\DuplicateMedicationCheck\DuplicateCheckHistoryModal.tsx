import React, { useEffect, useMemo, useState } from "react";

import styled from "styled-components";
import { Checkbox, Tooltip } from "antd";
import { Controller, useForm } from "react-hook-form";
import dayjs from "dayjs";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { RenderIf } from "@/utils/common/render-if";
import { usePostApiEpsSaveDuplicateMedicationCheckMutation } from "@/apis/gql/operations/__generated__/duplicate-medication";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { ErrorText } from "@/components/ui/ErrorText";
import { useEPrescriptionContext } from "@/features/karte/ui/Karte/PrintSetting/EPrescriptionContextProvider";
import { usePrintSetting } from "@/features/karte/ui/Karte/PrintSetting/usePrintSetting";

import { BaseModal } from "./BaseModal";
import { TableCheck } from "./TableCheck";

import type { DomainModelsEpsChkEpsChkModel } from "@/apis/gql/generated/types";

const StyleModal = styled(Modal)<{ $isBetween?: boolean }>`
  .ant-modal-footer {
    justify-content: ${(props) =>
      props?.$isBetween ? "space-between" : "center"};
  }

  .ant-modal-header {
    background-color: #005bac;
    color: white;
    border-bottom: none;
  }
`;

const Wrapper = styled.div`
  display: flex;
`;

const WrapCheckDate = styled.div`
  width: 200px;
  min-width: 200px;
  height: 632px;
  flex-grow: 0;
  border-right: solid 1px #e2e3e5;
  background-color: #fff;
`;

const DateTitle = styled.div`
  height: 36px;
  flex-grow: 0;
  padding: 11px 12px;
  background-color: #e0e6ec;
  font-family: NotoSansJP;
  font-size: 14px;
  font-weight: bold;
  line-height: 1;
  color: #243544;
`;

const ListDateContent = styled.div`
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  overflow-y: auto;
  max-height: 596px;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 4px;
  }
`;

const ItemDate = styled.div<{ $isDelete?: boolean }>`
  height: 32px;
  flex-grow: 0;
  padding: 9px 8px;
  border-radius: 6px;
  background-color: ${(props) => (props?.$isDelete ? "#eaf0f5" : "#fff")};

  font-family: Roboto;
  font-size: 14px;
  font-weight: normal;
  line-height: 1;
  color: #243544;
  cursor: pointer;
`;

const WrapperContent = styled.div`
  padding: 20px 24px 36px;
  width: 100%;
`;

const Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 22px;
`;

const HeaderWrap = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
`;

const HeaderContent = styled.div`
  height: 32px;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  background-color: #eaf0f5;
  .ant-checkbox,
  .ant-checkbox-wrapper,
  .ant-checkbox-checked {
    &:hover {
      .ant-checkbox-inner {
        background-color: #eaf0f5 !important;
      }
    }
  }
  .ant-checkbox-inner {
    width: 24px;
    height: 24px;
  }
  .ant-checkbox-checked .ant-checkbox-inner {
    background-color: #eaf0f5;
    border-color: transparent;
  }
  .ant-checkbox-checked .ant-checkbox-inner:after {
    border-color: #43c3d5;
  }
`;

const HeaderContentTitle = styled.div`
  font-family: NotoSansJP;
  font-size: 14px;
  line-height: 1;
  color: #243544;
`;

const CheckMessage = styled.div`
  font-family: NotoSansJP;
  font-size: 14px;
  line-height: 1;
  color: #243544;
  margin-bottom: 20px;
`;

const CheckboxResultWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  .ant-checkbox-inner {
    width: 24px;
    height: 24px;
  }
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 6px;
  font-size: 12px;
  word-wrap: break-word;
`;

type BaseModalProps = {
  onClose: () => void;
  isCheckHeader?: boolean;
  data: DomainModelsEpsChkEpsChkModel[];
};

export const DuplicateCheckHistoryModal: React.FC<BaseModalProps> = ({
  onClose,
  isCheckHeader,
  data,
}) => {
  const {
    handleConsentPatient,
    handleUpdateKarte,
    isDisableAcquisitionDrug,
    handleGetPrescriptionIdList,
    flagRegister,
    statePrintSetting,
  } = useEPrescriptionContext();

  const [isOpenAcquisition, setIsOpenAcquisition] = useState<boolean>(false);

  const [activeDuplicateData, setActiveDuplicateData] = useState<
    DomainModelsEpsChkEpsChkModel | undefined
  >(data[0]);

  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { handleError } = useErrorHandler();
  const { handlePrintPDF } = usePrintSetting();

  const [postApiEpsSaveDuplicateMedicationCheck] =
    usePostApiEpsSaveDuplicateMedicationCheckMutation({
      onError: (error) => {
        handleError({ error });
      },
    });

  const { control, watch, setValue, getValues, reset, trigger } =
    useForm<DomainModelsEpsChkEpsChkModel>({
      defaultValues: activeDuplicateData,
    });

  useEffect(() => {
    if (!activeDuplicateData) return;
    reset(activeDuplicateData);
  }, [activeDuplicateData, reset]);

  const onClickSubmit = async () => {
    setIsLoading(true);
    const isValid = await trigger();
    if (isValid) {
      onSubmit(getValues());
    }
    setIsLoading(false);
  };

  const onSubmit = async (data: DomainModelsEpsChkEpsChkModel) => {
    try {
      const formattedData = {
        ptId: String(data?.ptId),
        raiinNo: String(data?.raiinNo),
        sinDate: Number(data?.sinDate),
        seqNo: Number(data?.seqNo),
        checkResult: Number(data?.checkResult),
        oralBrowsingConsent: Number(data?.oralBrowsingConsent),
        drugInfo: String(data?.drugInfo),
        epsChkDetailList: data?.epsChkDetailModels?.length
          ? data?.epsChkDetailModels.map((detail) => ({
              ptId: String(detail?.ptId),
              raiinNo: String(detail?.raiinNo),
              messageId: String(detail?.messageId) || "",
              messageCategory: detail?.messageCategory,
              pharmaceuticalsIngredientName:
                detail?.pharmaceuticalsIngredientName,
              message: detail?.message || "",
              targetPharmaceuticalCodeType:
                detail?.targetPharmaceuticalCodeType,
              targetPharmaceuticalCode: detail?.targetPharmaceuticalCode,
              targetPharmaceuticalName: detail?.targetPharmaceuticalName,
              targetDispensingQuantity: detail?.targetDispensingQuantity,
              targetUsage: detail?.targetUsage,
              targetDosageForm: detail?.targetDosageForm,
              pastDate: detail?.pastDate,
              pastPharmaceuticalCodeType: detail?.pastPharmaceuticalCodeType,
              pastPharmaceuticalCode: detail?.pastPharmaceuticalCode,
              pastPharmaceuticalName: detail?.pastPharmaceuticalName,
              pastMedicalInstitutionName: detail?.pastMedicalInstitutionName,
              pastInsurancePharmacyName: detail?.pastInsurancePharmacyName,
              pastDispensingQuantity: detail?.pastDispensingQuantity,
              pastUsage: detail?.pastUsage,
              pastDosageForm: detail?.pastDosageForm,
              comment: detail?.comment,
            }))
          : [],
      };
      const isSaveSuccess = await handleUpdateKarte();
      console.log("isSaveSuccess", isSaveSuccess);
      const statePrint = {
        ...statePrintSetting,
        isOutpatientPrescription: false,
      };
      await handlePrintPDF(statePrint);

      postApiEpsSaveDuplicateMedicationCheck({
        variables: {
          input: formattedData,
        },
        onCompleted: async () => {
          console.log("isSaveSuccess", isSaveSuccess);
          if (isSaveSuccess && flagRegister === "ON") {
            //TODO: Step 5.2
            handleGetPrescriptionIdList();
            setIsLoading(false);
          }
        },
        onError: (error) => {
          handleError({ error });
          setIsLoading(false);
        },
      });
    } catch (error) {
      onClose();
      handleError({ error: error as Error });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcquisitionModal = () => {
    setIsOpenAcquisition(!isOpenAcquisition);
  };

  const handleConfirmAcquisition = () => {
    setIsOpenAcquisition(false);
    handleConsentPatient();
  };

  const messageCheck = useMemo(() => {
    const result = activeDuplicateData?.checkResult;
    switch (result) {
      case 0:
        return "チェック結果を確認の上で投薬する場合は、投薬理由コメントを入力してください。";
      case 1:
        return "処方内容に重複投薬や併用禁忌がありました。";
      case 2:
        return "処方内容に重複投薬や併用禁忌はありません。";
      default:
        return "";
    }
  }, [activeDuplicateData]);

  return (
    <>
      <StyleModal
        width={1280}
        isOpen
        title={
          isCheckHeader
            ? "重複投薬等チェック結果画面"
            : "重複投薬等チェック履歴"
        }
        onCancel={onClose}
        $isBetween={!isCheckHeader}
        footer={[
          <Button
            key="close"
            varient="tertiary"
            shape="round"
            onClick={onClose}
          >
            閉じる
          </Button>,
          !isCheckHeader && (
            <Button
              key="confirm"
              varient="primary"
              shape="round"
              form="duplicate-check-form"
              disabled={activeDuplicateData?.isDeleted === 1 || isLoading}
              onClick={onClickSubmit}
              // loading={isLoading}
            >
              確定
            </Button>
          ),
        ]}
      >
        <form id="duplicate-check-form">
          <Wrapper>
            {!isCheckHeader ? (
              <WrapCheckDate>
                <DateTitle>チェック日時</DateTitle>
                <ListDateContent>
                  {data &&
                    data.map((item) => (
                      <ItemDate
                        $isDelete={item?.isDeleted === 1}
                        key={item?.createDate}
                        onClick={() => setActiveDuplicateData(item)}
                      >
                        {dayjs(item?.createDate).format("YYYY/MM/DD HH:mm:ss")}{" "}
                      </ItemDate>
                    ))}
                </ListDateContent>
              </WrapCheckDate>
            ) : null}
            <WrapperContent>
              <Header>
                <HeaderWrap>
                  <HeaderContent>
                    <HeaderContentTitle>チェック対象：</HeaderContentTitle>
                    <Controller
                      name="sameMedicalInstitutionAlertFlg"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          {...field}
                          checked={field.value === 2}
                          onChange={(value) => {
                            field.onChange(value.target.checked ? 2 : 1);
                          }}
                        >
                          自院分含む
                        </Checkbox>
                      )}
                    />
                  </HeaderContent>
                  <HeaderContent>
                    <HeaderContentTitle>薬剤閲覧：</HeaderContentTitle>
                    <Controller
                      name="onlineConsent"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          {...field}
                          checked={field.value === 1}
                          onChange={(value) => {
                            field.onChange(value.target.checked ? 1 : 0);
                          }}
                        >
                          マイナ同意
                        </Checkbox>
                      )}
                    />
                    <Controller
                      name="oralBrowsingConsent"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          {...field}
                          checked={field.value === 1}
                          onChange={(value) => {
                            field.onChange(value.target.checked ? 1 : 0);
                          }}
                        >
                          口頭同意
                        </Checkbox>
                      )}
                    />
                  </HeaderContent>
                </HeaderWrap>
                <Tooltip title="チェック対象薬剤を表示するには患者から同意を得てください">
                  <Button
                    key="confirm"
                    varient="secondary"
                    shape="round"
                    onClick={handleAcquisitionModal}
                    disabled={
                      !isDisableAcquisitionDrug ||
                      activeDuplicateData?.isDeleted === 1 ||
                      (isDisableAcquisitionDrug &&
                        watch("oralBrowsingConsent") === 1) ||
                      (isDisableAcquisitionDrug && watch("onlineConsent") === 1)
                    }
                  >
                    対象薬剤取得
                  </Button>
                </Tooltip>
              </Header>
              {messageCheck && <CheckMessage>{messageCheck}</CheckMessage>}

              <TableCheck
                data={activeDuplicateData?.epsChkDetailModels}
                watch={watch}
                isCheckHeader={isCheckHeader}
                control={control}
                onSetValue={setValue}
                getValues={getValues}
                isDisabled={activeDuplicateData?.isDeleted === 1}
              />
              {!isCheckHeader && (
                <CheckboxResultWrapper>
                  <Controller
                    name="checkResult"
                    control={control}
                    rules={{
                      validate: (value) => {
                        if (!value) {
                          return "処方内容に問題がない場合は、チェックを付けてください";
                        }
                        return true;
                      },
                    }}
                    render={({ field, fieldState }) => (
                      <div>
                        <Checkbox
                          {...field}
                          checked={field.value === 1}
                          disabled={field.value === 2}
                          onChange={(value) => {
                            field.onChange(value.target.checked ? 1 : 0);
                          }}
                        >
                          重複投薬等チェック結果を確認済みです。
                        </Checkbox>
                        {fieldState.error?.message && (
                          <StyledErrorText>
                            {fieldState.error.message}
                          </StyledErrorText>
                        )}
                      </div>
                    )}
                  />
                </CheckboxResultWrapper>
              )}
            </WrapperContent>
          </Wrapper>
        </form>
      </StyleModal>
      <RenderIf condition={isOpenAcquisition}>
        <BaseModal
          title="過去の処方/調剤結の取得"
          subtitle="薬剤情報を取得するには、患者の同意（口頭同意を含む）が必要です"
          description="患者から同意を得ましたか？"
          onClose={() => handleAcquisitionModal()}
          btnCloseTitle="キャンセル"
          btnConfirm={{
            title: "患者から同意を得たうえで取得",
            fitContent: true,
            onClick: handleConfirmAcquisition,
          }}
        />
      </RenderIf>
    </>
  );
};
