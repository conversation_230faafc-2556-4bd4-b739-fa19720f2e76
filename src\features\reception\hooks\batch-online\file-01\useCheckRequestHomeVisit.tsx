import { useCallback, useRef, useState } from "react";

import { useError<PERSON>and<PERSON> } from "@/hooks/useErrorHandler";
import { System } from "@/utils/socket-helper";
import { createXmlFileBatchOnlineCheckRequestHomeVisit } from "@/utils/socket-helper/onlineQualification";
import {
  CONFIRMATION_FILE_NAME,
  SystemHub,
  SystemScreenCode,
} from "@/constants/confirm-online";
import { usePostApiOnlineProcessXmloqSmuhvq01resMutation } from "@/apis/gql/operations/__generated__/batch-online";
import { RenderIf } from "@/utils/common/render-if";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { useCheckConctionAgent } from "@/hooks/useCheckConctionAgent";

import { BatchOnlineErrorModal } from "../../../ui/modals/BatchOnlineCheck/BatchOnlineErrorModal";
import { BatchOnlineLoadingModal } from "../../../ui/modals/BatchOnlineCheck/BatchOnlineLoadingModal";

type ValidateState = {
  type: "success" | "error";
  content: {
    messageTitle: string;
    messageContent: string;
  };
};

export const useCheckRequestHomeVisit = (onClose: () => void) => {
  const [isLoading, setLoading] = useState<boolean>(false);
  const waitingOQController = useRef<AbortController>();
  const [openValidateModal, setOpenValidateModal] = useState<ValidateState>();
  const { handleError } = useErrorHandler();
  const { notification } = useGlobalNotification();
  const { checkConnection } = useCheckConctionAgent();

  const [postApiOnlineProcessXMLOQSmuhvq01res] =
    usePostApiOnlineProcessXmloqSmuhvq01resMutation({
      onError: (error) => handleError({ error }),
    });

  const handleResponseFile = async (
    xmlString: string,
    fromDate: string,
    toDate: string,
  ) => {
    postApiOnlineProcessXMLOQSmuhvq01res({
      variables: {
        payload: {
          batchConfirmationType: 3,
          consentFrom: Number(fromDate),
          consentTo: Number(toDate),
          sinYm: 0,
          xmlString,
          examinationFrom: 0,
          examinationTo: 0,
          yoyakuDate: 0,
        },
      },
      onCompleted: (data) => {
        if (data.postApiOnlineProcessXMLOQSmuhvq01res?.status === 0) {
          onClose();
        } else {
          notification.error({
            message: data.postApiOnlineProcessXMLOQSmuhvq01res?.message,
          });
        }
      },
    });
  };

  const handleSendFileToAgentType3 = async (
    fromDate: string,
    toDate: string,
  ) => {
    try {
      const isConnected = await checkConnection();
      if (!isConnected) {
        return;
      }

      setLoading(true);
      waitingOQController.current?.abort();
      waitingOQController.current = new AbortController();

      const { fileName, msg } = createXmlFileBatchOnlineCheckRequestHomeVisit(
        fromDate,
        toDate,
      );

      const res = await onlineVisiting.createFile(
        msg.XmlMsg,
        fileName,
        { signal: waitingOQController.current?.signal },
        "CreateXmlFile",
      );

      if (!res || waitingOQController.current.signal.aborted) return;
      await onlineVisiting.moveFile({ files: [res.data.fileName] });
      if (
        res.data.fileName.includes(CONFIRMATION_FILE_NAME.REQUEST_HOME_VISIT)
      ) {
        const requestFileName = res.data.fileName.replace("err", "xml");
        await onlineVisiting.moveFile({ files: [requestFileName] });
        const match = res.data.content.match(/\[(.*?)\] (.*)/);
        if (match) {
          const processingResultCode = match[1];
          const errorMessage = match[2];
          setOpenValidateModal({
            type: "error",
            content: {
              messageTitle: "オンライン資格確認に失敗しました",
              messageContent: `処理結果コード: ${processingResultCode}\n${errorMessage}`,
            },
          });
          return;
        }
      }

      if (res.data?.content)
        handleResponseFile(res.data.content, fromDate, toDate);
    } catch (err) {
      if (err instanceof Error) {
        setOpenValidateModal({
          type: "error",
          content: {
            messageTitle: "オンライン資格確認に失敗しました",
            messageContent: "タイムアウト",
          },
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const ValidateCancelRequestComponentFile1Type3 = useCallback(() => {
    return (
      <>
        <RenderIf condition={openValidateModal?.type === "error"}>
          <BatchOnlineErrorModal
            onClose={() => setOpenValidateModal(undefined)}
            content={openValidateModal?.content}
          />
        </RenderIf>
        <RenderIf condition={isLoading}>
          <BatchOnlineLoadingModal
            content={{
              title: "処理中",
              heading: "オンライン資格確認システムからの結果を待っています",
            }}
            onClose={() => {
              setLoading(false);
              waitingOQController.current?.abort();
            }}
          />
        </RenderIf>
      </>
    );
  }, [isLoading, openValidateModal?.content, openValidateModal?.type]);

  return {
    isLoading,
    handleSendFileToAgentType3,
    ValidateCancelRequestComponentFile1Type3,
  };
};

class OnlineVisiting extends System {
  constructor() {
    super(SystemHub.PatientInf, [{}], {
      screenCode: SystemScreenCode.PatientInfo,
    });
  }
}

const onlineVisiting = new OnlineVisiting();
