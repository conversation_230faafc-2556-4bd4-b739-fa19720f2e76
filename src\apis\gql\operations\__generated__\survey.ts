import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as Apollo from "@apollo/client";
const defaultOptions = {} as const;
export type GetSurveysQueryVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.GetSurveysReq>;
}>;

export type GetSurveysQuery = {
  __typename?: "query_root";
  getSurveys: Array<{
    __typename?: "SurveyRes";
    surveyId: number;
    name: string;
    fQuesJson: string;
    createdDate: string;
    lastModifiedDate: string;
    secret?: string;
    isDeleted: number;
    createdAt: string;
  }>;
};

export type GetSurveysWithoutPermissionQueryVariables = Types.Exact<{
  input?: Types.InputMaybe<Types.GetSurveysReq>;
}>;

export type GetSurveysWithoutPermissionQuery = {
  __typename?: "query_root";
  getSurveysWithoutPermission: Array<{
    __typename?: "SurveyRes";
    surveyId: number;
    name: string;
    fQuesJson: string;
    createdDate: string;
    lastModifiedDate: string;
    secret?: string;
    isDeleted: number;
    createdAt: string;
  }>;
};

export type GetSurveyByIdQueryVariables = Types.Exact<{
  id: Types.Scalars["Int"]["input"];
}>;

export type GetSurveyByIdQuery = {
  __typename?: "query_root";
  getSurveyById: {
    __typename?: "SurveyRes";
    surveyId: number;
    name: string;
    fQuesJson: string;
  };
};

export type CreateSurveyMutationVariables = Types.Exact<{
  input: Types.SurveyCreateReq;
}>;

export type CreateSurveyMutation = {
  __typename?: "mutation_root";
  createSurvey: {
    __typename?: "SurveyRes";
    surveyId: number;
    fQuesJson: string;
    name: string;
  };
};

export type UpdateSurveyMutationVariables = Types.Exact<{
  input: Types.SurveyInformationReq;
}>;

export type UpdateSurveyMutation = {
  __typename?: "mutation_root";
  updateSurvey: {
    __typename?: "SurveyRes";
    surveyId: number;
    fQuesJson: string;
    name: string;
  };
};

export type DeleteSurveyByIdMutationVariables = Types.Exact<{
  id: Types.Scalars["Int"]["input"];
}>;

export type DeleteSurveyByIdMutation = {
  __typename?: "mutation_root";
  deleteSurveyById?: { __typename?: "SurveyRes"; surveyId: number };
};

export type DownloadSurveyPdfZipByDateRangeQueryVariables = Types.Exact<{
  input: Types.SurveyDateRangeReq;
}>;

export type DownloadSurveyPdfZipByDateRangeQuery = {
  __typename?: "query_root";
  downloadSurveyPDFZipByDateRange: {
    __typename?: "SurveyPDFURLRes";
    pdfURL: string;
  };
};

export type GetSurveyTemplatesQueryVariables = Types.Exact<{
  [key: string]: never;
}>;

export type GetSurveyTemplatesQuery = {
  __typename?: "query_root";
  getSurveyTemplates?: Array<{
    __typename?: "SurveyTemplate";
    surveyTemplateId: number;
    fQuesJson: string;
    name: string;
  }>;
};

export type GetSurveyAnswersQueryVariables = Types.Exact<{
  patientId: Types.Scalars["Int"]["input"];
}>;

export type GetSurveyAnswersQuery = {
  __typename?: "query_root";
  getSurveyAnswers: Array<{
    __typename?: "SurveyAnswer";
    surveyAnswerId: number;
    treatmentTitle?: string;
    treatmentType?: number;
    basicAnswerJson: string;
    customAnswerJson: string;
    createdAt: string;
    examStartDate: string;
    reserveDetailId: number;
  }>;
};

export type PostApiConsultationResultUpdateMutationVariables = Types.Exact<{
  emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput?: Types.InputMaybe<Types.EmrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput>;
}>;

export type PostApiConsultationResultUpdateMutation = {
  __typename?: "mutation_root";
  postApiConsultationResultUpdate?: {
    __typename?: "EmrCloudApiResponsesResponse1EmrCloudApiResponsesConsultationResultConsultationResultUpdateResponse";
    message?: string;
    status?: number;
    data?: {
      __typename?: "EmrCloudApiResponsesConsultationResultConsultationResultUpdateResponse";
      status?: number;
    };
  };
};

export const GetSurveysDocument = gql`
  query getSurveys($input: GetSurveysReq) {
    getSurveys(input: $input) {
      surveyId
      name
      fQuesJson
      createdDate
      lastModifiedDate
      secret
      isDeleted
      createdAt
    }
  }
`;

/**
 * __useGetSurveysQuery__
 *
 * To run a query within a React component, call `useGetSurveysQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveysQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveysQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetSurveysQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSurveysQuery,
    GetSurveysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetSurveysQuery, GetSurveysQueryVariables>(
    GetSurveysDocument,
    options,
  );
}
export function useGetSurveysLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveysQuery,
    GetSurveysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetSurveysQuery, GetSurveysQueryVariables>(
    GetSurveysDocument,
    options,
  );
}
export function useGetSurveysSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveysQuery,
    GetSurveysQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<GetSurveysQuery, GetSurveysQueryVariables>(
    GetSurveysDocument,
    options,
  );
}
export type GetSurveysQueryHookResult = ReturnType<typeof useGetSurveysQuery>;
export type GetSurveysLazyQueryHookResult = ReturnType<
  typeof useGetSurveysLazyQuery
>;
export type GetSurveysSuspenseQueryHookResult = ReturnType<
  typeof useGetSurveysSuspenseQuery
>;
export type GetSurveysQueryResult = Apollo.QueryResult<
  GetSurveysQuery,
  GetSurveysQueryVariables
>;
export const GetSurveysWithoutPermissionDocument = gql`
  query getSurveysWithoutPermission($input: GetSurveysReq) {
    getSurveysWithoutPermission(input: $input) {
      surveyId
      name
      fQuesJson
      createdDate
      lastModifiedDate
      secret
      isDeleted
      createdAt
    }
  }
`;

/**
 * __useGetSurveysWithoutPermissionQuery__
 *
 * To run a query within a React component, call `useGetSurveysWithoutPermissionQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveysWithoutPermissionQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveysWithoutPermissionQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useGetSurveysWithoutPermissionQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSurveysWithoutPermissionQuery,
    GetSurveysWithoutPermissionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSurveysWithoutPermissionQuery,
    GetSurveysWithoutPermissionQueryVariables
  >(GetSurveysWithoutPermissionDocument, options);
}
export function useGetSurveysWithoutPermissionLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveysWithoutPermissionQuery,
    GetSurveysWithoutPermissionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSurveysWithoutPermissionQuery,
    GetSurveysWithoutPermissionQueryVariables
  >(GetSurveysWithoutPermissionDocument, options);
}
export function useGetSurveysWithoutPermissionSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveysWithoutPermissionQuery,
    GetSurveysWithoutPermissionQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSurveysWithoutPermissionQuery,
    GetSurveysWithoutPermissionQueryVariables
  >(GetSurveysWithoutPermissionDocument, options);
}
export type GetSurveysWithoutPermissionQueryHookResult = ReturnType<
  typeof useGetSurveysWithoutPermissionQuery
>;
export type GetSurveysWithoutPermissionLazyQueryHookResult = ReturnType<
  typeof useGetSurveysWithoutPermissionLazyQuery
>;
export type GetSurveysWithoutPermissionSuspenseQueryHookResult = ReturnType<
  typeof useGetSurveysWithoutPermissionSuspenseQuery
>;
export type GetSurveysWithoutPermissionQueryResult = Apollo.QueryResult<
  GetSurveysWithoutPermissionQuery,
  GetSurveysWithoutPermissionQueryVariables
>;
export const GetSurveyByIdDocument = gql`
  query getSurveyById($id: Int!) {
    getSurveyById(id: $id) {
      surveyId
      name
      fQuesJson
    }
  }
`;

/**
 * __useGetSurveyByIdQuery__
 *
 * To run a query within a React component, call `useGetSurveyByIdQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveyByIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveyByIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetSurveyByIdQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetSurveyByIdQuery,
    GetSurveyByIdQueryVariables
  > &
    (
      | { variables: GetSurveyByIdQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetSurveyByIdQuery, GetSurveyByIdQueryVariables>(
    GetSurveyByIdDocument,
    options,
  );
}
export function useGetSurveyByIdLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveyByIdQuery,
    GetSurveyByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<GetSurveyByIdQuery, GetSurveyByIdQueryVariables>(
    GetSurveyByIdDocument,
    options,
  );
}
export function useGetSurveyByIdSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveyByIdQuery,
    GetSurveyByIdQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSurveyByIdQuery,
    GetSurveyByIdQueryVariables
  >(GetSurveyByIdDocument, options);
}
export type GetSurveyByIdQueryHookResult = ReturnType<
  typeof useGetSurveyByIdQuery
>;
export type GetSurveyByIdLazyQueryHookResult = ReturnType<
  typeof useGetSurveyByIdLazyQuery
>;
export type GetSurveyByIdSuspenseQueryHookResult = ReturnType<
  typeof useGetSurveyByIdSuspenseQuery
>;
export type GetSurveyByIdQueryResult = Apollo.QueryResult<
  GetSurveyByIdQuery,
  GetSurveyByIdQueryVariables
>;
export const CreateSurveyDocument = gql`
  mutation createSurvey($input: SurveyCreateReq!) {
    createSurvey(input: $input) {
      surveyId
      fQuesJson
      name
    }
  }
`;
export type CreateSurveyMutationFn = Apollo.MutationFunction<
  CreateSurveyMutation,
  CreateSurveyMutationVariables
>;

/**
 * __useCreateSurveyMutation__
 *
 * To run a mutation, you first call `useCreateSurveyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useCreateSurveyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [createSurveyMutation, { data, loading, error }] = useCreateSurveyMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useCreateSurveyMutation(
  baseOptions?: Apollo.MutationHookOptions<
    CreateSurveyMutation,
    CreateSurveyMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    CreateSurveyMutation,
    CreateSurveyMutationVariables
  >(CreateSurveyDocument, options);
}
export type CreateSurveyMutationHookResult = ReturnType<
  typeof useCreateSurveyMutation
>;
export type CreateSurveyMutationResult =
  Apollo.MutationResult<CreateSurveyMutation>;
export type CreateSurveyMutationOptions = Apollo.BaseMutationOptions<
  CreateSurveyMutation,
  CreateSurveyMutationVariables
>;
export const UpdateSurveyDocument = gql`
  mutation updateSurvey($input: SurveyInformationReq!) {
    updateSurvey(input: $input) {
      surveyId
      fQuesJson
      name
    }
  }
`;
export type UpdateSurveyMutationFn = Apollo.MutationFunction<
  UpdateSurveyMutation,
  UpdateSurveyMutationVariables
>;

/**
 * __useUpdateSurveyMutation__
 *
 * To run a mutation, you first call `useUpdateSurveyMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateSurveyMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateSurveyMutation, { data, loading, error }] = useUpdateSurveyMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateSurveyMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateSurveyMutation,
    UpdateSurveyMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateSurveyMutation,
    UpdateSurveyMutationVariables
  >(UpdateSurveyDocument, options);
}
export type UpdateSurveyMutationHookResult = ReturnType<
  typeof useUpdateSurveyMutation
>;
export type UpdateSurveyMutationResult =
  Apollo.MutationResult<UpdateSurveyMutation>;
export type UpdateSurveyMutationOptions = Apollo.BaseMutationOptions<
  UpdateSurveyMutation,
  UpdateSurveyMutationVariables
>;
export const DeleteSurveyByIdDocument = gql`
  mutation deleteSurveyById($id: Int!) {
    deleteSurveyById(id: $id) {
      surveyId
    }
  }
`;
export type DeleteSurveyByIdMutationFn = Apollo.MutationFunction<
  DeleteSurveyByIdMutation,
  DeleteSurveyByIdMutationVariables
>;

/**
 * __useDeleteSurveyByIdMutation__
 *
 * To run a mutation, you first call `useDeleteSurveyByIdMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useDeleteSurveyByIdMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [deleteSurveyByIdMutation, { data, loading, error }] = useDeleteSurveyByIdMutation({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDeleteSurveyByIdMutation(
  baseOptions?: Apollo.MutationHookOptions<
    DeleteSurveyByIdMutation,
    DeleteSurveyByIdMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    DeleteSurveyByIdMutation,
    DeleteSurveyByIdMutationVariables
  >(DeleteSurveyByIdDocument, options);
}
export type DeleteSurveyByIdMutationHookResult = ReturnType<
  typeof useDeleteSurveyByIdMutation
>;
export type DeleteSurveyByIdMutationResult =
  Apollo.MutationResult<DeleteSurveyByIdMutation>;
export type DeleteSurveyByIdMutationOptions = Apollo.BaseMutationOptions<
  DeleteSurveyByIdMutation,
  DeleteSurveyByIdMutationVariables
>;
export const DownloadSurveyPdfZipByDateRangeDocument = gql`
  query downloadSurveyPDFZipByDateRange($input: SurveyDateRangeReq!) {
    downloadSurveyPDFZipByDateRange(input: $input) {
      pdfURL
    }
  }
`;

/**
 * __useDownloadSurveyPdfZipByDateRangeQuery__
 *
 * To run a query within a React component, call `useDownloadSurveyPdfZipByDateRangeQuery` and pass it any options that fit your needs.
 * When your component renders, `useDownloadSurveyPdfZipByDateRangeQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDownloadSurveyPdfZipByDateRangeQuery({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useDownloadSurveyPdfZipByDateRangeQuery(
  baseOptions: Apollo.QueryHookOptions<
    DownloadSurveyPdfZipByDateRangeQuery,
    DownloadSurveyPdfZipByDateRangeQueryVariables
  > &
    (
      | {
          variables: DownloadSurveyPdfZipByDateRangeQueryVariables;
          skip?: boolean;
        }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    DownloadSurveyPdfZipByDateRangeQuery,
    DownloadSurveyPdfZipByDateRangeQueryVariables
  >(DownloadSurveyPdfZipByDateRangeDocument, options);
}
export function useDownloadSurveyPdfZipByDateRangeLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    DownloadSurveyPdfZipByDateRangeQuery,
    DownloadSurveyPdfZipByDateRangeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    DownloadSurveyPdfZipByDateRangeQuery,
    DownloadSurveyPdfZipByDateRangeQueryVariables
  >(DownloadSurveyPdfZipByDateRangeDocument, options);
}
export function useDownloadSurveyPdfZipByDateRangeSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    DownloadSurveyPdfZipByDateRangeQuery,
    DownloadSurveyPdfZipByDateRangeQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    DownloadSurveyPdfZipByDateRangeQuery,
    DownloadSurveyPdfZipByDateRangeQueryVariables
  >(DownloadSurveyPdfZipByDateRangeDocument, options);
}
export type DownloadSurveyPdfZipByDateRangeQueryHookResult = ReturnType<
  typeof useDownloadSurveyPdfZipByDateRangeQuery
>;
export type DownloadSurveyPdfZipByDateRangeLazyQueryHookResult = ReturnType<
  typeof useDownloadSurveyPdfZipByDateRangeLazyQuery
>;
export type DownloadSurveyPdfZipByDateRangeSuspenseQueryHookResult = ReturnType<
  typeof useDownloadSurveyPdfZipByDateRangeSuspenseQuery
>;
export type DownloadSurveyPdfZipByDateRangeQueryResult = Apollo.QueryResult<
  DownloadSurveyPdfZipByDateRangeQuery,
  DownloadSurveyPdfZipByDateRangeQueryVariables
>;
export const GetSurveyTemplatesDocument = gql`
  query getSurveyTemplates {
    getSurveyTemplates {
      surveyTemplateId
      fQuesJson
      name
    }
  }
`;

/**
 * __useGetSurveyTemplatesQuery__
 *
 * To run a query within a React component, call `useGetSurveyTemplatesQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveyTemplatesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveyTemplatesQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetSurveyTemplatesQuery(
  baseOptions?: Apollo.QueryHookOptions<
    GetSurveyTemplatesQuery,
    GetSurveyTemplatesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<
    GetSurveyTemplatesQuery,
    GetSurveyTemplatesQueryVariables
  >(GetSurveyTemplatesDocument, options);
}
export function useGetSurveyTemplatesLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveyTemplatesQuery,
    GetSurveyTemplatesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSurveyTemplatesQuery,
    GetSurveyTemplatesQueryVariables
  >(GetSurveyTemplatesDocument, options);
}
export function useGetSurveyTemplatesSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveyTemplatesQuery,
    GetSurveyTemplatesQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSurveyTemplatesQuery,
    GetSurveyTemplatesQueryVariables
  >(GetSurveyTemplatesDocument, options);
}
export type GetSurveyTemplatesQueryHookResult = ReturnType<
  typeof useGetSurveyTemplatesQuery
>;
export type GetSurveyTemplatesLazyQueryHookResult = ReturnType<
  typeof useGetSurveyTemplatesLazyQuery
>;
export type GetSurveyTemplatesSuspenseQueryHookResult = ReturnType<
  typeof useGetSurveyTemplatesSuspenseQuery
>;
export type GetSurveyTemplatesQueryResult = Apollo.QueryResult<
  GetSurveyTemplatesQuery,
  GetSurveyTemplatesQueryVariables
>;
export const GetSurveyAnswersDocument = gql`
  query getSurveyAnswers($patientId: Int!) {
    getSurveyAnswers(patientId: $patientId) {
      surveyAnswerId
      treatmentTitle
      treatmentType
      basicAnswerJson
      customAnswerJson
      createdAt
      examStartDate
      reserveDetailId
    }
  }
`;

/**
 * __useGetSurveyAnswersQuery__
 *
 * To run a query within a React component, call `useGetSurveyAnswersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetSurveyAnswersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetSurveyAnswersQuery({
 *   variables: {
 *      patientId: // value for 'patientId'
 *   },
 * });
 */
export function useGetSurveyAnswersQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetSurveyAnswersQuery,
    GetSurveyAnswersQueryVariables
  > &
    (
      | { variables: GetSurveyAnswersQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetSurveyAnswersQuery, GetSurveyAnswersQueryVariables>(
    GetSurveyAnswersDocument,
    options,
  );
}
export function useGetSurveyAnswersLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetSurveyAnswersQuery,
    GetSurveyAnswersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetSurveyAnswersQuery,
    GetSurveyAnswersQueryVariables
  >(GetSurveyAnswersDocument, options);
}
export function useGetSurveyAnswersSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetSurveyAnswersQuery,
    GetSurveyAnswersQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetSurveyAnswersQuery,
    GetSurveyAnswersQueryVariables
  >(GetSurveyAnswersDocument, options);
}
export type GetSurveyAnswersQueryHookResult = ReturnType<
  typeof useGetSurveyAnswersQuery
>;
export type GetSurveyAnswersLazyQueryHookResult = ReturnType<
  typeof useGetSurveyAnswersLazyQuery
>;
export type GetSurveyAnswersSuspenseQueryHookResult = ReturnType<
  typeof useGetSurveyAnswersSuspenseQuery
>;
export type GetSurveyAnswersQueryResult = Apollo.QueryResult<
  GetSurveyAnswersQuery,
  GetSurveyAnswersQueryVariables
>;
export const PostApiConsultationResultUpdateDocument = gql`
  mutation postApiConsultationResultUpdate(
    $emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput: EmrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput
  ) {
    postApiConsultationResultUpdate(
      emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput: $emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput
    ) {
      message
      status
      data {
        status
      }
    }
  }
`;
export type PostApiConsultationResultUpdateMutationFn = Apollo.MutationFunction<
  PostApiConsultationResultUpdateMutation,
  PostApiConsultationResultUpdateMutationVariables
>;

/**
 * __usePostApiConsultationResultUpdateMutation__
 *
 * To run a mutation, you first call `usePostApiConsultationResultUpdateMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `usePostApiConsultationResultUpdateMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [postApiConsultationResultUpdateMutation, { data, loading, error }] = usePostApiConsultationResultUpdateMutation({
 *   variables: {
 *      emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput: // value for 'emrCloudApiRequestsConsultationResultConsultationResultUpdateRequestInput'
 *   },
 * });
 */
export function usePostApiConsultationResultUpdateMutation(
  baseOptions?: Apollo.MutationHookOptions<
    PostApiConsultationResultUpdateMutation,
    PostApiConsultationResultUpdateMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    PostApiConsultationResultUpdateMutation,
    PostApiConsultationResultUpdateMutationVariables
  >(PostApiConsultationResultUpdateDocument, options);
}
export type PostApiConsultationResultUpdateMutationHookResult = ReturnType<
  typeof usePostApiConsultationResultUpdateMutation
>;
export type PostApiConsultationResultUpdateMutationResult =
  Apollo.MutationResult<PostApiConsultationResultUpdateMutation>;
export type PostApiConsultationResultUpdateMutationOptions =
  Apollo.BaseMutationOptions<
    PostApiConsultationResultUpdateMutation,
    PostApiConsultationResultUpdateMutationVariables
  >;
