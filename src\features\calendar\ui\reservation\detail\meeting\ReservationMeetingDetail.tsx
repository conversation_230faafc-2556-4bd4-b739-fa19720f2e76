import { useMemo } from "react";

import Link from "next/link";
import styled from "styled-components";

import { SvgIconTextCopy } from "@/components/ui/Icon/IconTextCopy";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";

const BasicInfoItem = styled.div`
  display: flex;
  align-items: center;
  border-top: 1px solid #e2e3e5;
  padding: 8px 0;
  line-height: 1;
  > div {
    display: flex;
    align-items: center;
  }
`;

const Label = styled.div`
  display: flex;
  margin-right: 4px;
  color: #6a757d;
`;

const ContentWrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
`;

type Props = {
  meetingId: number;
  reserveDetailId: number;
};

export const ReservationMeetingDetail: React.FC<Props> = ({
  meetingId,
  reserveDetailId,
}) => {
  const { notification } = useGlobalNotification();

  const meetingUrl = useMemo(
    () =>
      typeof window !== "undefined"
        ? `${window.location.origin}/meet/${meetingId}?detail=${reserveDetailId}`
        : "",
    [meetingId],
  );

  return (
    <BasicInfoItem>
      <ContentWrapper>
        <Label>オンライン診療URL:</Label>
        <Link href={meetingUrl} target="_blank">
          {meetingUrl}
        </Link>
        <SvgIconTextCopy
          cursor="pointer"
          onClick={async () => {
            try {
              await navigator.clipboard.writeText(meetingUrl);
              notification.success({ message: "コピーしました" });
            } catch {
              notification.error({ message: "コピーに失敗しました" });
            }
          }}
        />
      </ContentWrapper>
    </BasicInfoItem>
  );
};
