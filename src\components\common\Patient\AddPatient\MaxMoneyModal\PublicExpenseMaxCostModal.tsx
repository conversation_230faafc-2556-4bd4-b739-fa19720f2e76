import { useCallback, useEffect, useMemo, useRef, useState } from "react";

import { useClickOutside } from "amazon-chime-sdk-component-library-react";
import { DatePicker, Flex } from "antd";
import dayjs from "dayjs";
import { concat, debounce, sortBy, sumBy } from "lodash";
import { useFormContext } from "react-hook-form";
import styled from "styled-components";
import { usePathname, useSearchParams } from "next/navigation";

import {
  useGetApiPatientInforGetMaxMoneyByPtIdQuery,
  useGetApiReceptionGetMaxMoneyDataQuery,
} from "@/apis/gql/operations/__generated__/reception";
import { SvgIconDelete } from "@/components/ui/Icon/IconDelete";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { Table } from "@/components/ui/Table";
import { useError<PERSON>andler } from "@/hooks/useErrorHandler";
import { RenderIf } from "@/utils/common/render-if";
import { TextInput } from "@/components/ui/TextInput";
import {
  defaultRaiinNoFirst,
  defaultTimeFirst,
  kohiJuni,
  PID,
} from "@/constants/insurance";
import { ErrorText } from "@/components/ui/ErrorText";
import { InputLabel } from "@/components/ui/InputLabel";
import { useAuditLog } from "@/hooks/useAuditLog";
import { AuditEventCode } from "@/constants/audit-log";
import { ModalLoading } from "@/components/ui/ModalLoading";

import { usePatientContext } from "../Providers/PatientProvider";

import { ConfirmSaveLimitListModal } from "./ConfirmSaveLimitList";

import type { DataTableMaxMoneyItem } from "@/types/patient";
import type {
  PublicExpenseSelectData,
  RegistrationPublicExpenseInforForm,
} from "@/types/insurance";
import type {
  DomainModelsInsuranceMstHokenMstModel,
  DomainModelsMaxMoneyLimitListModel,
} from "@/apis/gql/generated/types";
import type { Dayjs } from "dayjs";
import type { TableColumnsType } from "antd";

const BLANK_MAX_MONEY: Omit<DomainModelsMaxMoneyLimitListModel, "__typename"> =
  {
    biko: "",
    futanGaku: 0,
    raiinNo: "0",
    sinDateD: 0,
    totalMoney: 0,
    sinDateM: 0,
    sinDateY: 0,
    isDeleted: 0,
    totalGaku: 0,
    kohiId: 0,
    id: "0",
    seqNo: 0,
    hokenPid: 0,
    code: "",
  };

const showGendoGaku = (
  selectedPublicExpense: DomainModelsInsuranceMstHokenMstModel,
  sinDate?: number,
) => {
  if (sinDate) return;
  if (
    selectedPublicExpense.kaiLimitFutan &&
    selectedPublicExpense.kaiLimitFutan > 0
  ) {
    return selectedPublicExpense.kaiLimitFutan;
  }
  if (
    selectedPublicExpense.dayLimitFutan &&
    selectedPublicExpense.dayLimitFutan > 0
  ) {
    return selectedPublicExpense.dayLimitFutan;
  }
  return selectedPublicExpense?.monthLimitFutan;
};

const formatDate = (
  sinDateD: number,
  sinDateM: number,
  sinDateY: number,
): string => {
  return `${sinDateY}${String(sinDateM).padStart(2, "0")}${String(sinDateD).padStart(2, "0")}`;
};

type Props = {
  saveLimitList?: (
    data: DataTableMaxMoneyItem[],
    selectedMonth: dayjs.Dayjs,
  ) => Promise<void>;
  selectedPublicExpense: Omit<PublicExpenseSelectData, "uniqKey">;
  ptId?: string;
};

export const PublicExpenseMaxCostModal: React.FC<Props> = ({
  saveLimitList,
  selectedPublicExpense,
  ptId,
}) => {
  const { handleError } = useErrorHandler();
  const { modal, handleCloseModal, selectedPatient, selectedInsurance } =
    usePatientContext();
  const [dataTable, setDataTable] = useState<DataTableMaxMoneyItem[]>([]);
  const [_, setSelectedRow] = useState(null);
  const [selectedMonth, setSelectedMonth] = useState<Dayjs>(dayjs());
  const { setValue, getValues, watch } =
    useFormContext<RegistrationPublicExpenseInforForm>();
  const [tempSelectedDate, setTempSelectedDate] = useState<Dayjs>();
  const [errorTable, setErrorTable] = useState<string>("");

  const { rate, gendogaku } = watch();
  const tableRef = useRef<HTMLDivElement>(null);

  const [countDataBySinDate, setCountDataBySinDate] = useState(
    new Map<number, number>(),
  );

  const searchParams = useSearchParams();
  const sinDate = Number(searchParams.get("sinDate") ?? 0);

  const { data: receptionMaxMoney, loading: loadingReceptionMaxMoney } =
    useGetApiReceptionGetMaxMoneyDataQuery({
      skip: !sinDate,
      variables: {
        hokenKohiId: selectedInsurance?.hokenId,
        ptId: selectedPatient?.patientID.toString() ?? "0",
        sinDate: sinDate,
      },
      onError: (error) => {
        handleError({ error });
      },
    });
  const maxMoneyData =
    receptionMaxMoney?.getApiReceptionGetMaxMoneyData?.data?.data;

  const { loading: loadingPatientMaxMoney } =
    useGetApiPatientInforGetMaxMoneyByPtIdQuery({
      skip: !selectedInsurance,
      variables: {
        hokenId: selectedInsurance?.hokenId,
        ptId: selectedPatient?.patientID.toString() ?? "0",
      },
      onError: (error) => {
        handleError({ error });
      },

      onCompleted: (response) => {
        const data = response.getApiPatientInforGetMaxMoneyByPtId?.data?.data;
        if (!data) return;
        let defaultValue = [...(getValues("limitList") ?? [])];
        const dataWithKey = data.map((item) => {
          const foundItem = defaultValue.find(
            (latestItem) => latestItem.key === item.id,
          );
          if (foundItem) {
            defaultValue = defaultValue.filter(
              (latestItem) => latestItem.key !== item.id,
            );
            return {
              ...foundItem,
              key: item.id!,
              isModify: false,
              hasError: false,
            };
          }
          return {
            ...item,
            isModify: false,
            key: item.id!,
            hasError: false,
          };
        });
        setCountDataBySinDate((prevMap) => {
          const newMap = new Map(prevMap);
          dataWithKey.forEach((item) => {
            const itemSinDate = item.sinDate || 0;
            const currentCount = newMap.get(itemSinDate) ?? 0;
            newMap.set(itemSinDate, currentCount + 1);
          });
          return newMap;
        });

        setDataTable(
          handleCalculateTotalMoney(
            sortBy(dataWithKey.concat(defaultValue), "sinDateD"),
          ),
        );
      },
    });

  const pathname = usePathname();
  const { handleAuditLogMutation } = useAuditLog();

  const getDataThisMonthYear = useCallback(
    (dataTable: DataTableMaxMoneyItem[], isTypeInvalid?: boolean) => {
      return dataTable.filter((item) => {
        const conditionThisMonthYear =
          String(item.sinDateY) === dayjs(selectedMonth).format("YYYY") &&
          dayjs(String(item.sinDateM)).format("MM") ===
            dayjs(selectedMonth).format("MM");
        if (isTypeInvalid) {
          return conditionThisMonthYear && item.isModify && item.sinDateD;
        }

        return conditionThisMonthYear;
      });
    },
    [selectedMonth],
  );

  const handleCalculateTotalMoney = useCallback(
    (array: DataTableMaxMoneyItem[]) => {
      return array.map((prevItem) => {
        const dataInThisMonth = getDataThisMonthYear([...array]);
        const index = dataInThisMonth.findIndex(
          (item) => item.key === prevItem.key,
        );

        let totalMoney = prevItem.totalMoney ?? 0;

        if (index !== -1) {
          totalMoney = dataInThisMonth
            .slice(0, index + 1)
            .reduce((acc, curr) => {
              return acc + Number(curr.futanGaku);
            }, 0);
        }

        return {
          ...prevItem,
          totalMoney,
        };
      });
    },
    [getDataThisMonthYear],
  );

  useEffect(() => {
    // case add kohi (no selectedInsurance)
    if (modal.publicExpenseCostOpen && !selectedInsurance) {
      const defaultValue = [...(getValues("limitList") ?? [])];
      const dataWithKey = defaultValue.map((item) => {
        return {
          ...item,
          isModify: true,
          key: item.id!,
          hasError: false,
        };
      });
      setCountDataBySinDate((prevMap) => {
        const newMap = new Map(prevMap);
        dataWithKey.forEach((item) => {
          const itemSinDate = item.sinDate || 0;
          const currentCount = newMap.get(itemSinDate) ?? 0;
          newMap.set(itemSinDate, currentCount + 1);
        });
        return newMap;
      });
      setDataTable(handleCalculateTotalMoney(sortBy(dataWithKey, "sinDateD")));
    }
  }, [
    getValues,
    handleCalculateTotalMoney,
    modal.publicExpenseCostOpen,
    selectedInsurance,
  ]);

  const handleChangeFutanGaku = useCallback(
    (value: number | null, key: string) => {
      setDataTable((prev) => {
        const clonedPrev = [...prev];

        const indexCurrentItem = clonedPrev.findIndex(
          (item) => item.key === key,
        );
        if (indexCurrentItem !== -1 && clonedPrev[indexCurrentItem]) {
          clonedPrev[indexCurrentItem] = {
            ...clonedPrev[indexCurrentItem],
            futanGaku: Number(value),
            isModify: true,
          };
        }

        return handleCalculateTotalMoney(clonedPrev);
      });
    },
    [handleCalculateTotalMoney],
  );

  const columns: TableColumnsType<
    DomainModelsMaxMoneyLimitListModel & {
      key: string;
      isModify: boolean;
      hasError: boolean;
    }
  > = [
    {
      title: () => (
        <Flex justify="center">
          <InputLabel required label="診療日" />
        </Flex>
      ),
      dataIndex: "sinDateD",
      render: (_, { sinDateD, raiinNo, key, sinDateM, sinDateY, hasError }) => {
        if (Number(raiinNo) === 0) {
          const lastDayOfMonth = dayjs(`${sinDateY}-${sinDateM}-01`)
            .endOf("month")
            .format("DD");
          return (
            <StyledInput
              value={sinDateD || "0"}
              width={104}
              hasError={hasError}
              onChange={(e) => {
                const value = e.target.value;
                setErrorTable(value ? "" : "診療日を入力してください。");
                setDataTable((prev) =>
                  prev.map((item) => {
                    if (item.key === key) {
                      return {
                        ...item,
                        sinDateD: Number(value) || 0,
                        isModify: true,
                        hasError: !value,
                      };
                    }
                    return item;
                  }),
                );
              }}
              onInput={(e) => {
                const input = e.target as HTMLInputElement;
                input.value = input.value
                  .replace(/[^\d０-９]/g, "")
                  .replace(/[０-９]/g, (s) =>
                    String.fromCharCode(s.charCodeAt(0) - 0xfee0),
                  )
                  .replace(/^0+/, "");

                if (input.value === "") return;

                let numValue = Number(input.value) || 0;
                if (numValue > Number(lastDayOfMonth))
                  numValue = Number(lastDayOfMonth);

                input.value = String(numValue);
              }}
            />
          );
        }
        return <TableDataWrapper margin={17}>{sinDateD}</TableDataWrapper>;
      },
      align: "center",
      width: 120,
    },
    {
      title: "医療費総額",
      dataIndex: "totalGaku",
      render: (_, { raiinNo, totalGaku, key }) => {
        if (Number(raiinNo) === 0) {
          return (
            <StyledInput
              value={totalGaku || "0"}
              width={84}
              maxLength={4}
              onChange={(e) => {
                const value = e.target.value;
                setDataTable((prev) =>
                  prev.map((item) => {
                    if (item.key === key) {
                      return {
                        ...item,
                        totalGaku: Number(value) || 0,
                        isModify: true,
                      };
                    }
                    return item;
                  }),
                );
              }}
              onInput={(e) => {
                const input = e.target as HTMLInputElement;
                input.value = input.value
                  .replace(/[^\d０-９]/g, "")
                  .replace(/[０-９]/g, (s) =>
                    String.fromCharCode(s.charCodeAt(0) - 0xfee0),
                  )
                  .replace(/^0+(?=\d)/, "");
              }}
            />
          );
        }
        return <TableDataWrapper margin={17}>{totalGaku}</TableDataWrapper>;
      },
      align: "center",
      width: 100,
      hidden: selectedPublicExpense.isLimitListSum !== 1,
    },
    {
      title: "自己負担額",
      dataIndex: "futanGaku",
      render: (_, { futanGaku, raiinNo, key }) => {
        if (Number(raiinNo) === 0) {
          return (
            <StyledInput
              value={futanGaku || "0"}
              width={84}
              maxLength={4}
              onChange={(e) => {
                const value = e.target.value;
                handleChangeFutanGaku(Number(value), key);
              }}
              onInput={(e) => {
                const input = e.target as HTMLInputElement;
                input.value = input.value
                  .replace(/[^\d０-９]/g, "")
                  .replace(/[０-９]/g, (s) =>
                    String.fromCharCode(s.charCodeAt(0) - 0xfee0),
                  )
                  .replace(/^0+(?=\d)/, "");
              }}
            />
          );
        }
        return <TableDataWrapper margin={17}>{futanGaku}</TableDataWrapper>;
      },
      align: "center",
      width: 100,
    },
    {
      title: "自己負担累計",
      dataIndex: "totalMoney",
      align: "center",
      render: (totalMoney) => {
        return <TotalMoneyStyled>{totalMoney}</TotalMoneyStyled>;
      },
      width: 100,
      className: "total-money",
    },
    {
      title: "医療機関",
      dataIndex: "raiinNo",
      align: "center",
      render: (raiinNo) => {
        return <span>{Number(raiinNo) === 0 ? "他院分" : ""}</span>;
      },
      width: 96,
    },
    {
      title: "メモ",
      dataIndex: "biko",
      render: (_, { raiinNo, biko, key }) => {
        if (Number(raiinNo) === 0) {
          return (
            <StyledInput
              value={biko || ""}
              width={204}
              maxLength={200}
              onChange={(e) => {
                setDataTable((prev) =>
                  prev.map((item) => {
                    if (item.key === key) {
                      return {
                        ...item,
                        biko: e.target.value || "",
                        isModify: true,
                      };
                    }
                    return item;
                  }),
                );
              }}
            />
          );
        }
        return <span>{biko}</span>;
      },
      align: "center",
      width: 220,
    },
    {
      title: "",
      dataIndex: "raiinNo",
      render: (_, { raiinNo, key }) => {
        if (Number(raiinNo) === 0) {
          return (
            <IconButton
              varient="ordinary"
              onClick={() => {
                setDataTable((prev) => {
                  const itemToDelete = prev.find((item) => item.key === key);
                  if (itemToDelete) {
                    const itemSinDate = itemToDelete.sinDate || 0;
                    setCountDataBySinDate((prevMap) => {
                      const newMap = new Map(prevMap);
                      const currentCount = newMap.get(itemSinDate) ?? 0;
                      if (currentCount > 0) {
                        newMap.set(itemSinDate, currentCount - 1);
                      }
                      return newMap;
                    });
                  }
                  return prev.filter((item) => item.key !== key);
                });
              }}
            >
              <SvgIconDelete />
            </IconButton>
          );
        }
        return <div></div>;
      },
      width: 40,
      align: "center",
    },
  ];

  useClickOutside(
    tableRef,
    debounce(() => {
      setDataTable((prev) =>
        handleCalculateTotalMoney(sortBy(prev, "sinDateD")),
      );
    }, 300),
  );
  const handleSaveLimitList = async () => {
    const dataSave = dataTable.map((item, index) => {
      const sinDate = Number(
        formatDate(item.sinDateD ?? 0, item.sinDateM ?? 0, item.sinDateY ?? 0),
      );
      return {
        ...item,
        sinDate,
        biko: item.biko ? item.biko.trim() : "",
        sortKey:
          sinDate + defaultTimeFirst + defaultRaiinNoFirst + kohiJuni + PID,
        sort: index,
      };
    });

    if (saveLimitList) {
      await saveLimitList(dataSave, selectedMonth);
      return;
    }

    const currentMap = new Map(countDataBySinDate);

    const dataLimitList = dataSave.map((item) => {
      if (!item.isModify) return item;

      const itemSinDate = item.sinDate || 0;

      const currentCount = currentMap.get(itemSinDate) ?? 0;
      const suffix = currentCount.toString().padStart(2, "0");

      currentMap.set(itemSinDate, currentCount + 1);

      return {
        ...item,
        sortKey: `${item.sortKey || ""}${suffix}`,
      };
    });
    setCountDataBySinDate(currentMap);
    setValue(
      "limitList",
      dataLimitList.filter((item) => item.isModify),
    );
  };

  useEffect(() => {
    if (selectedMonth) {
      setDataTable((prev) => prev.filter((item) => item.sinDateD !== 0));
    }
  }, [selectedMonth]);

  useEffect(() => {
    const dataThisMonthYear = getDataThisMonthYear(dataTable);
    if (!dataThisMonthYear.length) {
      const sinDateY = Number(dayjs(selectedMonth).format("YYYY"));
      const sinDateM = Number(dayjs(selectedMonth).format("MM"));
      const sinDate = Number(dayjs(selectedMonth).format("YYYYMMDD"));
      setDataTable((prev) => [
        {
          ...BLANK_MAX_MONEY,
          key: String(Math.random() * Date.now()),
          isModify: true,
          sinDateY,
          sinDateM,
          sinDate,
          hasError: false,
        },
        ...prev,
      ]);
    }
  }, [dataTable, dataTable.length, getDataThisMonthYear, selectedMonth]);

  useEffect(() => {
    if (pathname.match(/^\/karte\/([^\\/]+)\/$/)) {
      handleAuditLogMutation({
        ptId: ptId,
        eventCd: AuditEventCode.PublicExpenseInfoMaxMoneyDisplay,
      });
    }
  }, [handleAuditLogMutation, pathname, ptId]);

  const maxMoneyRate = useMemo(() => {
    if (sinDate) {
      return maxMoneyData?.rate;
    }
    return rate || selectedPublicExpense.futanRate || 0;
  }, [sinDate, maxMoneyData, rate, selectedPublicExpense.futanRate]);

  const gendogakuValue = useMemo(() => {
    if (sinDate) {
      return maxMoneyData?.gendoGaku || 0;
    }
    return gendogaku || showGendoGaku(selectedPublicExpense) || 0;
  }, [sinDate, maxMoneyData, gendogaku, selectedPublicExpense]);

  const residualAmountToBePaid = useMemo(() => {
    return gendogakuValue - sumBy(getDataThisMonthYear(dataTable), "futanGaku");
  }, [dataTable, gendogakuValue]);

  return (
    <StyledModal
      width={selectedPublicExpense.isLimitListSum !== 1 ? 680 : 800}
      title="上限額管理"
      isOpen={modal.publicExpenseCostOpen}
      onCancel={() => handleCloseModal("PUBLIC_EXPENSE_MAX_COST")}
      footer={[
        <Button
          key="cancel"
          shape="round"
          varient="tertiary"
          onClick={() => handleCloseModal("PUBLIC_EXPENSE_MAX_COST")}
        >
          キャンセル
        </Button>,
        <Button
          key="save"
          shape="round"
          varient="primary"
          onClick={async () => {
            let haveInvalidSindate = false;

            const updatedDataTable = dataTable.map((item) => {
              const isInvalid =
                Number(item.sinDateD) === 0 &&
                (!!item.biko || !!item.futanGaku || !!item.totalGaku);

              if (isInvalid) {
                haveInvalidSindate = true;
              }

              return {
                ...item,
                hasError: isInvalid,
              };
            });

            if (haveInvalidSindate) {
              setErrorTable("診療日を入力してください。");
              setDataTable(updatedDataTable);
              return;
            }

            await handleSaveLimitList();
            handleCloseModal("PUBLIC_EXPENSE_MAX_COST");
          }}
        >
          確定
        </Button>,
      ]}
    >
      {(loadingPatientMaxMoney || loadingReceptionMaxMoney) && <ModalLoading />}
      <RenderIf condition={!!tempSelectedDate}>
        <ConfirmSaveLimitListModal
          onConfirm={() => {
            if (!tempSelectedDate) return;
            setSelectedMonth(tempSelectedDate);
            handleSaveLimitList();
            setTempSelectedDate(undefined);
          }}
          onClose={() => setTempSelectedDate(undefined)}
        />
      </RenderIf>

      <ScrollableArea>
        <HeadingWrapper>
          <Heading>
            {selectedPublicExpense.houbetu} {selectedPublicExpense.hokenName}
          </Heading>
          <SubHeadingWrapper>
            <Flex gap={8} align="flex-end">
              <SubHeadingLabel>負担率</SubHeadingLabel>
              <SubHeadingValue>{maxMoneyRate}％</SubHeadingValue>
            </Flex>
            <Flex gap={8} align="flex-end">
              <SubHeadingLabel>負担上限</SubHeadingLabel>
              <SubHeadingValue>{gendogakuValue}円</SubHeadingValue>
            </Flex>
            <Flex gap={8} align="flex-end">
              <SubHeadingLabel>負担残額</SubHeadingLabel>
              <SubHeadingValue>{residualAmountToBePaid}円</SubHeadingValue>
            </Flex>
          </SubHeadingWrapper>
        </HeadingWrapper>
        <ActionsWrapper>
          <div>
            <StyledDatePicker
              picker="month"
              format="YYYY/MM"
              width={120}
              value={selectedMonth}
              allowClear={false}
              inputReadOnly
              onChange={(value) => {
                const limitListThisMonthYear = getDataThisMonthYear(
                  dataTable,
                  true,
                );

                const invalidData = limitListThisMonthYear.find(
                  (item) => item.sinDateD !== 0,
                );

                const isExistInFormData = getValues("limitList")?.some(
                  (item) => item.key === invalidData?.key,
                );

                if (limitListThisMonthYear.length > 0 && !isExistInFormData) {
                  setTempSelectedDate(value as Dayjs);
                } else {
                  setSelectedMonth(value as Dayjs);
                }
              }}
            />
            <StyledSpan>月度</StyledSpan>
          </div>
          <StyledButton
            varient="ordinary"
            disabled={dataTable.some(
              (item) =>
                item.sinDateM === Number(dayjs(selectedMonth).format("MM")) &&
                item.sinDateY === Number(dayjs(selectedMonth).format("YYYY")) &&
                item.isModify &&
                !item.sinDateD,
            )}
            onClick={() => {
              const sinDateY = Number(dayjs(selectedMonth).format("YYYY"));
              const sinDateM = Number(dayjs(selectedMonth).format("MM"));
              const sinDate = Number(dayjs(selectedMonth).format("YYYYMMDD"));

              setDataTable((prev) => {
                return concat(
                  {
                    ...BLANK_MAX_MONEY,
                    key: String(Math.random() * Date.now()),
                    isModify: true,
                    sinDateY,
                    sinDateM,
                    sinDate,
                    hasError: false,
                  },
                  prev,
                );
              });
            }}
          >
            診療日追加
          </StyledButton>
        </ActionsWrapper>
        <div ref={tableRef}>
          <StyledTable
            columns={columns.filter((item) => !item.hidden)}
            scroll={{
              y: getDataThisMonthYear(dataTable).length > 5 ? 180 : undefined,
            }}
            onRow={(data) => {
              return {
                onClick: () => {
                  setSelectedRow((prev) => {
                    if (prev !== data.key) {
                      setDataTable((prev) =>
                        handleCalculateTotalMoney(sortBy(prev, "sinDateD")),
                      );
                    }
                    return data.key;
                  });
                },
              };
            }}
            dataSource={getDataThisMonthYear(dataTable)}
          />
          {errorTable !== "" && <StyledErrorText>{errorTable}</StyledErrorText>}
        </div>
      </ScrollableArea>
    </StyledModal>
  );
};

const TotalMoneyStyled = styled.p`
  text-align: end;
`;

const ScrollableArea = styled.div`
  height: 328px;
`;

const StyledModal = styled(Modal)``;

const StyledTable = styled(Table)`
  .ant-table-tbody > .ant-table-row {
    height: 36px !important;
  }
  .ant-table-tbody > .ant-table-row > .ant-table-cell {
    border-right: 1px solid #e2e3e5;
    padding: 0 !important;
    box-shadow: none;
  }
  .ant-table-tbody > .ant-table-row > .total-money {
    padding: 0 8px !important;
  }
  .ant-table-thead > tr > .ant-table-cell-scrollbar {
    box-shadow: none;
  }

  .ant-table-fixed-header .ant-table-scroll .ant-table-header {
    overflow: hidden !important;
    margin-bottom: 0px !important;
  }
  thead > tr > .ant-table-cell {
    border-right: 1px solid #e2e3e5;
    padding: 10px 0 !important;
  }
`;

const HeadingWrapper = styled.div`
  padding: 8px 24px;
  border-bottom: 1px solid #e2e3e5;
`;

const Heading = styled.p`
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;
  margin-bottom: 10px;
`;

const SubHeadingWrapper = styled.div`
  display: flex;
  gap: 44px;
`;

const SubHeadingLabel = styled.p`
  font-size: 12px;
  line-height: 12px;
  color: #6a757d;
`;

const SubHeadingValue = styled.p`
  font-size: 16px;
  line-height: 16px;
`;

const ActionsWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  padding: 12px 24px;
`;

const StyledDatePicker = styled(DatePicker)<{ width: number }>`
  width: ${(props) => props.width}px;
`;

const StyledSpan = styled.span`
  margin-left: 8px;
`;

const StyledInput = styled(TextInput)<{ width?: number }>`
  width: ${(props) => props.width}px;
  height: 28px;
`;

const IconButton = styled(Button)`
  width: auto;
  height: auto;
  padding: 0;
  background: #fff;
  border: none !important;
`;

const StyledButton = styled(Button)`
  width: 120px;
  height: 36px;
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 6px;
  font-size: 12px;
  margin-left: 10px;
`;

const TableDataWrapper = styled(Flex)<{ margin: number }>`
  margin-left: ${(props) => props.margin}px;
`;
