import { useState } from "react";

import dayjs from "dayjs";
import { useRouter } from "next/router";
import { useForm } from "react-hook-form";

import { useSurveyAnswerNoPatientTable } from "@/features/survey-answers/hooks/useSurveyAnswerNoPatientTable";
import { formatYYYYMMDD } from "@/utils/datetime-format";

import { useSurveyAnswerNoPatientDetail } from "./useSurveyAnswerNoPatientDetail";

import type { PatientSearchFormType } from "@/hooks/usePatientSearchForm";
import type { SurveyRes } from "@/apis/gql/generated/types";

export const useSurveyAnswerNoPatientTemplate = () => {
  const { query, push } = useRouter();
  const [isFetchingMore] = useState(false);
  const { control } = useForm<PatientSearchFormType>();
  const [selectedDate, setSelectedDate] = useState<string>(() =>
    formatYYYYMMDD(dayjs().toDate()),
  );
  const [selectedSurvey, setSelectedSurvey] = useState<SurveyRes | undefined>();
  const [searchKeyword, setSearchKeyword] = useState<string>("");
  const [selectedSurveyAnswerId, setSelectedSurveyAnswerId] =
    useState<string>("");
  const { refetchSurveyAnswerNoPatientById } = useSurveyAnswerNoPatientDetail(
    selectedSurveyAnswerId ?? "",
  );
  const { refetchSurveyAnswerNoPatients } = useSurveyAnswerNoPatientTable(
    selectedDate,
    selectedSurvey,
    searchKeyword,
    selectedSurveyAnswerId,
    setSelectedSurveyAnswerId,
    refetchSurveyAnswerNoPatientById,
  );

  const handleSettingSurveyRoute = () => push("/setting/survey/");

  return {
    query,
    handleSettingSurveyRoute,
    isFetchingMore,
    control,
    selectedDate,
    setSelectedDate,
    selectedSurvey,
    setSelectedSurvey,
    selectedSurveyAnswerId,
    searchKeyword,
    setSearchKeyword,
    refetchSurveyAnswerNoPatients,
    setSelectedSurveyAnswerId,
    refetchSurveyAnswerNoPatientById,
  };
};
