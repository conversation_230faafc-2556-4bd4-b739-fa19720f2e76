import {
  useEffect,
  useMemo,
  useState,
  type Dispatch,
  type SetStateAction,
} from "react";

import styled from "styled-components";
import { Spin, Typography } from "antd";
import dayjs from "dayjs";
import { v4 as uuidv4 } from "uuid";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { usePostApiEpsUpdatePrescriptionStatusByIdsMutation } from "@/apis/gql/operations/__generated__/karte-retry-cancel";
import { Connection, System } from "@/utils/socket-helper";
import { SvgIconError } from "@/components/ui/Icon/IconError";
import { usePostApiEpsSavePrescriptionInfoMutation } from "@/apis/gql/operations/__generated__/duplicate-medication";
import { useUpsertEpsRegister } from "@/hooks/useUpsertEpsRegister";
import { useGetOrderInfoContext } from "@/features/karte/hooks/useGetOrderInfoContext";

import { useEPrescriptionContext } from "../PrintSetting/EPrescriptionContextProvider";

import { MODAL_TYPE } from "./TableInformationPrescription";

import type {
  DomainModelsEpsPrescriptionEpsPrescriptionModel,
  EmrCloudApiResponsesSystemConfSystemConfDto,
  DomainModelsEpsReqEpsReqModel,
} from "@/apis/gql/generated/types";

type ModalCancelPrescriptionProps = {
  numberOfRegistered: number;
  type: MODAL_TYPE | undefined;
  setModalType: Dispatch<SetStateAction<MODAL_TYPE | undefined>>;
  submit: () => void;
  disableCancel: boolean;
  prescriptionIdList:
    | DomainModelsEpsPrescriptionEpsPrescriptionModel[]
    | undefined;
  systemConf: EmrCloudApiResponsesSystemConfSystemConfDto[] | undefined;
};

export type PrescriptionIdListXmlProps = {
  PrescriptionId: string;
  AccessCode: string;
  CreateDateTime: string;
};

const StyledModal = styled(Modal)<{
  $errorModal?: boolean;
}>`
  .ant-modal-header {
    background-color: ${({ $errorModal }) =>
      $errorModal ? "#e74c3c" : "#005BAC"};
  }
  .ant-modal-body {
    min-height: 256px;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .ant-modal-footer {
    height: 84px;
    align-items: center;
  }
`;

const ContentErrorWapper = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 24px 50px;
`;

const ModalContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30px;
  min-height: 256px;
  width: 100%;
`;

const ModalContentConfirm = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 24px;
`;

const { Title, Text } = Typography;

export const ModalCancelPrescription = ({
  numberOfRegistered,
  type,
  setModalType,
  submit,
  disableCancel,
  prescriptionIdList,
  systemConf,
}: ModalCancelPrescriptionProps) => {
  const [contentError, setContentError] = useState<{
    title: string;
    content?: string;
    content2?: string;
  }>({
    title: "",
    content: "",
    content2: "",
  });

  const { handleError } = useErrorHandler();
  const { updateEpsRegister } = useEPrescriptionContext();
  const [registerResult, setRegisterResult] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const [registerResultDIM06req, setRegisterResultDIM06req] =
    useState<DomainModelsEpsReqEpsReqModel | null>(null);
  const { raiinNo, ptId, sinDate } = useGetOrderInfoContext();

  const [updateStatusPrescription] =
    usePostApiEpsUpdatePrescriptionStatusByIdsMutation({
      onError: (error) => {
        setModalType(undefined);
        handleError({ error });
      },
    });
  const { handleUpsertEpsRegister } = useUpsertEpsRegister();

  const [updatePrescriptionInfo] = usePostApiEpsSavePrescriptionInfoMutation({
    onError: (error) => {
      setModalType(undefined);
      handleError({ error });
    },
  });

  const handleUpdateStatus = (id: string | undefined, status: number) => {
    updateStatusPrescription({
      variables: {
        emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput: {
          ptId: String(ptId),
          raiinNo: String(raiinNo),
          sinDate: Number(sinDate),
          prescriptionStatusItem: [
            {
              prescriptionId: id,
              status: status,
            },
          ],
        },
      },
      onError: (error) => {
        setModalType(undefined);
        handleError({ error }).then();
      },
    });
  };

  const system = new System("/medical", systemConf);
  const connect = new Connection("/medical");

  const handleCancelModal = () => {
    if (registerResult) {
      updateEpsRegister(registerResult, null);
    }
    if (registerResultDIM06req) {
      updateEpsRegister(registerResultDIM06req, null);
    }
    connect.disconnect();
    setModalType(undefined);
  };

  const sendReqCancelFile02 = async (prescriptionId: string) => {
    let status = false;

    const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;

    const registerResult = await handleUpsertEpsRegister({
      arbitraryFileIdentifier,
      reqDate: +dayjs().format("YYYYMMDD"),
      raiinNo: String(raiinNo),
      ptId: String(ptId),
      sinDate: Number(sinDate),
      prescriptionId: prescriptionId,
      dispensingResultId: "",
      reqType: 6,
      status: 1,
      resultCode: "",
      resultMessage: "",
      result: "",
    });

    if (registerResult) {
      setRegisterResult(registerResult);
    }

    await new Promise<void>((resolve) => {
      const messageBody = {
        PrescriptionId: prescriptionId,
      };
      system
        .createFile(
          {
            messageHeader: {},
            messageBody,
          },
          "EPSsiPIR02req",
        )
        .then((files) => {
          // const xmlContent = JSON.parse(files?.data?.content || "{}");
          let xmlContent;
          try {
            xmlContent = JSON.parse(files?.data?.content || "{}");
          } catch {
            const contentError = files?.data?.content;
            const codeError = `${contentError}`.match(/\[(.*?)\]/);
            setModalType(MODAL_TYPE.ERROR);
            setContentError({
              title: "処方箋情報の取り消しに失敗しました",
              content: `処理結果コード:${codeError && codeError[1] ? codeError[1] : ""}`,
              content2: contentError,
            });
          }
          const msgBody = xmlContent?.XmlMsg?.MessageBody;
          const segmentOfResult =
            xmlContent?.XmlMsg?.MessageHeader?.SegmentOfResult;
          const processingResultStatus =
            xmlContent?.XmlMsg?.MessageBody?.ProcessingResultStatus;

          const processingResultMessage =
            xmlContent?.XmlMsg?.MessageBody?.ProcessingResultMessage;

          const errorCode = xmlContent?.XmlMsg?.MessageHeader?.ErrorCode;
          const errorMsg = xmlContent?.XmlMsg?.MessageHeader?.ErrorMessage;
          const processingResultCode =
            xmlContent?.XmlMsg?.MessageBody?.ProcessingResultCode;
          if (registerResult) {
            updateEpsRegister(registerResult, null);
          }
          if (!msgBody && Number(segmentOfResult) === 9) {
            setModalType(MODAL_TYPE.ERROR);
            setContentError({
              title: "処方箋情報の取り消しに失敗しました",
              content: `処理結果コード: ${errorCode}\n${errorMsg}`,
            });
          }

          if (registerResult) {
            updateEpsRegister(registerResult, null);
          }

          if (msgBody && Number(processingResultStatus) === 2) {
            setModalType(MODAL_TYPE.ERROR);
            setContentError({
              title: "処方箋情報の取り消しに失敗しました",
              content: `処理結果コード: ${processingResultCode}\n${processingResultMessage}`,
            });
          }

          // if (files?.data?.fileName?.includes(".err")) {
          //   setModalType(MODAL_TYPE.ERROR);
          //   setContentError({
          //     title: "処方箋情報の取り消しに失敗しました",
          //     content: `${xmlContent}`,
          //   });
          // }

          if (
            (Number(segmentOfResult) === 1 &&
              Number(processingResultStatus) === 1) ||
            errorCode === "EPSB1030W" ||
            processingResultCode === "EPSB1030W"
          ) {
            handleUpdateStatus(prescriptionId, 3);
            status = true;
            resolve();
          }

          if (
            errorCode === "EPSB1032W" ||
            processingResultCode === "EPSB1032W"
          ) {
            connect.disconnect();

            const dataSystemConfFiltered = systemConf?.find(
              (item) => item.grpCd === 100040 && item.grpEdaNo === 8,
            );

            const messageBody = {
              PrescriptionId: prescriptionId,
              RefillSupported: dataSystemConfFiltered?.val,
            };

            const arbitraryFileIdentifierDIM06req = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;

            handleUpsertEpsRegister({
              arbitraryFileIdentifier: arbitraryFileIdentifierDIM06req,
              reqDate: +dayjs().format("YYYYMMDD"),
              raiinNo: String(raiinNo),
              ptId: String(ptId),
              sinDate: Number(sinDate),
              prescriptionId: prescriptionId,
              dispensingResultId: "",
              reqType: 8,
              status: 1,
              resultCode: "",
              resultMessage: "",
              result: "",
            }).then((registerResultDIM06req) => {
              if (registerResultDIM06req) {
                setRegisterResultDIM06req(registerResultDIM06req);
              }
            });

            system
              .createFile(
                {
                  messageHeader: {},
                  messageBody,
                },
                "EPSsiDIM06req",
              )
              .then((filesGetInfo) => {
                let xmlContentGetInfo;
                try {
                  xmlContentGetInfo = JSON.parse(
                    filesGetInfo?.data?.content || "{}",
                  );
                } catch {
                  handleUpdateStatus(prescriptionId, 1);
                  const contentError = filesGetInfo?.data?.content;
                  const codeError = `${contentError}`.match(/\[(.*?)\]/);
                  setModalType(MODAL_TYPE.ERROR);
                  setContentError({
                    title: "処方箋情報の取り消しに失敗しました",
                    content: `処理結果コード:${codeError && codeError[1] ? codeError[1] : ""}`,
                    content2: contentError,
                  });
                }
                // const xmlContentGetInfo = JSON.parse(
                //   filesGetInfo?.data?.content || "{}",
                // );
                const msgHeaderGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageHeader;
                const msgBodyGetInfo = xmlContentGetInfo?.XmlMsg?.MessageBody;
                const processingResultStatusGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageBody
                    ?.ProcessingResultStatus;
                const processingResultCodeGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageBody?.ProcessingResultCode;
                const processingResultMessageGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageBody
                    ?.ProcessingResultMessage;

                const segmentOfResultGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageHeader?.SegmentOfResult;
                const errorCodeGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageHeader?.ErrorCode;
                const errorMsgGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageHeader?.ErrorMessage;

                const labelContentError = msgHeaderGetInfo
                  ? "エラーコード"
                  : "処理結果コード";
                const contentError = msgHeaderGetInfo
                  ? {
                      title: "処方箋情報の取り消しに失敗しました",
                      content: `${labelContentError}:${errorCodeGetInfo}`,
                      content2: errorMsgGetInfo,
                    }
                  : {
                      title: "処方箋情報の取り消しに失敗しました",
                      content: `${labelContentError}:${processingResultCodeGetInfo}`,
                      content2: processingResultMessageGetInfo,
                    };

                if (registerResultDIM06req) {
                  updateEpsRegister(registerResultDIM06req, null);
                }

                if (!msgBodyGetInfo && Number(segmentOfResultGetInfo) === 9) {
                  handleUpdateStatus(prescriptionId, 1);
                  setModalType(MODAL_TYPE.ERROR);
                  setContentError(contentError);
                }

                if (
                  msgBodyGetInfo &&
                  Number(processingResultStatusGetInfo) === 2
                ) {
                  handleUpdateStatus(prescriptionId, 1);
                  setModalType(MODAL_TYPE.ERROR);
                  setContentError(contentError);
                }

                // if (filesGetInfo?.data?.fileName?.includes(".err")) {
                //   handleUpdateStatus(prescriptionId, 1);
                //   setModalType(MODAL_TYPE.ERROR);
                //   setContentError({
                //     title: "処方箋情報の取り消しに失敗しました",
                //     content: `${xmlContentGetInfo}`,
                //   });
                // }

                const prescriptionStatusGetInfo =
                  xmlContentGetInfo?.XmlMsg?.MessageBody?.PrescriptionStatus;

                const isSuccessFile =
                  Number(segmentOfResultGetInfo) === 1 &&
                  Number(processingResultStatusGetInfo) === 1;

                if (!isSuccessFile) {
                  handleUpdateStatus(prescriptionId, 1);
                  setModalType(MODAL_TYPE.ERROR);
                  setContentError(contentError);
                }

                if (
                  isSuccessFile &&
                  prescriptionStatusGetInfo ===
                    "当該処方箋は処方箋取消されています。"
                ) {
                  handleUpdateStatus(prescriptionId, 3);
                  status = true;
                  resolve();
                }

                if (
                  isSuccessFile &&
                  prescriptionStatusGetInfo === "薬局にて受付されていません。"
                ) {
                  handleUpdateStatus(prescriptionId, 1);
                  status = true;
                  resolve();
                }

                if (
                  isSuccessFile &&
                  prescriptionStatusGetInfo &&
                  prescriptionStatusGetInfo !==
                    "薬局にて受付されていません。" &&
                  prescriptionStatusGetInfo !==
                    "当該処方箋は処方箋取消されています。"
                ) {
                  updateStatusPrescription({
                    variables: {
                      emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput:
                        {
                          prescriptionStatusItem: [
                            {
                              prescriptionId,
                              deletedReason: 0,
                              status: 0,
                              deletedDate: "0001-01-01T00:00:00",
                            },
                          ],
                          ptId: String(ptId),
                          raiinNo: String(raiinNo),
                          sinDate: Number(sinDate),
                        },
                    },
                    onError: (error) => {
                      setModalType(undefined);
                      handleError({ error }).then();
                    },
                  });
                  setModalType(MODAL_TYPE.ERROR);
                  setContentError({
                    ...contentError,
                    content:
                      "既に薬局で受付済みであるため処方箋を取消できません。",
                    content2: "",
                  });
                }
              });
          }
        });
    });
    return status;
  };

  useEffect(() => {
    if (!!prescriptionIdList?.length && type === MODAL_TYPE.LOADING) {
      const sendReq = async () => {
        let i = 0;

        while (i < prescriptionIdList.length) {
          const currentIndex = i;
          const epsPrescription = prescriptionIdList[currentIndex];
          const prescriptionIdSelected = epsPrescription?.prescriptionId;
          if (!prescriptionIdSelected) {
            const arbitraryFileIdentifier = `${dayjs().format("YYYYMMDDHHmmssSSS")}${uuidv4()}`;
            const registerResult = await handleUpsertEpsRegister({
              arbitraryFileIdentifier,
              reqDate: +dayjs().format("YYYYMMDD"),
              raiinNo: raiinNo,
              ptId: ptId,
              sinDate: sinDate,
              prescriptionId: prescriptionIdSelected,
              dispensingResultId: "",
              reqType: 11,
              status: 1,
              resultCode: "",
              resultMessage: "",
              result: "",
            });
            await new Promise<void>((resolve) => {
              const messageBody = {
                InsurerNumber: epsPrescription?.hokensyaNo,
                InsuredCardSymbol: epsPrescription?.kigo,
                InsuredIdentificationNumber: epsPrescription?.bango,
                InsuredBranchNumber: epsPrescription?.edaNo,
                IssueDateFrom: dayjs(epsPrescription?.createDate).format(
                  "YYYYMMDD",
                ),
                IssueDateTo: dayjs(epsPrescription?.createDate).format(
                  "YYYYMMDD",
                ),
              };
              system
                .createFile(
                  {
                    messageHeader: {},
                    messageBody,
                  },
                  "EPSsiPIR12req",
                )
                .then(async (files) => {
                  const xmlContent = JSON.parse(files?.data?.content || "{}");
                  const msgBody = xmlContent?.XmlMsg?.MessageBody;
                  const errorCode =
                    xmlContent?.XmlMsg?.MessageHeader?.ErrorCode;
                  const errorMsg =
                    xmlContent?.XmlMsg?.MessageHeader?.ErrorMessage;
                  const processingResultCode = msgBody?.ProcessingResultCode;
                  const processingResultMessage =
                    msgBody?.ProcessingResultMessage;

                  if (registerResult) {
                    updateEpsRegister(registerResult, null);
                  }
                  if (
                    errorCode === "EPSB0056W" ||
                    processingResultCode === "EPSB0056W"
                  ) {
                    updatePrescriptionInfo({
                      variables: {
                        input: {
                          prescriptionInfos: [
                            {
                              bango: epsPrescription?.bango,
                              deletedReason: epsPrescription?.deletedReason,
                              edaNo: epsPrescription?.edaNo,
                              hokensyaNo: epsPrescription?.hokensyaNo,
                              issueType: epsPrescription?.issueType,
                              kigo: epsPrescription?.kigo,
                              kohiFutansyaNo: epsPrescription?.kohiFutansyaNo,
                              kohiJyukyusyaNo: epsPrescription?.kohiJyukyusyaNo,
                              prescriptionDocument:
                                epsPrescription?.prescriptionDocument,
                              ptId: epsPrescription?.ptId,
                              raiinNo: epsPrescription?.raiinNo,
                              refileCount: epsPrescription?.refileCount,
                              seqNo: epsPrescription?.seqNo,
                              sinDate: epsPrescription?.sinDate,
                              status: 3,
                              accessCode: epsPrescription?.accessCode,
                              prescriptionId: epsPrescription?.prescriptionId,
                            },
                          ],
                        },
                      },
                      onError: (error) => {
                        setModalType(undefined);
                        handleError({ error }).then();
                      },
                    });
                    resolve();
                  }
                  if (
                    errorCode &&
                    errorCode !== "EPSB0056W" &&
                    processingResultCode &&
                    processingResultCode !== "EPSB0056W"
                  ) {
                    updatePrescriptionInfo({
                      variables: {
                        input: {
                          prescriptionInfos: [
                            {
                              bango: epsPrescription?.bango,
                              deletedReason: epsPrescription?.deletedReason,
                              edaNo: epsPrescription?.edaNo,
                              hokensyaNo: epsPrescription?.hokensyaNo,
                              issueType: epsPrescription?.issueType,
                              kigo: epsPrescription?.kigo,
                              kohiFutansyaNo: epsPrescription?.kohiFutansyaNo,
                              kohiJyukyusyaNo: epsPrescription?.kohiJyukyusyaNo,
                              prescriptionDocument:
                                epsPrescription?.prescriptionDocument,
                              ptId: epsPrescription?.ptId,
                              raiinNo: epsPrescription?.raiinNo,
                              refileCount: epsPrescription?.refileCount,
                              seqNo: epsPrescription?.seqNo,
                              sinDate: epsPrescription?.sinDate,
                              status: 1,
                              accessCode: epsPrescription?.accessCode,
                              prescriptionId: epsPrescription?.prescriptionId,
                            },
                          ],
                        },
                      },
                      onError: (error) => {
                        setModalType(undefined);
                        handleError({ error }).then();
                      },
                    });

                    connect.disconnect();
                    i = prescriptionIdList.length + 1;
                    setModalType(MODAL_TYPE.ERROR);
                    setContentError({
                      title: "処方箋情報の取り消しに失敗しました",
                      content: `処理結果コード: ${errorCode ? `${errorCode}\n${errorMsg}` : `${processingResultCode}\n${processingResultMessage}`}`,
                    });
                  }

                  const prescriptionIdListXml = msgBody?.PrescriptionIdList;
                  const prescriptionIdListXmlSelected =
                    prescriptionIdListXml?.filter(
                      (itemXml: PrescriptionIdListXmlProps) =>
                        itemXml?.PrescriptionId ===
                        epsPrescription?.prescriptionId,
                    );

                  if (
                    !prescriptionIdListXmlSelected?.length ||
                    !prescriptionIdListXml?.length
                  ) {
                    resolve();
                  }

                  if (
                    prescriptionIdListXml?.length > 0 &&
                    prescriptionIdListXmlSelected?.length > 0
                  ) {
                    connect.disconnect();
                    const prescriptionIdSelected =
                      prescriptionIdListXmlSelected[0]?.PrescriptionId;
                    await sendReqCancelFile02(prescriptionIdSelected).then(
                      (status) => {
                        if (status) {
                          connect.disconnect();
                          resolve();
                        } else {
                          handleUpdateStatus(prescriptionIdSelected, 1);
                          connect.disconnect();
                          i = prescriptionIdList.length + 1;
                        }
                      },
                    );
                  }

                  if (
                    prescriptionIdListXml?.length > 0 &&
                    !prescriptionIdListXmlSelected?.length
                  ) {
                    const minDatePrescription: PrescriptionIdListXmlProps =
                      prescriptionIdListXml
                        ?.filter(
                          (itemXml: PrescriptionIdListXmlProps) =>
                            Number(itemXml.CreateDateTime) >
                            Number(
                              dayjs(epsPrescription?.createDate).format(
                                "YYYYMMDDHHmmss",
                              ),
                            ),
                        )
                        .reduce(
                          (
                            min: PrescriptionIdListXmlProps,
                            item: PrescriptionIdListXmlProps,
                          ) =>
                            item.CreateDateTime < min.CreateDateTime
                              ? item
                              : min,
                        );
                    if (minDatePrescription?.PrescriptionId) {
                      updatePrescriptionInfo({
                        variables: {
                          input: {
                            prescriptionInfos: [
                              {
                                bango: epsPrescription?.bango,
                                deletedReason: epsPrescription?.deletedReason,
                                edaNo: epsPrescription?.edaNo,
                                hokensyaNo: epsPrescription?.hokensyaNo,
                                issueType: epsPrescription?.issueType,
                                kigo: epsPrescription?.kigo,
                                kohiFutansyaNo: epsPrescription?.kohiFutansyaNo,
                                kohiJyukyusyaNo:
                                  epsPrescription?.kohiJyukyusyaNo,
                                prescriptionDocument:
                                  epsPrescription?.prescriptionDocument,
                                ptId: epsPrescription?.ptId,
                                raiinNo: epsPrescription?.raiinNo,
                                refileCount: epsPrescription?.refileCount,
                                seqNo: epsPrescription?.seqNo,
                                sinDate: epsPrescription?.sinDate,
                                status: epsPrescription?.status,
                                accessCode: minDatePrescription?.AccessCode,
                                prescriptionId:
                                  minDatePrescription?.PrescriptionId,
                              },
                            ],
                          },
                        },
                        onError: (error) => {
                          setModalType(undefined);
                          handleError({ error }).then();
                        },
                      });
                      await sendReqCancelFile02(
                        minDatePrescription?.PrescriptionId,
                      ).then((status) => {
                        if (status) {
                          connect.disconnect();
                          resolve();
                        } else {
                          handleUpdateStatus(
                            minDatePrescription?.PrescriptionId,
                            1,
                          );
                          connect.disconnect();
                          i = prescriptionIdList.length + 1;
                        }
                      });
                    } else {
                      handleUpdateStatus(epsPrescription?.prescriptionId, 3);
                      resolve();
                    }
                  }
                });
            });
          } else {
            await sendReqCancelFile02(
              String(epsPrescription?.prescriptionId),
            ).then((status) => {
              if (status) {
                connect.disconnect();
              } else {
                handleUpdateStatus(epsPrescription?.prescriptionId, 1);
                connect.disconnect();
                i = prescriptionIdList.length + 1;
              }
            });
          }
          i++;
        }

        if (i === prescriptionIdList.length) {
          connect.disconnect();
          setModalType(MODAL_TYPE.DONE);
        }
      };
      sendReq();
    }
  }, [prescriptionIdList, type, systemConf]);

  const titleModal = useMemo(() => {
    switch (type) {
      case MODAL_TYPE.ERROR:
        return "エラー";

      case MODAL_TYPE.LOADING:
        return "処理中";

      default:
        return "処方箋情報の取消";
    }
  }, [type]);

  const contentModal = useMemo(() => {
    switch (type) {
      case MODAL_TYPE.ERROR:
        return (
          <ContentErrorWapper>
            <SvgIconError />
            <Title level={2}>{contentError.title}</Title>
            <div style={{ width: "100%" }}>{contentError.content}</div>
            {contentError.content2 ? (
              <div style={{ width: "100%" }}>{contentError.content2}</div>
            ) : (
              ""
            )}
          </ContentErrorWapper>
        );

      case MODAL_TYPE.LOADING:
        return (
          <ModalContentWrapper>
            <span>処方箋情報を取り消しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        );
      case MODAL_TYPE.HAVE_PRESCRIPTION:
        return (
          <ModalContentConfirm>
            <Title level={2}>処方箋情報を取り消しできません</Title>
            <Text>この処方箋は薬局で受付済です。</Text>
          </ModalContentConfirm>
        );
      case MODAL_TYPE.HAVE_NOT_PRESCRIPTION:
        return (
          <ModalContentConfirm>
            <Title level={2}>処方箋情報を取り消す</Title>
            <Text>
              この来院は、{`${numberOfRegistered}件`}
              の処方箋が登録されています。<br></br>
              すべて取り消してよろしいですか？取り消し後は元に戻すことはできません。
            </Text>
          </ModalContentConfirm>
        );
      default:
        return (
          <ModalContentConfirm>
            <Title level={2}>処方箋情報を取り消しました</Title>
          </ModalContentConfirm>
        );
    }
  }, [type, numberOfRegistered, contentError]);

  const textCloseModal = useMemo(() => {
    switch (type) {
      case MODAL_TYPE.LOADING:
        return "キャンセル";
      default:
        return "閉じる";
    }
  }, [type]);

  const handleSubmit = () => {
    if (registerResult) {
      updateEpsRegister(registerResult, null);
    }
    if (registerResultDIM06req) {
      updateEpsRegister(registerResultDIM06req, null);
    }
    submit();
  };
  return (
    <StyledModal
      centered
      title={titleModal}
      $errorModal={type === MODAL_TYPE.ERROR}
      width={480}
      isOpen={!!type}
      onCancel={() => setModalType(undefined)}
      centerFooterContent={MODAL_TYPE.HAVE_NOT_PRESCRIPTION !== type}
      footer={
        MODAL_TYPE.HAVE_NOT_PRESCRIPTION === type
          ? [
              <Button
                key={"cancel"}
                varient="tertiary"
                onClick={() => setModalType(undefined)}
              >
                キャンセル
              </Button>,
              <Button key={"ok"} varient="emphasis" onClick={handleSubmit}>
                取消
              </Button>,
            ]
          : [
              <Button
                key={"cancel"}
                disabled={disableCancel && type === MODAL_TYPE.LOADING}
                varient="tertiary"
                onClick={handleCancelModal}
              >
                {textCloseModal}
              </Button>,
            ]
      }
    >
      {contentModal}
    </StyledModal>
  );
};
