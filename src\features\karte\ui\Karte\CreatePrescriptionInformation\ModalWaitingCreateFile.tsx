import React, { useCallback, useEffect, useState } from "react";

import styled from "styled-components";
import { Flex, Spin } from "antd";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { RenderIf } from "@/utils/common/render-if";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { useLock } from "@/features/karte/providers/LockInforProvider";
import { usePrintOutPatientPrescription } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/usePrintOutPatientPrescription";

import { useEPrescriptionContext } from "../PrintSetting/EPrescriptionContextProvider";
import { useObserverWaitingModalContext } from "../StationPrescription/ObserverWaitingModalActionProvider";
import { useCancelRegister } from "../CancelRegister";

import { ModalConfirm } from "./ModalConfirm";
import { useCreateDrugXml } from "./useCreateDrugXml";

import type { PrescriptionDrugLocalFlow } from "@/features/karte/ui/Karte/FlowLocalPresciption";
import type { PrescriptionDrugXml } from "../PrintSetting/EPrescriptionContextProvider";
import type { Dispatch, SetStateAction } from "react";

const StyleModal = styled(Modal)`
  .ant-modal-footer {
    justify-content: center;
  }
`;

const Wrapper = styled.div`
  padding: 24px 32px 24px 24px;
  height: 256px;
`;

type Props = {
  setProcessRecordPrescription: Dispatch<
    SetStateAction<PrescriptionDrugLocalFlow | null>
  >;
  prescription: PrescriptionDrugLocalFlow;
};
export const ModalWaitingCreateFile = ({
  setProcessRecordPrescription,
  prescription,
}: Props) => {
  const { handleCloseModal } = useModal();
  const { timeout, createDrugXML, handleDisconnectSocket } = useCreateDrugXml({
    onError: () => {
      handleCloseModal("WAITING_CREATE_FILE");
      setResolveBreakModalFlowLocalPrescription();
    },
  });
  const { statePrintSetting } = useEPrescriptionContext();
  const [disabled, setDisabled] = useState(true);
  const [showModalConfirm, setShowModalConfirm] = useState(false);
  const { setResolveBreakModalFlowLocalPrescription, setResolveWaitModal } =
    useObserverWaitingModalContext();
  const { handlePrintOutPatientPrescriptionPaper } =
    usePrintOutPatientPrescription();

  const { handleCancelRegister } = useCancelRegister();
  const { changeLockMode } = useLock();

  const handleClickButton = () => {
    if (statePrintSetting?.isOutpatientPrescription === false) {
      setResolveWaitModal();
      setResolveBreakModalFlowLocalPrescription();
      handleCancelRegister();
      handlePrintOutPatientPrescriptionPaper().then();
    } else {
      setShowModalConfirm(true);
    }
  };

  useEffect(() => {
    if (timeout > 0) {
      const timer = setTimeout(() => {
        setDisabled(false);
      }, timeout * 1000);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [timeout]);

  const getContenXML = useCallback(
    async (csvBase64: string) => {
      const data = await createDrugXML(csvBase64);
      return data;
    },
    [createDrugXML],
  );

  const convertXMLToBase64 = useCallback((xml: string) => {
    const encoder = new TextEncoder();
    const encodedData = encoder.encode(xml);
    return btoa(String.fromCharCode(...encodedData));
  }, []);

  const handleProcessPrescriptionData = useCallback(
    async (prescription: PrescriptionDrugXml) => {
      const contentXml = await getContenXML(
        prescription.prescriptionDocument ?? "",
      );
      const base64 = convertXMLToBase64(contentXml?.data?.DrugXml ?? "");

      setProcessRecordPrescription((prevState) => {
        if (!prevState) return prevState;
        return {
          ...prevState,
          drugXml: base64,
        };
      });

      // await sleep(3000);

      // Cleanup UI state
      handleCloseModal("WAITING_CREATE_FILE");
      setResolveWaitModal();
      setShowModalConfirm(false);
      setDisabled(false);
    },
    [
      getContenXML,
      convertXMLToBase64,
      handleCloseModal,
      setShowModalConfirm,
      setDisabled,
      setProcessRecordPrescription,
      setResolveWaitModal,
    ],
  );

  useEffect(() => {
    handleProcessPrescriptionData(prescription);
  }, []);

  const handleClose = () => {
    setResolveWaitModal();
    setResolveBreakModalFlowLocalPrescription();
    handleDisconnectSocket();
    handleCloseModal("WAITING_CREATE_FILE");
    setDisabled(false);

    setResolveWaitModal();
    changeLockMode(false);

    handleCancelRegister();
  };
  return (
    <StyleModal
      width={480}
      isOpen={true}
      title={"処理中"}
      onCancel={handleClose}
      footer={[
        <Button
          key="close"
          varient="tertiary"
          shape="round"
          onClick={handleClose}
          disabled={disabled}
        >
          キャンセル
        </Button>,
      ]}
    >
      <Wrapper>
        <Flex
          style={{
            minHeight: 200,
            flexDirection: "column",
            justifyContent: "space-between",
          }}
        >
          <Flex
            align="center"
            justify="center"
            style={{
              flexDirection: "column",
              marginTop: "auto",
              marginBottom: "auto",
            }}
          >
            <p style={{ textAlign: "center" }}>処方箋情報XMLを作成しています</p>
            <Flex align="center" justify="center" style={{ marginTop: 24 }}>
              <Spin size="large" />
            </Flex>
          </Flex>

          <Flex justify="flex-end">
            <Button
              varient="inline"
              disabled={disabled}
              onClick={handleClickButton}
            >
              処方箋情報を登録しない
            </Button>
          </Flex>
        </Flex>
      </Wrapper>
      <RenderIf condition={showModalConfirm}>
        <ModalConfirm onClose={() => setShowModalConfirm(false)} />
      </RenderIf>
    </StyleModal>
  );
};
