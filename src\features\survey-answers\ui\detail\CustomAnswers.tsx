import Link from "next/link";
import styled from "styled-components";

import { getCustomAnswersField } from "../../utils/survey";

import type { CustomAnswerType } from "@/types/survey";
import type { Files } from "../../utils/survey";

const FieldList = styled.li`
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  width: 100%;
  & + & {
    margin-top: 20px;
  }
`;

const FieldLabel = styled.p`
  margin: 0 16px 0 0;
  font-size: 14px;
  flex: 0 0 32%;
  max-width: 32%;
  word-break: break-word;
  white-space: pre-line;
  line-height: 1.6;
`;

const FieldText = styled.p`
  flex: 1 1 68%;
  margin: 0;
  font-size: 14px;
  white-space: pre-wrap;
  word-break: break-word;
  line-height: 1.6;
`;

export const CustomAnswers: React.FC<{
  answers: CustomAnswerType[];
  files: Files[];
}> = ({ answers, files }) => {
  const fields = getCustomAnswersField(answers, files);
  return fields.map(({ key, value, url }) => {
    return (
      <FieldList key={key}>
        <FieldLabel>【{key}】</FieldLabel>
        {url ? (
          <Link href={url} target="_blank" rel="noopener noreferrer">
            <FieldText>{value || "-"}</FieldText>
          </Link>
        ) : (
          <FieldText>{value || "-"}</FieldText>
        )}
      </FieldList>
    );
  });
};
