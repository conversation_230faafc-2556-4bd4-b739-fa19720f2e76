import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";

import { useUpdateTreatmentsMenu } from "../hooks/useUpdateTreatmentsMenu";
import { useModal } from "../providers/ModalProvider";

import type { GetTreatmentCategoriesQuery } from "@/apis/gql/operations/__generated__/treatment-category";
import type { GetTreatmentDepartmentsQuery } from "@/apis/gql/operations/__generated__/treatment";

const TextContent = styled.div`
  font-size: 14px;
  margin: 10px;
`;

const ContentWrapper = styled.div`
  padding: 14px 24px;
  height: 256px;
`;

type TreatmentMenu = NonNullable<
  GetTreatmentDepartmentsQuery["getTreatmentDepartments"][number]
>;

type TreatmentCategory = NonNullable<
  GetTreatmentCategoriesQuery["getTreatmentCategories"][number]
>;

type Props = {
  departments: TreatmentMenu[];
  tempSelectedTreatmentCategories: TreatmentCategory[];
  onSelectCategories: (param: TreatmentCategory[]) => void;
  onEditTreatmentMenuComplete: () => void;
};

export const TreatmentCategoryConfirmModal: React.FC<Props> = ({
  departments,
  onSelectCategories,
  tempSelectedTreatmentCategories,
  onEditTreatmentMenuComplete,
}) => {
  const {
    state: { categoryConfirmModal },
    handleCloseModal,
  } = useModal();

  const onClose = () => {
    handleCloseModal("CATEGORY_CONFIRM_MODAL");
  };

  const { handleCategoryConfirmModalSave } = useUpdateTreatmentsMenu(
    tempSelectedTreatmentCategories,
    departments,
    onEditTreatmentMenuComplete,
  );
  return (
    <Modal
      isOpen={categoryConfirmModal}
      title={"診療科選択の確認"} //TODO
      centered
      onCancel={onClose}
      zIndex={1200}
      footer={[
        <Button key="cancel" varient="tertiary" onClick={onClose}>
          キャンセル
        </Button>,
        <Button
          key="submit"
          varient="primary"
          htmlType="submit"
          onClick={() => {
            handleCategoryConfirmModalSave();
            onSelectCategories(tempSelectedTreatmentCategories);
            onClose();
            handleCloseModal("SELECT_MODAL");
          }}
        >
          保存
        </Button>,
      ]}
    >
      <ContentWrapper>
        <TextContent>
          選択していない診療科のメニューは無効になります。
          <br />
          よろしいですか？
        </TextContent>
      </ContentWrapper>
    </Modal>
  );
};
