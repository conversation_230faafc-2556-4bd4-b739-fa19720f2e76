import { useState } from "react";

import { useGetHpFincodeInfoByHospitalQuery } from "@/apis/gql/operations/__generated__/payment";
import { useGetTreatmentDepartmentsSettingQuery } from "@/apis/gql/operations/__generated__/treatment";
import { useGetTreatmentCategoriesQuery } from "@/apis/gql/operations/__generated__/treatment-category";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import { TREATMENT_MENU_LIMIT } from "../constant";

import type { TreatmentMenu } from "./useSettingTreatmentDepartment";

export const useTreatmentDepartment = () => {
  const [hasMore, setHasMore] = useState(true);
  const [departments, setDepartments] = useState<TreatmentMenu[]>([]);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const { handleError } = useErrorHandler();
  const [getDepartments, setGetDepartments] = useState(true);

  const { loading: departmentLoading, fetchMore } =
    useGetTreatmentDepartmentsSettingQuery({
      variables: {
        input: { limit: TREATMENT_MENU_LIMIT },
      },
      onError: (error) => {
        handleError({
          error,
          commonMessage: "診療メニューが取得できませんでした",
        });
      },
      onCompleted: ({ getTreatmentDepartmentsSetting }) => {
        setGetDepartments(false);
        setDepartments(getTreatmentDepartmentsSetting);
        if (getTreatmentDepartmentsSetting.length < TREATMENT_MENU_LIMIT) {
          setHasMore(false);
        }
      },
      skip: !getDepartments,
    });

  const { loading: categoryLoading, data: categoryData } =
    useGetTreatmentCategoriesQuery({
      onError: (error) => {
        handleError({
          error,
          commonMessage: "診療科情報が取得できませんでした",
        });
      },
    });

  const handleLoadMore = (forceLoadMore?: boolean) => {
    if (!hasMore && !forceLoadMore) return;
    const newLimit = departments.length + TREATMENT_MENU_LIMIT;

    setIsFetchingMore(true);
    fetchMore({
      variables: {
        input: {
          limit: newLimit,
        },
      },
    }).then(({ data }) => {
      setDepartments([...data.getTreatmentDepartmentsSetting]);
      setIsFetchingMore(false);
      if (data.getTreatmentDepartmentsSetting.length < newLimit) {
        setHasMore(false);
      }
    });
  };

  const handleCreateTreatmentMenuComplete = () => {
    handleLoadMore(true);
  };

  const handleEditTreatmentMenuComplete = () => {
    setGetDepartments(true);
    setHasMore(true);
  };

  const handleDeleteTreatmentMenuComplete = (treatmentDepartmentId: number) => {
    const updatedDepartments = departments.filter(
      (department) =>
        department.treatmentDepartmentId !== treatmentDepartmentId,
    );
    setDepartments(updatedDepartments);
  };

  const { data: fincodeInfoData } = useGetHpFincodeInfoByHospitalQuery({
    onError: (error) => {
      handleError({ error });
    },
  });

  return {
    loading: departmentLoading || categoryLoading,
    departments,
    categories: categoryData?.getTreatmentCategories.filter(
      (item) => item.treatmentCategoryId !== 84, //気管食道・耳鼻咽喉科
    ),
    isFetchingMore,
    handleLoadMore,
    handleCreateTreatmentMenuComplete,
    handleEditTreatmentMenuComplete,
    handleDeleteTreatmentMenuComplete,
    hpFincodeInfo: fincodeInfoData?.getHpFincodeInfoByHospital,
  };
};
