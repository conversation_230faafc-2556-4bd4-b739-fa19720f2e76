# 代理ログイン用FEのbuildspec
version: 0.2

env:
  shell: bash
phases:
  install:
    runtime-versions:
      nodejs: 22
    commands:
      - n 22.16.0
  pre_build:
    commands:
      ### PRE-CHECK
      - pwd
      - rm -rf .npmrc
      - aws --version
      - node -v
      - yarn -v
      - ls -la
      ### ADD-ENV VARIABLES
      - NEXT_PUBLIC_CLIENT_URL=${NEXT_PUBLIC_CLIENT_URL}
      - NEXT_PUBLIC_SERVER_API_BASE_URL=${NEXT_PUBLIC_SERVER_API_BASE_URL}
      - NEXT_PUBLIC_SERVER_GRAPHQL_BASE_URL=${NEXT_PUBLIC_SERVER_GRAPHQL_BASE_URL}
      - NEXT_PUBLIC_SERVER_GRAPHQL_WS_BASE_URL=${NEXT_PUBLIC_SERVER_GRAPHQL_WS_BASE_URL}
      - NEXT_PUBLIC_IS_OPERATOR_ACCESS=${NEXT_PUBLIC_IS_OPERATOR_ACCESS}
      ### NODE_MODULE INSTALL
      - yarn install
      ### CODE CHECK
      - export NODE_OPTIONS="--max-old-space-size=8192"
      - yarn type-check
      - yarn lint
  build:
    commands:
      ### BUILD
      - yarn build:dev
    on-failure: ABORT
  post_build:
    commands:
      ### DEPLOY
      ### AWS S3 SYNC
      - aws s3 sync --region ap-northeast-1 ./out s3://${S3_BUCKET_NAME} --delete
      ### STEP 2) AWS CloudFront RESET
      - aws cloudfront create-invalidation --distribution-id ${CF_DISTRIBUTION_ID} --paths "/*"
    on-failure: ABORT

cache:
  paths:
    - /root/.cache/yarn/**/*
