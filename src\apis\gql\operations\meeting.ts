import { gql } from "@apollo/client";

export const GET_MEETING = gql`
  query getMeetingDetail($meetingId: Int!) {
    getMeetingDetail(meetingId: $meetingId) {
      meetingId
      chimeMeetingId
      status
      patient {
        patientID
        patientName
        patientNameKana
        birthdate
        gender
        phoneNumber1
        phoneNumber2
      }
      reservation {
        reserveId
        reservationDetails {
          status
          reserveId
          reserveDetailId
          updatedAt
          calendarTreatment {
            calendarTreatmentID
            treatmentDepartment {
              treatmentDepartmentId
              title
            }
          }
          examTimeSlot {
            examTimeSlotID
            examStartDate
            examEndDate
            calendar {
              calendarID
            }
          }
          patient {
            patientID
            patientName
            patientNameKana
            birthdate
            gender
            phoneNumber1
            phoneNumber2
          }
        }
      }
      pharmacyReserve {
        pharmacyReserveId
        desiredDateStatus
        desiredDate {
          desiredDate
          desiredType
          pharmacyDesiredDateId
        }
        customer {
          name
          kanaName
          birthday
          gender
          telephone
        }
      }
    }
  }
`;

export const UPDATE_MEETING = gql`
  mutation updateMeeting($input: UpdateMeetingRequest!) {
    updateMeeting(input: $input) {
      meetingId
      status
    }
  }
`;

export const SUBSCRIBE_MEETING = gql`
  subscription notifyMeeting($meetingId: Int!) {
    notifyMeeting(meetingId: $meetingId) {
      meetingId
      chimeMeetingId
      status
      patient {
        patientID
        patientName
        patientNameKana
        birthdate
        gender
        phoneNumber1
        phoneNumber2
      }
      reservation {
        reserveId
        reservationDetails {
          status
          reserveId
          reserveDetailId
          updatedAt
          calendarTreatment {
            calendarTreatmentID
            treatmentDepartment {
              treatmentDepartmentId
              title
            }
          }
          examTimeSlot {
            examTimeSlotID
            examStartDate
            examEndDate
            calendar {
              calendarID
            }
          }
          patient {
            patientID
            patientName
            patientNameKana
            birthdate
            gender
            phoneNumber1
            phoneNumber2
          }
        }
      }
      pharmacyReserve {
        pharmacyReserveId
        desiredDate {
          desiredDate
          desiredType
          pharmacyDesiredDateId
        }
        customer {
          name
          kanaName
          birthday
          gender
          telephone
        }
      }
    }
  }
`;

export const NOTIFY_MEETING_TO_PATIENT = gql`
  mutation notifyUpdatedMeetingToPatient($meetingId: Int!) {
    notifyUpdatedMeetingToPatient(meetingId: $meetingId) {
      success
    }
  }
`;
