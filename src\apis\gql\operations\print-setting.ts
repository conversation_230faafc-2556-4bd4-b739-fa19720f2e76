import { gql } from "@apollo/client";

export const GET_API_MST_ITEM_ALL_CHECK_MST = gql`
  query getApiMstItemGetAllCmtCheckMst($sinDay: Int) {
    getApiMstItemGetAllCmtCheckMst(sinDay: $sinDay) {
      data {
        itemCmtModels {
          hpId
          isDeleted
          santeiItemCd
          masterSbt
          yohoKbn
          itemCd
          kanaName1
          kanaName2
          kanaName3
          kohatuKbn
          kohatuKbnDisplay
          kouiName
          ten
          tenDisplay
          tenId
          tenMstName
        }
      }
    }
  }
`;

export const POST_API_ERROR_PRINT_SETTING = gql`
  mutation postApiEpsCheckErrorTodayOdrForEPSRegistrationPrintSetting(
    $input: EmrCloudApiRequestsEpsGetPreRegistrationDataRequestInput
  ) {
    postApiEpsCheckErrorTodayOdrForEPSRegistration(
      emrCloudApiRequestsEpsGetPreRegistrationDataRequestInput: $input
    ) {
      data {
        errorMessageIsErrorAcceptedByPharmacy
        errorMessageIsErrorDetailBunkatu
        errorMessageIsErrorDetailRefill
        errorMessageIsErrorHokenExpired
        errorMessageIsErrorHokenNotCoverDrug
        errorMessageIsErrorHokenNotHealthInsurance
        errorMessageIsErrorManyHokenUsingForRp
        errorMessageIsErrorMedicalMaterialsNotCovered
        errorMessageIsErrrorContainsGenericNamesNotIncluded
        flagPrintDrugInformationSheet
        flagPrintHospitalPrescription
        flagPrintInstruction
        flagPrintOutpatientPrescription
      }
      message
      status
    }
  }
`;

export const GET_DEFAULT_VALUE_PRINT_SETTING = gql`
  query getApiReceptionGetLastRaiinInfsForPrintSetting(
    $ptId: BigInt
    $sinDate: Int
    $isLastVisit: Boolean
  ) {
    getApiReceptionGetLastRaiinInfs(
      isLastVisit: $isLastVisit
      ptId: $ptId
      sinDate: $sinDate
    ) {
      data {
        data {
          sinDate
          tantoId
          canCombine
          comment
          confirmationType
          departmentSName
          hasMessage
          hokenHoubetu
          hokenId
          hokenKbn
          hokenKbnName
          hokenName
          hokenPid
          hokenSbtCd
          hokensyaNo
          houbetu
          hpId
          infoConsFlg
          isDeleted
          isYoyaku
          jikanKbn
          kaId
          kaSname
          kaikeiId
          kaikeiTime
          kateEditionRaiinNo
          onlineConfirmationHistoryId
          oyaRaiinNo
          raiinNo
          prescriptionIssueType
          printEpsReference
          syosaisinKbn
          updateDate
        }
      }
    }
  }
`;

export const GET_API_PDF_DRUG_INFO = gql`
  query getApiPdfCreatorExportDrugInfo(
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
  ) {
    getApiPdfCreatorExportDrugInfo(
      raiinNo: $raiinNo
      ptId: $ptId
      sinDate: $sinDate
    ) {
      fileUrl
      message
      status
    }
  }
`;

export const GET_API_PDF_SIJISEN = gql`
  query getApiPdfCreatorExportSijisen(
    $formType: Int
    $odrKouiKbns: [EmrCloudApiRequestsExportPdFLimitModelInput]
    $printNoOdr: Boolean
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
  ) {
    getApiPdfCreatorExportSijisen(
      formType: $formType
      odrKouiKbns: $odrKouiKbns
      printNoOdr: $printNoOdr
      raiinNo: $raiinNo
      ptId: $ptId
      sinDate: $sinDate
    ) {
      fileUrl
      message
      status
    }
  }
`;

export const POST_API_PDF_SIJISEN = gql`
  mutation postApiPdfCreatorExportSijisen(
    $emrCloudApiRequestsExportPdFSijisenExportRequestInput: EmrCloudApiRequestsExportPdFSijisenExportRequestInput
  ) {
    postApiPdfCreatorExportSijisen(
      emrCloudApiRequestsExportPdFSijisenExportRequestInput: $emrCloudApiRequestsExportPdFSijisenExportRequestInput
    ) {
      data {
        base64Pdfs
        fileUrl
        message
        status
      }
    }
  }
`;

export const GET_API_PDF_KARTE1 = gql`
  query getApiPdfCreatorExportKarte1PrintSetting(
    $hokenPid: Int
    $ptId: BigInt
    $sinDate: Int
    $syuByomei: Boolean
    $tenkiByomei: Boolean
  ) {
    getApiPdfCreatorExportKarte1(
      hokenPid: $hokenPid
      ptId: $ptId
      sinDate: $sinDate
      syuByomei: $syuByomei
      tenkiByomei: $tenkiByomei
    ) {
      fileUrl
      message
      status
    }
  }
`;

export const GET_API_PDF_OUT_DRUG = gql`
  query getApiPdfCreatorOutDrug(
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
    $hokenGp: Int
    $epsPrintType: Int
    $delayPrint: Boolean
    $windowType: WindowType
    $registrationChecked: Boolean
    $isPrescriptionCanCancel: Boolean
    $printEpsReference: Int
    $issueType: Int
  ) {
    getApiPdfCreatorOutDrug(
      raiinNo: $raiinNo
      ptId: $ptId
      sinDate: $sinDate
      hokenGp: $hokenGp
      epsPrintType: $epsPrintType
      delayPrint: $delayPrint
      windowType: $windowType
      registrationChecked: $registrationChecked
      isPrescriptionCanCancel: $isPrescriptionCanCancel
      printEpsReference: $printEpsReference
      issueType: $issueType
    ) {
      base64Pdfs
      fileUrl
      message
      status
    }
  }
`;

export const GET_API_PDF_IN_DRUG = gql`
  query getApiPdfCreatorInDrug($ptId: BigInt, $sinDate: Int, $raiinNo: BigInt) {
    getApiPdfCreatorInDrug(raiinNo: $raiinNo, ptId: $ptId, sinDate: $sinDate) {
      fileUrl
      message
      status
    }
  }
`;

export const GET_API_PDF_KARTE_2 = gql`
  query getApiPdfCreatorExportKarte2PrintSetting(
    $deletedOdrVisibilitySetting: Int
    $emptyMode: Boolean
    $endDate: Int
    $hpId: Int
    $isCheckedApproved: Boolean
    $isCheckedDoctor: Boolean
    $isCheckedEndTime: Boolean
    $isCheckedHideOrder: Boolean
    $isCheckedHoken: Boolean
    $isCheckedHokenJibai: Boolean
    $isCheckedHokenJihi: Boolean
    $isCheckedHokenRousai: Boolean
    $isCheckedInputDate: Boolean
    $isCheckedJihi: Boolean
    $isCheckedJihiRece: Boolean
    $isCheckedSetName: Boolean
    $isCheckedStartTime: Boolean
    $isCheckedSyosai: Boolean
    $isCheckedVisitingTime: Boolean
    $isIncludeTempSave: Boolean
    $isIppanNameChecked: Boolean
    $isUketsukeNameChecked: Boolean
    $ptId: BigInt
    $raiinNo: BigInt
    $sinDate: Int
    $startDate: Int
    $includeDraft: Boolean
    $isGetVersionData: Boolean
  ) {
    getApiPdfCreatorExportKarte2(
      sinDate: $sinDate
      raiinNo: $raiinNo
      ptId: $ptId
      deletedOdrVisibilitySetting: $deletedOdrVisibilitySetting
      emptyMode: $emptyMode
      endDate: $endDate
      hpId: $hpId
      isCheckedApproved: $isCheckedApproved
      isCheckedDoctor: $isCheckedDoctor
      isCheckedEndTime: $isCheckedEndTime
      isCheckedHideOrder: $isCheckedHideOrder
      isCheckedHoken: $isCheckedHoken
      isCheckedHokenJibai: $isCheckedHokenJibai
      isCheckedHokenJihi: $isCheckedHokenJihi
      isCheckedHokenRousai: $isCheckedHokenRousai
      isCheckedInputDate: $isCheckedInputDate
      isCheckedJihi: $isCheckedJihi
      isCheckedJihiRece: $isCheckedJihiRece
      isCheckedSetName: $isCheckedSetName
      isCheckedStartTime: $isCheckedStartTime
      isCheckedSyosai: $isCheckedSyosai
      isCheckedVisitingTime: $isCheckedVisitingTime
      isIncludeTempSave: $isIncludeTempSave
      isIppanNameChecked: $isIppanNameChecked
      isUketsukeNameChecked: $isUketsukeNameChecked
      startDate: $startDate
      includeDraft: $includeDraft
      isGetVersionData: $isGetVersionData
    ) {
      fileUrl
      message
      status
    }
  }
`;

export const POST_API_PDF_CREATOR_OUT_DRUG = gql`
  mutation postApiPdfCreatorOutDrug(
    $ptId: BigInt
    $sinDate: Int
    $raiinNo: BigInt
    $hokenGp: Int
    $epsPrintType: Int
    $delayPrint: Boolean
    $windowType: WindowType
    $registrationChecked: Boolean
    $isPrescriptionCanCancel: Boolean
    $printEpsReference: Int
    $issueType: Int
  ) {
    postApiPdfCreatorOutDrug(
      raiinNo: $raiinNo
      ptId: $ptId
      sinDate: $sinDate
      hokenGp: $hokenGp
      epsPrintType: $epsPrintType
      delayPrint: $delayPrint
      windowType: $windowType
      registrationChecked: $registrationChecked
      isPrescriptionCanCancel: $isPrescriptionCanCancel
      printEpsReference: $printEpsReference
      issueType: $issueType
    ) {
      base64Pdfs
      fileUrl
      message
      status
    }
  }
`;
