import styled from "styled-components";

import { SvgIconComplete } from "@/components/ui/Icon/IconComplete";

/**
 * Web問診票：完了フォーム
 */

export const WebSurveyCompleteForm = () => {
  return (
    <Wrapper>
      <ContentArea>
        <StyledTitle>提出完了</StyledTitle>
        <SvgIconComplete />
        <StyledMessage>問診票のご提出ありがとうございました。</StyledMessage>
      </ContentArea>
    </Wrapper>
  );
};

const Wrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: start;
  overflow-y: auto;

  @media (max-width: 600px) {
    height: 100%;
    border-radius: unset;
    margin-top: unset;
  }
`;

const ContentArea = styled.div`
  width: 640px;
  margin: 40px 0;
  background-color: #fff;
  border-radius: 26px;
  text-align: center;

  @media (max-width: 600px) {
    margin: unset;
  }
`;

const StyledTitle = styled.div`
  font-family: "NotoSansJP";
  font-size: 20px;
  font-weight: bold;
  color: "#243544";
  padding-bottom: 20px;
  margin: 40px 40px 20px;
  border-bottom: solid 4px #e2e3e5;
  text-align: left;

  @media (max-width: 600px) {
    margin: 20px 20px;
    text-align: center;
  }
`;

const StyledMessage = styled.div`
  font-size: 16px;
  color: "#243544";
  margin: 20px 0 60px;
`;
