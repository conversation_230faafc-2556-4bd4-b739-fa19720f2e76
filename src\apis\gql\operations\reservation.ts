import { gql } from "@apollo/client";

export const GET_RESERVATIONS = gql`
  query getReservationDetailsByConditions(
    $input: GetReservationDetailsByConditions!
  ) {
    getReservationDetailsByConditions(input: $input) {
      reserveId
      reserveDetailId
      status
      treatmentType
      reserveType
      updatedAt
      memo
      isSuspendedReservation
      paymentStatus
      examTimeSlot {
        examTimeSlotID
        examStartDate
        examEndDate
        treatmentType
        slotLimitReserveNum
        calendar {
          calendarID
          label
          calendarName
          doctor {
            staffId
            staffName
          }
          isActive
        }
      }
      patient {
        patientName
        patientNameKana
        patientID
        patientNumber
        phoneNumber1
        phoneNumber2
        birthdate
      }
      paymentDetail {
        paymentClinicDetailId
      }
      pharmacyReserveDetail {
        pharmacyReserveDetailId
        paymentStatus
      }
      reservation {
        patientId
        meeting {
          meetingId
          status
        }
      }
      calendarTreatment {
        calendarTreatmentID
        treatmentDepartment {
          treatmentType
          title
          firstConsultationTime
          nextConsultationTime
          treatmentCategory {
            name
          }
          treatmentDepartmentStatus
          isDeleted
        }
      }
      reservationDetailHistories {
        reserveDetailHistoryId
        status
        createdAt
        calendarTreatment {
          treatmentDepartment {
            title
            treatmentType
            treatmentCategory {
              name
            }
          }
        }
        examTimeSlot {
          examTimeSlotID
          examStartDate
          examEndDate
          treatmentType
          calendar {
            calendarID
            label
            calendarName
            doctor {
              staffId
              staffName
            }
          }
        }
      }
      isSurveyAnswered
      raiinInfo {
        status
      }
    }
  }
`;

export const GET_RESERVATION_DETAIL = gql`
  query getReservationDetailById($input: GetReservationDetailByIdInput!) {
    getReservationDetailById(input: $input) {
      reserveId
      reserveDetailId
      isSuspendedReservation
      treatmentType
      reserveType
      memo
      status
      fincodeCustomerId
      paymentCardId
      examTimeSlot {
        examTimeSlotID
        examStartDate
        examEndDate
        treatmentType
        calendar {
          calendarID
          reservationMethodType
          label
          calendarName
          doctor {
            staffId
            staffName
          }
        }
      }
      reservation {
        meeting {
          meetingId
        }
      }
      patient {
        patientID
        patientName
        patientNameKana
        patientNumber
        phoneNumber1
        phoneNumber2
        birthdate
        gender
      }
      calendarTreatment {
        calendarTreatmentID
        treatmentDepartment {
          treatmentDepartmentId
          treatmentType
          title
          firstConsultationTime
          nextConsultationTime
        }
      }
      paymentDetail {
        paymentClinicDetailId
        paymentType
        billingAmount
        paymentStatus
      }
      reservation {
        patientId
        prescriptionReceiveMethod
        reservationDetails {
          patient {
            patientID
            patientName
            patientNameKana
          }
        }
        pharmacyReserve {
          pharmacyReserveId
          desiredDateStatus
          pharmacyDeliveryAddress {
            pharmacyDeliveryAddressId
            pharmacyReserveId
            address1
            address2
            postCode
            phoneNumber
          }
        }
        portalCustomerPharmacy {
          portalCustomerPharmacyId
          pharmacyName
          pharmacyStoreName
          address1
          address2
          faxNumber
          postCode
          phoneNumber
        }
      }
      isSurveyAnswered
      paymentStatus
    }
  }
`;

export const RESERVE = gql`
  mutation reserve($input: ReservationCreateInput!) {
    reserve(input: $input) {
      reserveId
      reserveDetailId
      registerRaiinInput {
        receptionModel {
          hpId
          ptId
          sinDate
          yoyakuId
          yoyakuTime
          yoyakuEndTime
          treatmentDepartmentId
          reserveDetailId
          tantoId
        }
        raiinComment
      }
    }
  }
`;

export const UPDATE_RESERVATION = gql`
  mutation updateReservation(
    $reserveId: Int!
    $reserveDetailId: Int!
    $calendarId: Int!
    $input: ReservationUpdateInput!
    $resendMessage: Boolean
  ) {
    updateReservation(
      calendarId: $calendarId
      reserveId: $reserveId
      reserveDetailId: $reserveDetailId
      input: $input
      resendMessage: $resendMessage
    ) {
      reserveId
      reserveDetailId
      calendarId
    }
  }
`;

export const CANCEL_RESERVATION = gql`
  mutation cancelReservation($input: CancelReservationByIdInput!) {
    cancelReservation(input: $input)
  }
`;

export const UPDATE_RESERVATIONS_TO_COMPLETE = gql`
  mutation updateTreatmentStatusToCompleted($reserveDetailIds: [Int!]!) {
    updateTreatmentStatusToCompleted(reserveDetailIds: $reserveDetailIds)
  }
`;

export const SUSPEND_RESERVATION = gql`
  mutation suspendReservation($input: SuspendReservationInput!) {
    suspendReservation(input: $input)
  }
`;
