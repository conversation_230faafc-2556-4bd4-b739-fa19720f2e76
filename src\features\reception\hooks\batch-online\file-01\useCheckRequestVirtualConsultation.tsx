import { useCallback, useRef, useState } from "react";

import { useE<PERSON>r<PERSON>and<PERSON> } from "@/hooks/useErrorHandler";
import { System } from "@/utils/socket-helper";
import { createXmlFileBatchOnlineCheckRequestVirtualConsultation } from "@/utils/socket-helper/onlineQualification";
import {
  CONFIRMATION_FILE_NAME,
  SystemHub,
  SystemScreenCode,
} from "@/constants/confirm-online";
import { usePostApiOnlineProcessXmloqSmuonq01resMutation } from "@/apis/gql/operations/__generated__/batch-online";
import { RenderIf } from "@/utils/common/render-if";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { useCheckConctionAgent } from "@/hooks/useCheckConctionAgent";

import { BatchOnlineErrorModal } from "../../../ui/modals/BatchOnlineCheck/BatchOnlineErrorModal";
import { BatchOnlineLoadingModal } from "../../../ui/modals/BatchOnlineCheck/BatchOnlineLoadingModal";

type ValidateState = {
  type: "success" | "error";
  content: {
    messageTitle: string;
    messageContent: string;
  };
};

export const useCheckRequestVirtualConsultation = (onClose: () => void) => {
  const [isLoading, setLoading] = useState<boolean>(false);
  const waitingOQController = useRef<AbortController>();
  const [openValidateModal, setOpenValidateModal] = useState<ValidateState>();
  const { handleError } = useErrorHandler();
  const { notification } = useGlobalNotification();
  const { checkConnection } = useCheckConctionAgent();

  const [postApiOnlineProcessXMLOQSmuonq01res] =
    usePostApiOnlineProcessXmloqSmuonq01resMutation({
      onError: (error) => handleError({ error }),
    });

  const handleResponseFile = async (
    xmlString: string,
    examinationDateFrom?: string,
    examinationDateTo?: string,
    consentDateFrom?: string,
    consentDateTo?: string,
  ) => {
    postApiOnlineProcessXMLOQSmuonq01res({
      variables: {
        payload: {
          batchConfirmationType: 4,
          consentFrom: Number(consentDateFrom || 0),
          consentTo: Number(consentDateTo || 0),
          sinYm: 0,
          xmlString,
          examinationFrom: Number(examinationDateFrom || 0),
          examinationTo: Number(examinationDateTo || 0),
          yoyakuDate: 0,
        },
      },
      onCompleted: (data) => {
        if (data.postApiOnlineProcessXMLOQSmuonq01res?.status === 0) {
          onClose();
        } else {
          notification.error({
            message: data.postApiOnlineProcessXMLOQSmuonq01res?.message,
          });
        }
      },
    });
  };

  const handleSendFileToAgentType4 = async (
    examinationDateFrom?: string,
    examinationDateTo?: string,
    consentDateFrom?: string,
    consentDateTo?: string,
  ) => {
    try {
      const isConnected = await checkConnection();
      if (!isConnected) {
        return;
      }

      setLoading(true);
      waitingOQController.current?.abort();
      waitingOQController.current = new AbortController();

      const { fileName, msg } =
        createXmlFileBatchOnlineCheckRequestVirtualConsultation(
          examinationDateFrom,
          examinationDateTo,
          consentDateFrom,
          consentDateTo,
        );

      const res = await onlineVisiting.createFile(
        msg.XmlMsg,
        fileName,
        { signal: waitingOQController.current?.signal },
        "CreateXmlFile",
      );

      if (!res || waitingOQController.current.signal.aborted) return;
      await onlineVisiting.moveFile({ files: [res.data.fileName] });
      if (
        res.data.fileName.includes(
          CONFIRMATION_FILE_NAME.REQUEST_VIRTUAL_CONSULTATION,
        )
      ) {
        const requestFileName = res.data.fileName.replace("err", "xml");
        await onlineVisiting.moveFile({ files: [requestFileName] });
        const match = res.data.content.match(/\[(.*?)\] (.*)/);
        if (match) {
          const processingResultCode = match[1];
          const errorMessage = match[2];
          setOpenValidateModal({
            type: "error",
            content: {
              messageTitle: "オンライン資格確認に失敗しました",
              messageContent: `処理結果コード: ${processingResultCode}\n${errorMessage}`,
            },
          });
          return;
        }
      }

      if (res.data?.content)
        handleResponseFile(
          res.data.content,
          examinationDateFrom,
          examinationDateTo,
          consentDateFrom,
          consentDateTo,
        );
    } catch (err) {
      if (err instanceof Error) {
        if (err.message === "NO_CONNECTION") {
          notification.error({
            message: "通信エラーが発生しました。",
            duration: 7,
          });
        } else {
          setOpenValidateModal({
            type: "error",
            content: {
              messageTitle: "オンライン資格確認に失敗しました",
              messageContent: "タイムアウト",
            },
          });
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const ValidateCancelRequestComponentFile1Type4 = useCallback(() => {
    return (
      <>
        <RenderIf condition={openValidateModal?.type === "error"}>
          <BatchOnlineErrorModal
            onClose={() => setOpenValidateModal(undefined)}
            content={openValidateModal?.content}
          />
        </RenderIf>
        <RenderIf condition={isLoading}>
          <BatchOnlineLoadingModal
            content={{
              title: "処理中",
              heading: "オンライン資格確認システムからの結果を待っています",
            }}
            onClose={() => {
              setLoading(false);
              waitingOQController.current?.abort();
            }}
          />
        </RenderIf>
      </>
    );
  }, [isLoading, openValidateModal?.content, openValidateModal?.type]);

  return {
    isLoading,
    handleSendFileToAgentType4,
    ValidateCancelRequestComponentFile1Type4,
  };
};

class OnlineVisiting extends System {
  constructor() {
    super(SystemHub.PatientInf, [{}], {
      screenCode: SystemScreenCode.PatientInfo,
    });
  }
}

const onlineVisiting = new OnlineVisiting();
