import { useState } from "react";

import { useGetSurveyBySecretQuery } from "@/apis/gql/operations/__generated__/web-servey";

const INVALID_PARAMETER_ERROR_MESSAGE = "Web問診票のURLが無効です。";

export const useWebSurvey = (querySecret: string | string[] | undefined) => {
  const secret = String(querySecret ?? "");
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const { data, loading } = useGetSurveyBySecretQuery({
    variables: {
      secret,
    },
    onError: (error) => {
      if (error.graphQLErrors[0]?.extensions?.code === "INVALID_PARAMETER") {
        setErrorMessage(INVALID_PARAMETER_ERROR_MESSAGE);
      } else {
        setErrorMessage("エラーが発生しました");
      }
    },
    skip: !secret,
  });

  return {
    secret,
    data: data?.getSurveyBySecret,
    loading,
    errorMessage,
  };
};
