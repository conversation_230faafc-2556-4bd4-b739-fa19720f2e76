import * as Types from "../../generated/types";

import { gql } from "@apollo/client";
import * as <PERSON> from "@apollo/client";
const defaultOptions = {} as const;
export type GetMeetingDetailQueryVariables = Types.Exact<{
  meetingId: Types.Scalars["Int"]["input"];
}>;

export type GetMeetingDetailQuery = {
  __typename?: "query_root";
  getMeetingDetail: {
    __typename?: "Meeting";
    meetingId: number;
    chimeMeetingId?: string;
    status: number;
    patient?: {
      __typename?: "Patient";
      patientID: number;
      patientName?: string;
      patientNameKana?: string;
      birthdate?: string;
      gender?: number;
      phoneNumber1?: string;
      phoneNumber2?: string;
    };
    reservation?: {
      __typename?: "Reservation";
      reserveId: number;
      reservationDetails?: Array<{
        __typename?: "ReservationDetail";
        status: number;
        reserveId: number;
        reserveDetailId: number;
        updatedAt: string;
        calendarTreatment: {
          __typename?: "CalendarTreatment";
          calendarTreatmentID: number;
          treatmentDepartment?: {
            __typename?: "TreatmentDepartment";
            treatmentDepartmentId: number;
            title: string;
          };
        };
        examTimeSlot: {
          __typename?: "ExamTimeSlot";
          examTimeSlotID: number;
          examStartDate: string;
          examEndDate: string;
          calendar: { __typename?: "Calendar"; calendarID: number };
        };
        patient?: {
          __typename?: "Patient";
          patientID: number;
          patientName?: string;
          patientNameKana?: string;
          birthdate?: string;
          gender?: number;
          phoneNumber1?: string;
          phoneNumber2?: string;
        };
      }>;
    };
    pharmacyReserve?: {
      __typename?: "PharmacyReserve";
      pharmacyReserveId: number;
      desiredDateStatus: number;
      desiredDate: Array<{
        __typename?: "DesiredDate";
        desiredDate: string;
        desiredType: number;
        pharmacyDesiredDateId: number;
      }>;
      customer: {
        __typename?: "PortalCustomer";
        name?: string;
        kanaName?: string;
        birthday?: string;
        gender?: number;
        telephone?: string;
      };
    };
  };
};

export type UpdateMeetingMutationVariables = Types.Exact<{
  input: Types.UpdateMeetingRequest;
}>;

export type UpdateMeetingMutation = {
  __typename?: "mutation_root";
  updateMeeting: { __typename?: "Meeting"; meetingId: number; status: number };
};

export type NotifyMeetingSubscriptionVariables = Types.Exact<{
  meetingId: Types.Scalars["Int"]["input"];
}>;

export type NotifyMeetingSubscription = {
  __typename?: "subscription_root";
  notifyMeeting: {
    __typename?: "Meeting";
    meetingId: number;
    chimeMeetingId?: string;
    status: number;
    patient?: {
      __typename?: "Patient";
      patientID: number;
      patientName?: string;
      patientNameKana?: string;
      birthdate?: string;
      gender?: number;
      phoneNumber1?: string;
      phoneNumber2?: string;
    };
    reservation?: {
      __typename?: "Reservation";
      reserveId: number;
      reservationDetails?: Array<{
        __typename?: "ReservationDetail";
        status: number;
        reserveId: number;
        reserveDetailId: number;
        updatedAt: string;
        calendarTreatment: {
          __typename?: "CalendarTreatment";
          calendarTreatmentID: number;
          treatmentDepartment?: {
            __typename?: "TreatmentDepartment";
            treatmentDepartmentId: number;
            title: string;
          };
        };
        examTimeSlot: {
          __typename?: "ExamTimeSlot";
          examTimeSlotID: number;
          examStartDate: string;
          examEndDate: string;
          calendar: { __typename?: "Calendar"; calendarID: number };
        };
        patient?: {
          __typename?: "Patient";
          patientID: number;
          patientName?: string;
          patientNameKana?: string;
          birthdate?: string;
          gender?: number;
          phoneNumber1?: string;
          phoneNumber2?: string;
        };
      }>;
    };
    pharmacyReserve?: {
      __typename?: "PharmacyReserve";
      pharmacyReserveId: number;
      desiredDate: Array<{
        __typename?: "DesiredDate";
        desiredDate: string;
        desiredType: number;
        pharmacyDesiredDateId: number;
      }>;
      customer: {
        __typename?: "PortalCustomer";
        name?: string;
        kanaName?: string;
        birthday?: string;
        gender?: number;
        telephone?: string;
      };
    };
  };
};

export type NotifyUpdatedMeetingToPatientMutationVariables = Types.Exact<{
  meetingId: Types.Scalars["Int"]["input"];
}>;

export type NotifyUpdatedMeetingToPatientMutation = {
  __typename?: "mutation_root";
  notifyUpdatedMeetingToPatient: { __typename?: "notifyRes"; success: number };
};

export const GetMeetingDetailDocument = gql`
  query getMeetingDetail($meetingId: Int!) {
    getMeetingDetail(meetingId: $meetingId) {
      meetingId
      chimeMeetingId
      status
      patient {
        patientID
        patientName
        patientNameKana
        birthdate
        gender
        phoneNumber1
        phoneNumber2
      }
      reservation {
        reserveId
        reservationDetails {
          status
          reserveId
          reserveDetailId
          updatedAt
          calendarTreatment {
            calendarTreatmentID
            treatmentDepartment {
              treatmentDepartmentId
              title
            }
          }
          examTimeSlot {
            examTimeSlotID
            examStartDate
            examEndDate
            calendar {
              calendarID
            }
          }
          patient {
            patientID
            patientName
            patientNameKana
            birthdate
            gender
            phoneNumber1
            phoneNumber2
          }
        }
      }
      pharmacyReserve {
        pharmacyReserveId
        desiredDateStatus
        desiredDate {
          desiredDate
          desiredType
          pharmacyDesiredDateId
        }
        customer {
          name
          kanaName
          birthday
          gender
          telephone
        }
      }
    }
  }
`;

/**
 * __useGetMeetingDetailQuery__
 *
 * To run a query within a React component, call `useGetMeetingDetailQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetMeetingDetailQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetMeetingDetailQuery({
 *   variables: {
 *      meetingId: // value for 'meetingId'
 *   },
 * });
 */
export function useGetMeetingDetailQuery(
  baseOptions: Apollo.QueryHookOptions<
    GetMeetingDetailQuery,
    GetMeetingDetailQueryVariables
  > &
    (
      | { variables: GetMeetingDetailQueryVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useQuery<GetMeetingDetailQuery, GetMeetingDetailQueryVariables>(
    GetMeetingDetailDocument,
    options,
  );
}
export function useGetMeetingDetailLazyQuery(
  baseOptions?: Apollo.LazyQueryHookOptions<
    GetMeetingDetailQuery,
    GetMeetingDetailQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useLazyQuery<
    GetMeetingDetailQuery,
    GetMeetingDetailQueryVariables
  >(GetMeetingDetailDocument, options);
}
export function useGetMeetingDetailSuspenseQuery(
  baseOptions?: Apollo.SuspenseQueryHookOptions<
    GetMeetingDetailQuery,
    GetMeetingDetailQueryVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSuspenseQuery<
    GetMeetingDetailQuery,
    GetMeetingDetailQueryVariables
  >(GetMeetingDetailDocument, options);
}
export type GetMeetingDetailQueryHookResult = ReturnType<
  typeof useGetMeetingDetailQuery
>;
export type GetMeetingDetailLazyQueryHookResult = ReturnType<
  typeof useGetMeetingDetailLazyQuery
>;
export type GetMeetingDetailSuspenseQueryHookResult = ReturnType<
  typeof useGetMeetingDetailSuspenseQuery
>;
export type GetMeetingDetailQueryResult = Apollo.QueryResult<
  GetMeetingDetailQuery,
  GetMeetingDetailQueryVariables
>;
export const UpdateMeetingDocument = gql`
  mutation updateMeeting($input: UpdateMeetingRequest!) {
    updateMeeting(input: $input) {
      meetingId
      status
    }
  }
`;
export type UpdateMeetingMutationFn = Apollo.MutationFunction<
  UpdateMeetingMutation,
  UpdateMeetingMutationVariables
>;

/**
 * __useUpdateMeetingMutation__
 *
 * To run a mutation, you first call `useUpdateMeetingMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useUpdateMeetingMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [updateMeetingMutation, { data, loading, error }] = useUpdateMeetingMutation({
 *   variables: {
 *      input: // value for 'input'
 *   },
 * });
 */
export function useUpdateMeetingMutation(
  baseOptions?: Apollo.MutationHookOptions<
    UpdateMeetingMutation,
    UpdateMeetingMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    UpdateMeetingMutation,
    UpdateMeetingMutationVariables
  >(UpdateMeetingDocument, options);
}
export type UpdateMeetingMutationHookResult = ReturnType<
  typeof useUpdateMeetingMutation
>;
export type UpdateMeetingMutationResult =
  Apollo.MutationResult<UpdateMeetingMutation>;
export type UpdateMeetingMutationOptions = Apollo.BaseMutationOptions<
  UpdateMeetingMutation,
  UpdateMeetingMutationVariables
>;
export const NotifyMeetingDocument = gql`
  subscription notifyMeeting($meetingId: Int!) {
    notifyMeeting(meetingId: $meetingId) {
      meetingId
      chimeMeetingId
      status
      patient {
        patientID
        patientName
        patientNameKana
        birthdate
        gender
        phoneNumber1
        phoneNumber2
      }
      reservation {
        reserveId
        reservationDetails {
          status
          reserveId
          reserveDetailId
          updatedAt
          calendarTreatment {
            calendarTreatmentID
            treatmentDepartment {
              treatmentDepartmentId
              title
            }
          }
          examTimeSlot {
            examTimeSlotID
            examStartDate
            examEndDate
            calendar {
              calendarID
            }
          }
          patient {
            patientID
            patientName
            patientNameKana
            birthdate
            gender
            phoneNumber1
            phoneNumber2
          }
        }
      }
      pharmacyReserve {
        pharmacyReserveId
        desiredDate {
          desiredDate
          desiredType
          pharmacyDesiredDateId
        }
        customer {
          name
          kanaName
          birthday
          gender
          telephone
        }
      }
    }
  }
`;

/**
 * __useNotifyMeetingSubscription__
 *
 * To run a query within a React component, call `useNotifyMeetingSubscription` and pass it any options that fit your needs.
 * When your component renders, `useNotifyMeetingSubscription` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the subscription, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useNotifyMeetingSubscription({
 *   variables: {
 *      meetingId: // value for 'meetingId'
 *   },
 * });
 */
export function useNotifyMeetingSubscription(
  baseOptions: Apollo.SubscriptionHookOptions<
    NotifyMeetingSubscription,
    NotifyMeetingSubscriptionVariables
  > &
    (
      | { variables: NotifyMeetingSubscriptionVariables; skip?: boolean }
      | { skip: boolean }
    ),
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useSubscription<
    NotifyMeetingSubscription,
    NotifyMeetingSubscriptionVariables
  >(NotifyMeetingDocument, options);
}
export type NotifyMeetingSubscriptionHookResult = ReturnType<
  typeof useNotifyMeetingSubscription
>;
export type NotifyMeetingSubscriptionResult =
  Apollo.SubscriptionResult<NotifyMeetingSubscription>;
export const NotifyUpdatedMeetingToPatientDocument = gql`
  mutation notifyUpdatedMeetingToPatient($meetingId: Int!) {
    notifyUpdatedMeetingToPatient(meetingId: $meetingId) {
      success
    }
  }
`;
export type NotifyUpdatedMeetingToPatientMutationFn = Apollo.MutationFunction<
  NotifyUpdatedMeetingToPatientMutation,
  NotifyUpdatedMeetingToPatientMutationVariables
>;

/**
 * __useNotifyUpdatedMeetingToPatientMutation__
 *
 * To run a mutation, you first call `useNotifyUpdatedMeetingToPatientMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useNotifyUpdatedMeetingToPatientMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [notifyUpdatedMeetingToPatientMutation, { data, loading, error }] = useNotifyUpdatedMeetingToPatientMutation({
 *   variables: {
 *      meetingId: // value for 'meetingId'
 *   },
 * });
 */
export function useNotifyUpdatedMeetingToPatientMutation(
  baseOptions?: Apollo.MutationHookOptions<
    NotifyUpdatedMeetingToPatientMutation,
    NotifyUpdatedMeetingToPatientMutationVariables
  >,
) {
  const options = { ...defaultOptions, ...baseOptions };
  return Apollo.useMutation<
    NotifyUpdatedMeetingToPatientMutation,
    NotifyUpdatedMeetingToPatientMutationVariables
  >(NotifyUpdatedMeetingToPatientDocument, options);
}
export type NotifyUpdatedMeetingToPatientMutationHookResult = ReturnType<
  typeof useNotifyUpdatedMeetingToPatientMutation
>;
export type NotifyUpdatedMeetingToPatientMutationResult =
  Apollo.MutationResult<NotifyUpdatedMeetingToPatientMutation>;
export type NotifyUpdatedMeetingToPatientMutationOptions =
  Apollo.BaseMutationOptions<
    NotifyUpdatedMeetingToPatientMutation,
    NotifyUpdatedMeetingToPatientMutationVariables
  >;
