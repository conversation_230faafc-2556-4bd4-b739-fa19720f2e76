import React, { use<PERSON>allback, useEffect, useMemo, useState } from "react";

import { Flex } from "antd";
import { Controller, useFormContext } from "react-hook-form";
import styled from "styled-components";

import { Checkbox } from "@/components/ui/Checkbox";
import { ErrorText } from "@/components/ui/ErrorText";
import { Button } from "@/components/ui/NewButton";
import { Pulldown } from "@/components/ui/Pulldown";
import { HOKEN_CONSTANTS, HOKEN_KBN_JIBAI_ROUSAI } from "@/constants/insurance";
import { useSession } from "@/hooks/useSession";
import { RenderIf } from "@/utils/common/render-if";
import { formatYYYYMMDDWithJapaneseEra } from "@/utils/datetime-format";
import { useReceptionConfirmOnlineCertification } from "@/hooks/add-patient/useReceptionConfirmOnlineCertification";
import { Emitter } from "@/utils/event-emitter";
import { EMITTER_EVENT } from "@/constants/event";
import { numberToDate } from "@/utils/add-patient";

import { usePatientContext } from "../Providers/PatientProvider";

import { getDateOfExpiry, getLastCheckedDate } from "./helper";

import type {
  DomainModelsInsuranceAiChatHokenInfDto,
  DomainModelsPatientInforPatientInforModel,
} from "@/apis/gql/generated/types";
import type { InsuranceDataState } from "@/types/insurance";
import type { AddReceptionForm } from "@/types/reception";
import type { FC } from "react";
import type { KohiRef } from "./KohiInfo";

type Props = {
  insurancesData: InsuranceDataState;
  patientDetail?: DomainModelsPatientInforPatientInforModel;
  resetKohiList?: (pattern: AddReceptionForm["hokenPattern"]) => void;
  setClickConfirmDate: () => void;
} & Partial<KohiRef>;

type InsuranceInfor = {
  hokenInfo: DomainModelsInsuranceAiChatHokenInfDto | undefined;
};

export const HokenInfo: FC<Props> = ({
  insurancesData,
  patientDetail,
  resetKohiList,
  setClickConfirmDate,
}) => {
  const {
    watch,
    setValue,
    control,
    trigger,
    formState: { isSubmitted },
    clearErrors,
  } = useFormContext<AddReceptionForm>();
  const {
    setSelectedInsurance,
    handleSetPatient,
    handleOpenModal,
    selectedSinDate,
    changeConfirmingType,
    handleSetListHokenCheck,
  } = usePatientContext();

  const [limitConsFlg, setLimitConsFlg] = useState<"0" | "1">("0");
  console.log('render hoken')
  const { session } = useSession();
  const { hokenPattern, hokenCheckList } = watch();
  const hokenId = hokenPattern.hokenId;

  const selectedHoken = watch("hokenPattern.hokenId");
  const hokenInfo = useMemo(() => {
    return insurancesData.listHoken.find((item) => item.hokenId === hokenId);
  }, [hokenId, insurancesData.listHoken]);

  const isJihi100Percent =
    hokenInfo?.houbetu === HOKEN_CONSTANTS.HOUBETU_JIHI_108;
  const isJihi = [
    HOKEN_CONSTANTS.HOUBETU_JIHI_108,
    HOKEN_CONSTANTS.HOUBETU_JIHI_109,
  ].includes(hokenInfo?.houbetu ?? "");

  const hokenOptions = [
    { value: 0, label: "選択してください" },
    ...(insurancesData?.listHoken?.map((item) => ({
      label: item.hokenSentaku,
      value: item.hokenId,
    })) ?? []),
  ];

  const lastCheckedDate = getLastCheckedDate(hokenInfo);
  const dateOfExpiry = getDateOfExpiry(hokenInfo);

  const onOpenConfirmHistory = useCallback(() => {
    const checkList = hokenCheckList?.find((item) => {
      return (
        item.hokenId === hokenInfo?.hokenId && item.seqNo === hokenInfo?.seqNo
      );
    });
    handleSetListHokenCheck([...(checkList?.confirmDateList ?? [])]);
  }, [
    handleSetListHokenCheck,
    hokenCheckList,
    hokenInfo?.hokenId,
    hokenInfo?.seqNo,
  ]);

  const handleEditInsurance = useCallback(() => {
    if (!hokenInfo?.hokenId) return;

    setSelectedInsurance({
      hokenId: hokenInfo.hokenId,
      seqNo: hokenInfo.seqNo ?? "0",
    });

    if (patientDetail?.ptId && patientDetail?.birthday) {
      handleSetPatient({
        patientID: Number(patientDetail.ptId),
        birthdate: patientDetail.birthday
          ? numberToDate(patientDetail.birthday).toISOString()
          : "",
        patientName: patientDetail.name,
        patientNameKana: patientDetail.kanaName,
        gender: patientDetail.sex,
        patientNumber: Number(patientDetail.ptNum),
      });
    }

    if (hokenInfo.seqNo) {
      onOpenConfirmHistory();
    }

    switch (hokenInfo.hokenKbn) {
      case 11:
      case 12:
      case 13:
        handleOpenModal("HANDLE_WORK_RELATED_INJURY");
        break;
      case 14:
        handleOpenModal("HANDLE_AUTOMOBILE_INSURANCE");
        break;

      default:
        handleOpenModal("HANDLE_INSURANCE_CARD");
        break;
    }
  }, [
    handleOpenModal,
    handleSetPatient,
    hokenInfo?.hokenId,
    hokenInfo?.hokenKbn,
    hokenInfo?.seqNo,
    onOpenConfirmHistory,
    patientDetail?.birthday,
    patientDetail?.kanaName,
    patientDetail?.name,
    patientDetail?.ptId,
    patientDetail?.sex,
    setSelectedInsurance,
  ]);

  const handleChangeHokenInf = useCallback(
    (value: number) => {
      const foundItem = insurancesData?.listHoken.find(
        (item) => item.hokenId === value,
      );

      if (
        (foundItem?.hokenKbn &&
          HOKEN_KBN_JIBAI_ROUSAI.includes(foundItem.hokenKbn)) ||
        foundItem?.houbetu === HOKEN_CONSTANTS.HOUBETU_JIHI_108
      ) {
        const newPattern = {
          hokenPid: 0,
          hokenId: value,
          kohi1Id: 0,
          kohi2Id: 0,
          kohi3Id: 0,
          kohi4Id: 0,
        };

        setValue("hokenPattern", newPattern);
        resetKohiList?.(newPattern);
      }

      setValue("hokenPattern.hokenId", value);
      if (isSubmitted) {
        trigger("hokenPattern.kohi1Id");
      }
    },
    [insurancesData?.listHoken, isSubmitted, resetKohiList, setValue, trigger],
  );

  const selectedHokenFromHokenCheckList = useMemo(() => {
    if (!hokenInfo) return;
    return hokenCheckList?.find((item) => {
      return (
        item.hokenId === hokenInfo?.hokenId &&
        item.seqNo === hokenInfo?.seqNo &&
        !item.isKohi
      );
    })?.confirmDateList?.[0];
  }, [hokenCheckList, hokenInfo]);

  const { handleConfirmOnline, ValidateOnlineConfirmationComponent } =
    useReceptionConfirmOnlineCertification({
      ptId: patientDetail?.ptId,
      onDone: (resultOfQualification, checkDate) => {
        Emitter.emit(EMITTER_EVENT.COMPARE_PATIENT_INFO, {
          resultOfQualification,
          checkDate,
        });
      },
      hasSelectPatient: true,
      handleSetSelectedResult: (resultOfQualification, checkDate) => {
        Emitter.emit(EMITTER_EVENT.COMPARE_PATIENT_INFO, {
          resultOfQualification: [resultOfQualification],
          checkDate,
        });
      },
    });

  const handleConfirmOnlineBtnClicked = useCallback(() => {
    changeConfirmingType("CONFIRMING_HOKEN_MY_INSURANCE");
    handleSetPatient({
      patientID: Number(patientDetail?.ptId ?? 0),
      birthdate: patientDetail?.birthday
        ? numberToDate(patientDetail.birthday).toISOString()
        : "",
      patientNumber: Number(patientDetail?.ptNum ?? 0),
    });
    setSelectedInsurance({
      hokenId: selectedHoken,
      seqNo: "0",
    });
    handleConfirmOnline({
      birthdate: String(patientDetail?.birthday ?? ""),
      limitConsFlg,
      selectedHokenInf: {
        hokenId,
        bango: hokenInfo?.bango ?? "",
        edaNo: hokenInfo?.edaNo ?? "",
        hokensyaNo: hokenInfo?.hokensyaNo ?? "",
        kigo: hokenInfo?.kigo ?? "",
      },
    });
  }, [
    changeConfirmingType,
    handleConfirmOnline,
    handleSetPatient,
    hokenId,
    hokenInfo?.bango,
    hokenInfo?.edaNo,
    hokenInfo?.hokensyaNo,
    hokenInfo?.kigo,
    limitConsFlg,
    patientDetail?.birthday,
    patientDetail?.ptId,
    selectedHoken,
    setSelectedInsurance,
  ]);

  useEffect(() => {
    if (selectedHoken) {
      clearErrors("hokenPattern.hokenId");
    }
  }, [clearErrors, selectedHoken]);

  return (
    <Section>
      <Flex align="center" justify="space-between">
        <SectionTitle>保険情報</SectionTitle>
        <Flex align="center" gap={10}>
          <NoOutlineButton
            varient="inline"
            onClick={() => {
              if (patientDetail?.ptId && patientDetail?.birthday) {
                handleSetPatient({
                  patientID: Number(patientDetail.ptId),
                  birthdate: numberToDate(patientDetail.birthday).toISOString(),
                  patientName: patientDetail.name,
                  patientNameKana: patientDetail.kanaName,
                  gender: patientDetail.sex,
                });
              }
              handleOpenModal("INSURANCE_SELECTION");
            }}
          >
            労災/自賠責保険情報を登録する
          </NoOutlineButton>
          <NoOutlineButton
            varient="inline"
            onClick={() => {
              if (patientDetail?.ptId && patientDetail?.birthday) {
                handleSetPatient({
                  patientID: Number(patientDetail.ptId),
                  birthdate: numberToDate(patientDetail.birthday).toISOString(),
                  patientName: patientDetail.name,
                  patientNameKana: patientDetail.kanaName,
                  gender: patientDetail.sex,
                  patientNumber: Number(patientDetail.ptNum),
                });
              }
              handleOpenModal("HANDLE_INSURANCE_CARD");
            }}
          >
            保険情報を登録する
          </NoOutlineButton>
        </Flex>
      </Flex>
      <Flex align="center" justify="space-between">
        <InputWrapper>
          <Controller
            control={control}
            name="hokenPattern.hokenId"
            render={({ field, fieldState: { error } }) => (
              <>
                <StyledSelect
                  placeholder="選択してください"
                  options={hokenOptions}
                  error={hokenInfo?.isExpirated}
                  width={280}
                  hasError={!!error}
                  {...field}
                  onChange={(selectedValue) => {
                    field.onChange(selectedValue); // trigger react-hook-form validation
                    handleChangeHokenInf(selectedValue); // your existing handler
                  }}
                />
                <StyledErrorText>{error?.message}</StyledErrorText>
              </>
            )}
            rules={{
              validate: (value, { hokenPattern }) => {
                const { kohi1Id, kohi2Id, kohi3Id, kohi4Id } = hokenPattern;

                const isSelectedKohi = kohi1Id || kohi2Id || kohi3Id || kohi4Id;
                if (!value && !isSelectedKohi) {
                  return "保険情報を入力してください。";
                }

                const isHokenNashi = insurancesData.listHoken.some(
                  (item) => item.hokenId === value && item.hokenNo === 100,
                );

                if (
                  isHokenNashi &&
                  !kohi1Id &&
                  !kohi2Id &&
                  !kohi3Id &&
                  !kohi4Id
                ) {
                  resetKohiList?.(hokenPattern, true);
                }

                return true;
              },
            }}
          />
        </InputWrapper>
        <Flex align="center">
          <EditInsuranceButton
            disabled={!hokenInfo || (hokenId === 1 && isJihi100Percent)}
            varient="custom"
            onClick={handleEditInsurance}
          >
            編集
          </EditInsuranceButton>
        </Flex>
      </Flex>
      <InsuranceSection>
        <InsuranceLayout align="flex-end" justify="space-between" gap={20}>
          <Flex vertical gap={10}>
            <RenderIf
              condition={
                [0, 1, 2].includes(hokenInfo?.hokenKbn as number) ||
                !hokenInfo?.hokenKbn
              }
            >
              <HokenInforComponent hokenInfo={hokenInfo} />
            </RenderIf>
            <RenderIf
              condition={[11, 12, 13].includes(hokenInfo?.hokenKbn as number)}
            >
              <RosaiInforComponent hokenInfo={hokenInfo} />
            </RenderIf>
            <RenderIf condition={hokenInfo?.hokenKbn === 14}>
              <JibaiInforComponent hokenInfo={hokenInfo} />
            </RenderIf>

            <InsuranceContentInfo>
              <InsuranceContentTitle>有効期限</InsuranceContentTitle>
              <InsuranceContent>{dateOfExpiry}</InsuranceContent>
            </InsuranceContentInfo>
            <InsuranceContentInfo>
              <InsuranceContentTitle>確認年月日</InsuranceContentTitle>
              <Flex gap={15}>
                <InsuranceContent minWidth={60}>
                  {`${selectedHokenFromHokenCheckList?.sinDate ? formatYYYYMMDDWithJapaneseEra(String(selectedHokenFromHokenCheckList?.sinDate)) : " "}`}
                </InsuranceContent>
                <InsuranceContentConfirmationBtn
                  varient="custom"
                  disabled={
                    !hokenInfo?.hokenId ||
                    (selectedHokenFromHokenCheckList?.sinDate ?? 0) >=
                    selectedSinDate ||
                    hokenInfo.houbetu === HOKEN_CONSTANTS.HOUBETU_NASHI ||
                    hokenInfo.houbetu === HOKEN_CONSTANTS.HOUBETU_JIHI_108
                  }
                  onClick={() => {
                    const newConfirmDate = hokenCheckList.map((item) => {
                      if (
                        item.hokenId === hokenInfo?.hokenId &&
                        item.seqNo === hokenInfo?.seqNo &&
                        !item.isKohi
                      ) {
                        return {
                          ...item,
                          confirmDateList: [
                            {
                              comment: "",
                              sinDate: selectedSinDate,
                              isDelete: false,
                              seqNo: "0",
                              checkName: session.staffInfo?.staffName,
                            },
                            ...(item.confirmDateList ?? []),
                          ],
                        };
                      }
                      return item;
                    });

                    setValue("hokenCheckList", newConfirmDate);
                    setClickConfirmDate();
                  }}
                >
                  確認
                </InsuranceContentConfirmationBtn>
                <NoOutlineButton
                  varient="inline"
                  disabled={!hokenInfo?.hokenId || isJihi100Percent}
                  onClick={() => {
                    if (!hokenInfo?.hokenId || !hokenInfo?.seqNo) {
                      return;
                    }
                    handleOpenModal("QUALIFICATION_CONFIRMATION_HISTORY");
                    onOpenConfirmHistory();
                  }}
                >
                  資格確認履歴
                </NoOutlineButton>
              </Flex>
            </InsuranceContentInfo>
          </Flex>

          <RenderIf
            condition={[0, 1, 2].includes(hokenInfo?.hokenKbn as number)}
          >
            <StyledFlex vertical align="flex-end" justify="space-between">
              <StyledCheckbox
                checked={limitConsFlg === "1"}
                onChange={(e) => {
                  const checked = e.target.checked;
                  setLimitConsFlg(checked ? "1" : "0");
                }}
                disabled={!hokenInfo?.hokenId || isJihi100Percent}
              >
                限度額適用認定証提供に同意する
              </StyledCheckbox>
              <Flex vertical align="flex-end">
                <InsuranceContentTitle>
                  {lastCheckedDate && `前回の確認日 ${lastCheckedDate}`}
                </InsuranceContentTitle>
                <OnlineEligibilityVerificationBtn
                  varient="secondary"
                  disabled={!hokenInfo?.hokenId || isJihi}
                  onClick={handleConfirmOnlineBtnClicked}
                >
                  オンライン資格確認
                </OnlineEligibilityVerificationBtn>
                <NoOutlineButton
                  varient="inline"
                  disabled={!hokenInfo?.hokenId}
                  onClick={() => {
                    if (patientDetail?.ptId && patientDetail?.birthday) {
                      handleSetPatient({
                        patientID: Number(patientDetail.ptId),
                        birthdate: numberToDate(
                          patientDetail.birthday,
                        ).toISOString(),
                        patientName: patientDetail.name,
                        patientNameKana: patientDetail.kanaName,
                        gender: patientDetail.sex,
                      });
                    }
                    handleOpenModal("ONLINE_QUALIFICATION_VERIFICATION");
                  }}
                >
                  オンライン資格確認履歴
                </NoOutlineButton>
              </Flex>
            </StyledFlex>
          </RenderIf>
        </InsuranceLayout>
      </InsuranceSection>
      <ValidateOnlineConfirmationComponent />
    </Section>
  );
};

const HokenInforComponent = ({ hokenInfo }: InsuranceInfor) => {
  return (
    <Flex gap={20}>
      <InsuranceContentInfo>
        <InsuranceContentTitle>保険者番号</InsuranceContentTitle>
        <InsuranceContent>{hokenInfo?.hokensyaNo ?? " "}</InsuranceContent>
      </InsuranceContentInfo>
      <InsuranceContentInfo>
        <InsuranceContentTitle>記号</InsuranceContentTitle>
        <InsuranceContent>{hokenInfo?.kigo ?? " "}</InsuranceContent>
      </InsuranceContentInfo>
      <InsuranceContentInfo>
        <InsuranceContentTitle>番号</InsuranceContentTitle>
        <InsuranceContent>{hokenInfo?.bango ?? " "}</InsuranceContent>
      </InsuranceContentInfo>
      <InsuranceContentInfo>
        <InsuranceContentTitle>枝番</InsuranceContentTitle>
        <InsuranceContent>{hokenInfo?.edaNo ?? " "}</InsuranceContent>
      </InsuranceContentInfo>
    </Flex>
  );
};

const RosaiInforComponent = ({ hokenInfo }: InsuranceInfor) => {
  return (
    <Flex gap={20}>
      <InsuranceContentInfo>
        <InsuranceContentTitle>
          {(() => {
            switch (hokenInfo?.hokenKbn) {
              case 11:
                return "労災保険番号";
              case 12:
                return "年金証書番号";
              default:
                return "健康管理低調番号";
            }
          })()}
        </InsuranceContentTitle>
        <InsuranceContent>{hokenInfo?.rousaiKofuNo ?? " "}</InsuranceContent>
      </InsuranceContentInfo>
    </Flex>
  );
};

const JibaiInforComponent = ({ hokenInfo }: InsuranceInfor) => {
  return (
    <Flex gap={20}>
      <InsuranceContentInfo>
        <InsuranceContentTitle>自賠責保険会社名</InsuranceContentTitle>
        <InsuranceContent>{hokenInfo?.jibaiHokenName ?? " "}</InsuranceContent>
      </InsuranceContentInfo>
    </Flex>
  );
};

const StyledFlex = styled(Flex)`
  height: 120px;
`;

const SectionTitle = styled.p`
  font-size: 16px;
  line-height: 16px;
  font-weight: bold;
  margin-bottom: 16px;
`;

const Section = styled.div`
  padding: 20px 24px;
  border-bottom: 1px solid #e2e3e5;

  &:last-of-type {
    border-bottom: none;
  }
`;

const NoOutlineButton = styled(Button)`
  font-size: 14px;
  line-height: 14px;
  font-weight: normal;
  height: auto;
  width: auto;
`;

const InsuranceSection = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  border-radius: 6px;
  border: solid 1px #e2e3e5;
  background-color: #f1f4f7;
  margin: 8px 0 0;
`;

const EditInsuranceButton = styled(Button)`
  padding: 11px 26px;
  width: 80px;
  border-radius: 6px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
`;

const InsuranceContentTitle = styled.span`
  font-size: 12px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  color: #6a757d;
`;

const InsuranceContent = styled("span").withConfig({
  shouldForwardProp: (props) => props !== "minWidth",
}) <{ minWidth?: number }>`
  margin: 6px 0 0;
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  color: #243544;
  min-width: ${({ minWidth }) => (minWidth ? `${minWidth}px` : "auto")};
`;

const InsuranceContentInfo = styled.div`
  display: flex;
  flex-direction: column;
  height: 32px;
  justify-content: space-between;
`;

const InsuranceContentConfirmationBtn = styled(Button)`
  width: 60px;
  height: 28px;
  padding: 7px 16px;
  border-radius: 6px;
  border: solid 1px #e2e3e5;
  background-color: #fbfcfe;
`;

const OnlineEligibilityVerificationBtn = styled(Button)`
  width: auto;
  height: 28px;
  margin: 4px 0 8px;
  border-radius: 18px;
  background-color: #43c3d5;
`;

const InsuranceLayout = styled(Flex)`
  width: 100%;
`;

const StyledSelect = styled(Pulldown).withConfig({
  shouldForwardProp: (prop) => prop !== "error",
})<{
  width: string | number;
  error?: boolean;
}>(({ width, error }) => ({
  width: width === "full" ? "100%" : `${width}px`,

  ...(error && {
    ".ant-select-selection-item": {
      color: "#e74c3c",
    },
  }),
}));

const InputWrapper = styled.div`
  width: 100%;
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: ${({ children }) => (children ? "8px" : 0)};
`;

const StyledCheckbox = styled(Checkbox)`
  display: flex;
  flex-direction: row-reverse;

  .ant-checkbox + span {
    padding-inline-start: 5px;
    padding-inline-end: 5px;
  }
`;
