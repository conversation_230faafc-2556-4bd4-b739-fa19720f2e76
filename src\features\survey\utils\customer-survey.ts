import type { Question } from "survey-react";

//
// クリニックマップの次のコードをベースに作成
// https://github.com/bizleap-healthcare/clinic-reservation/blob/main/user-client/src/util/customer-survey.ts
//

type CustomBaseAnswer = {
  questionName: string;
  questionTitle: string;
  questionType: string;
  answerDisplayValue: string;
};

type CustomTextAnswer = CustomBaseAnswer & {
  questionType: "text";
  answerValue: string;
};

type CustomCommentAnswer = CustomBaseAnswer & {
  questionType: "comment";
  answerValue: string;
};

type CustomRadioGroupAnswer = CustomBaseAnswer & {
  questionType: "radiogroup";
  answerValue: string;
};

type CustomCheckboxAnswer = CustomBaseAnswer & {
  questionType: "checkbox";
  answerValue: string[];
};

type CustomDropdownAnswer = CustomBaseAnswer & {
  questionType: "dropdown";
  answerValue: string;
};

type CustomFileAnswer = CustomBaseAnswer & {
  questionType: "file";
  answerValue: File;
};

type CustomUploadedFileAnswer = CustomBaseAnswer & {
  questionType: "file";
  answerValue: { fileName?: string; fileType?: string; s3Key?: string };
};

type CustomMultipletextAnswer = CustomBaseAnswer & {
  questionType: "multipletext";
  answerValue?: Record<string, string>;
};

type CustomExpressionAnswer = CustomBaseAnswer & {
  questionType: "expression";
  answerValue: number | string | boolean;
};

export type CustomAnswerType =
  | CustomTextAnswer
  | CustomCommentAnswer
  | CustomRadioGroupAnswer
  | CustomCheckboxAnswer
  | CustomDropdownAnswer
  | CustomMultipletextAnswer
  | CustomFileAnswer
  | CustomExpressionAnswer;

export type CustomSurveyAnswer = Exclude<
  CustomAnswerType | CustomUploadedFileAnswer,
  CustomFileAnswer
>;

export const transformSurveyQuestion = (
  questions: Question[],
  tempFileStorage: Record<string, File>,
): CustomAnswerType[] => {
  try {
    return questions
      .map((question) => {
        const questionType = question.getType();
        const baseAnswer = {
          questionName: question.name,
          questionTitle: question.title,
          questionType,
          answerDisplayValue: question.displayValue,
        };

        switch (questionType) {
          case "text":
            return {
              ...baseAnswer,
              answerValue: question.value || "",
            } as CustomTextAnswer;

          case "comment":
            return {
              ...baseAnswer,
              answerValue: question.value || "",
            } as CustomCommentAnswer;

          case "radiogroup":
            return {
              ...baseAnswer,
              answerValue: question.value || "",
            } as CustomRadioGroupAnswer;

          case "checkbox":
            return {
              ...baseAnswer,
              answerValue: question.value || [],
            } as CustomCheckboxAnswer;

          case "dropdown":
            return {
              ...baseAnswer,
              answerValue: question.value || "",
            } as CustomDropdownAnswer;

          case "file":
            return {
              ...baseAnswer,
              answerDisplayValue: question.displayValue[0]?.name || "",
              answerValue: tempFileStorage[question.name] || {},
            } as CustomFileAnswer;

          case "multipletext":
            return {
              ...baseAnswer,
              answerDisplayValue: "",
              answerValue: question.value ? question.displayValue : undefined,
            } as CustomMultipletextAnswer;

          case "expression":
            return {
              ...baseAnswer,
              answerDisplayValue: question.displayValue,
              answerValue: question.value,
            } as CustomExpressionAnswer;

          default:
            return null;
        }
      })
      .filter((answer): answer is CustomAnswerType => answer !== null);
  } catch (e) {
    // logger.log(e);
    return [];
  }
};

export const isCustomFileAnswer = (
  question: CustomAnswerType,
): question is CustomFileAnswer => {
  return question.questionType === "file";
};
