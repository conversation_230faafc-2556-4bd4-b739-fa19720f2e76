import { useMemo, useState } from "react";

import { get, set } from "lodash";
import { useFormContext } from "react-hook-form";

import { useOrdInfValidateInputItemMutation } from "@/apis/gql/operations/__generated__/selective-comment";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";

import {
  KarteErrorCode,
  MAP_ERROR_STATUS_TO_MESSAGE,
} from "../../constants/error-validate-medical";
import { useGetOrderInfoContext } from "../useGetOrderInfoContext";

import { createValidationInput } from "./utils";
import { useKarteValidateModal } from "./useKarteValidateModal";
import { useCheckSanteiMax } from "./check/useCheckSanteiMax";
import { useAddAutoItem } from "./check/useAddAutoItem";
import { useCheckExpired } from "./check/useCheckExpired";
import { useCheckChangeName } from "./check/useCheckChangeName";
import { useCheckDrug } from "./check/useCheckDrug";

import type { IValidationData, KarteError } from "./types";
import type { KarteFormData } from "../../types/karte-order";

type Props = {
  setSelectedRpId: (value: string | undefined) => void;
};

export const useKarteValidate = ({ setSelectedRpId }: Props) => {
  const { sinDate } = useGetOrderInfoContext();
  const [errors, setErrors] = useState<KarteError>({
    orderRps: [],
  });

  const { notification } = useGlobalNotification();
  const { getValues } = useFormContext<KarteFormData>();

  const {
    validateCloseModal,
    validateModalProps,
    validateOpenModal,
    isOpenSanteiMaxModal,
    isOpenAutoAddItemModal,
    isOpenExpiredModal,
    isOpenChangeNameModal,
    isOpenChangeSyosaishinModal,
  } = useKarteValidateModal();

  const { checkSanteiMaxFromKarte, checkSanteiMaxFromDo } = useCheckSanteiMax({
    validateOpenModal,
    validateCloseModal,
  });

  const { checkDrug, drugErrorInfo } = useCheckDrug();

  const { getAutoAddItem } = useAddAutoItem({
    validateOpenModal,
    validateCloseModal,
  });

  const { checkExpiredFromHistory } = useCheckExpired({
    validateOpenModal,
    validateCloseModal,
  });

  const { checkChangeName } = useCheckChangeName({
    validateOpenModal,
    validateCloseModal,
  });

  const [ordInfValidateInputItemMutation] =
    useOrdInfValidateInputItemMutation();

  const setKarteError = (validationData: Partial<IValidationData>) => {
    const { validationOdrInfs, validationRaiinInf } = validationData;

    const orderRpErros = (validationOdrInfs ?? []).reduce(
      (acc, item) => {
        const rpIndex = Number(item.orderInfPosition);
        const itemIndex = Number(item.orderInfDetailPosition);

        const rp = acc[rpIndex] || { orderItems: [] };

        switch (item.status) {
          // todo: handle for the hoken error
          case KarteErrorCode.InvalidHokenPId:
          case KarteErrorCode.KohiIdNoExist:
          case KarteErrorCode.HokenPidNoExist: {
            rp.invalidHoken = item.hokenName;
            rp.invalidKohis = item.kohiNames;
            break;
          }
          case KarteErrorCode.InvalidHasUsageButNotDrug:
            rp.missDrug =
              MAP_ERROR_STATUS_TO_MESSAGE[
                KarteErrorCode.InvalidHasUsageButNotDrug
              ];
            break;
          case KarteErrorCode.InvalidHasUsageButNotInjectionOrDrug:
          case KarteErrorCode.InvalidMissDrug:
            rp.missInjection =
              MAP_ERROR_STATUS_TO_MESSAGE[
                KarteErrorCode.InvalidHasUsageButNotInjectionOrDrug
              ];
            break;
          case KarteErrorCode.InvalidHasDrugButNotUsage:
            rp.missDrugUsage = true;
            break;
          case KarteErrorCode.InvalidHasInjectionButNotUsage:
            rp.missInjectionUsage =
              MAP_ERROR_STATUS_TO_MESSAGE[
                KarteErrorCode.InvalidHasInjectionButNotUsage
              ];
            break;
          // case KarteErrorCode.InvalidSelectiveComment:
          //   rp.missSelectedComment = {
          //     message:
          //       MAP_ERROR_STATUS_TO_MESSAGE[
          //         KarteErrorCode.InvalidSelectiveComment
          //       ]!,
          //     rp: {
          //       [rpIndex]: [itemIndex],
          //     },
          //   };
          //   break;
          case KarteErrorCode.InvalidSumBunkatuDifferentSuryo:
            set(rp, `orderItems.${itemIndex}.bunkatuError`, true);
            break;
          case KarteErrorCode.InvalidSuryoAndYohoKbnWhenDisplayedUnitNotNull:
          case KarteErrorCode.InvalidSuryoBunkatuWhenIsCon_TouyakuOrSiBunkatu:
          case KarteErrorCode.InvalidSuryoPrice:
          case KarteErrorCode.InvalidSuryoOfReffill:
          case KarteErrorCode.InvalidSuryo:
          case KarteErrorCode.NoFillSuryo:
            set(rp, `orderItems.${itemIndex}.suryoError`, true);
            break;
        }

        acc[rpIndex] = rp;
        return acc;
      },
      {} as KarteError["orderRps"],
    );

    setErrors({
      orderRps: orderRpErros,
      invalidHoken: validationRaiinInf?.hokenName,
      invalidKohis: validationRaiinInf?.kohiNames,
    });
  };

  const getRpErrors = (rpIndex: number) => {
    return get(errors, `orderRps.${rpIndex}`) ?? {};
  };

  const getItemErrors = (rpIndex: number, itemIndex: number) => {
    return get(errors, `orderRps.${rpIndex}.orderItems.${itemIndex}`);
  };

  const clearErrors = () => {
    setErrors({
      orderRps: [],
    });
  };

  const highLightHokenNameError = useMemo(() => {
    const hokenErrors = Object.values(errors.orderRps)
      .map((rp) => rp.invalidHoken)
      .concat(errors.invalidHoken)
      .filter((item) => !!item);

    const kohiErrors = Object.values(errors.orderRps)
      .flatMap((rp) => rp.invalidKohis)
      .concat(errors.invalidKohis)
      .filter((item) => !!item);

    const firstHokenError = hokenErrors[0];
    const firstKohiError = kohiErrors[0];

    let message = undefined;
    if (firstHokenError) {
      message = firstHokenError.hokenName;
    } else if (firstKohiError) {
      message = firstKohiError.hokenName;
    }

    return message
      ? `${message} は、削除されているため、診療記録を保存できません。`
      : undefined;
  }, [errors]);

  const checkItem = async (
    opts = {
      notification: false,
      focusFirstErrorInput: false,
    },
  ) => {
    const value = getValues() as KarteFormData;
    const odrInfs = value.orderRps?.map(createValidationInput);

    const data = await ordInfValidateInputItemMutation({
      variables: {
        emrCloudApiRequestsOrdInfsValidationInputItemRequestInput: {
          sinDate,
          odrInfs,
        },
      },
    });

    const validationData =
      data.data?.postApiOrdInfValidateInputItem?.data ?? {};

    const validationOdrInfs = validationData.validationOdrInfs;

    const hasError = !!validationOdrInfs?.length;

    if (hasError) {
      if (
        opts.focusFirstErrorInput &&
        value.orderRps &&
        validationOdrInfs &&
        validationOdrInfs.length > 0
      ) {
        const rpErrorFirst =
          value.orderRps[
            parseInt(validationOdrInfs[0]?.orderInfPosition as string)
          ];
        setSelectedRpId(rpErrorFirst?.customId);
        const rpElement = document.getElementById(
          `rp-${rpErrorFirst?.customId}`,
        );
        if (rpElement) {
          rpElement.scrollIntoView({ behavior: "smooth", block: "center" });
        }
      }

      const isOnlyHokenDeleted = validationOdrInfs?.every(
        (item) => item.status === KarteErrorCode.KohiIdNoExist,
      );

      // if hoken is deleted, it won't show toast msg
      if (opts.notification && !isOnlyHokenDeleted) {
        notification.error({ message: "カルテを保存できませんでした。" });
      }

      setKarteError(validationData);
    }

    return !hasError;
  };

  return {
    errors,
    clearErrors,

    highLightHokenNameError,
    setKarteError,
    getRpErrors,
    getItemErrors,
    checkItem,
    checkExpiredFromHistory,
    checkSanteiMaxFromKarte,
    checkSanteiMaxFromDo,
    getAutoAddItem,
    checkChangeName,

    checkDrug,
    drugErrorInfo,

    // modal properties
    validateModalProps,
    validateCloseModal,
    validateOpenModal,
    isOpenSanteiMaxModal,
    isOpenAutoAddItemModal,
    isOpenExpiredModal,
    isOpenChangeNameModal,
    isOpenChangeSyosaishinModal,
  };
};
