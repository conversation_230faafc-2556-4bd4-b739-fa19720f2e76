import React, { useMemo } from "react";

import { useRouter } from "next/router";
import styled from "styled-components";

import { Meta } from "@/components/functional/Meta";
import { WebSocketProvider } from "@/hooks/chat/useWebSocket";
import { WebSocketSubscriber } from "@/hooks/chat/useWebSocketSubscriber";
import { MeetingStateProvider } from "@/hooks/useMeeting";
import { useSession } from "@/hooks/useSession";
import { isPathIncluded, isWebSocketException } from "@/utils/common-helper";
import { PatientProvider } from "@/components/common/Patient/AddPatient/Providers/PatientProvider";
import { ConnectionAgentProvider } from "@/hooks/useCheckConctionAgent";

import { Header } from "./Header";

import type { ReactNode } from "react";

type ClinicLayoutProps = {
  children: ReactNode;
};

const headerVisiblePaths = [
  "/calendar",
  "/chat",
  "/chat/*",
  "/karte/*",
  "/reception",
  "/setting",
  "/setting/*",
  "/task",
  "/task/*",
  "/login/initial",
  "/ai-assist",
  "request-exam",
  "/survey-answers",
];

// headerVisiblePathsにマッチする条件内で、例外的に非表示にするパス
const headerInvisiblePaths = ["/setting/survey/[0-9]+/?$"];

const webSocketExceptionPaths = [
  "/",
  "/login",
  "/login/*",
  "/logout",
  "/logout/*",
];

const StyledMain = styled.main<{ $hasHeader: boolean }>`
  min-width: var(--application-min-width);
  height: ${({ $hasHeader }) =>
    $hasHeader ? "calc(100vh - var(--global-header-height))" : `auto`};
  overflow-y: auto;
`;

export const ClinicLayout: React.FC<ClinicLayoutProps> = ({ children }) => {
  const router = useRouter();
  const {
    session: { isLoggedIn },
  } = useSession();

  // URLが非表示条件に該当するかチェック
  const matchesInvisiblePath = isPathIncluded(
    router.asPath,
    headerInvisiblePaths,
  );
  // URLが表示条件に該当するかチェック
  const matchesVisiblePath = isPathIncluded(router.asPath, headerVisiblePaths);
  // URLが例外条件に該当するかチェック
  const isWebSocketExceptionPath = isWebSocketException(
    router.asPath,
    webSocketExceptionPaths,
  );

  // WebSocketを有効にするかチェック
  const isEnabled = isLoggedIn && !isWebSocketExceptionPath;

  // 非表示条件に該当しない、かつ表示条件に該当する場合にヘッダーを表示
  const showHeader = isLoggedIn && !matchesInvisiblePath && matchesVisiblePath;

  const isMeetingRoute = useMemo(
    () => router.asPath.startsWith("/meet"),
    [router.asPath],
  );

  const Content = useMemo(() => {
    if (isMeetingRoute) {
      return <MeetingStateProvider>{children}</MeetingStateProvider>;
    }

    return children;
  }, [isMeetingRoute, children]);

  return (
    <>
      <Meta title="AIチャート" description="" />
      <WebSocketProvider isEnabled={isEnabled}>
        <WebSocketSubscriber isEnabled={isEnabled}>
          <ConnectionAgentProvider>
            <PatientProvider>
              {showHeader && <Header />}
              <StyledMain $hasHeader={showHeader}>{Content}</StyledMain>
            </PatientProvider>
          </ConnectionAgentProvider>
        </WebSocketSubscriber>
      </WebSocketProvider>
    </>
  );
};
