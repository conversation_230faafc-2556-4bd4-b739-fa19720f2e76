import { useData } from "@/features/setting-miscellaneous/hooks/useDataProvider";
import { NumberInput } from "@/components/ui/TextInput";

import { useTableContent } from "../../../hooks/useTableContentProvider";
import { MiscellaneousTable } from "../../common/MiscellaneousTable";
import { ToggleContentButton } from "../../common/ToggleContentButton";
import { DateTimeParamInput } from "../../common/DateTimeParamInput";

import { NationalInsurancePicker } from "./NationalInsurancePicker";
import { PrescriptionPicker } from "./PrescriptionPicker";
import { SocialInsurancePicker } from "./SocialInsurancePicker";
import { TaxRoundingPicker } from "./TaxRoundingPicker";
import { VehicleClassificationPicker } from "./VehicleClassificationPicker";
import { AccidentComputerizationReceiptPicker } from "./AccidentComputerizationReceiptPicker";
import { AftercareComputerizationReceiptPicker } from "./AftercareComputerizationReceiptPicker";

export const MedicalSettingTable = () => {
  const { tableContent, handleToggleDisplay } = useTableContent();
  const IGNORE_SPECIAL_NUMBER_CHARACTERS = ["e", "E", "+", "-"];

  const {
    state: {
      prescription,
      taxRounding,
      socialInsurance,
      nationalInsurance,
      vehicleClassification,
      accidentCompensationRate,
      accidentComputerizationReceipt,
      aftercareComputerizationReceipt,
    },
    handleChangeValue,
    handleChangeParam,
  } = useData();

  const columns = [
    {
      title: "基本設定",
      width: "50%",
      key: "basicSetting",
      dataIndex: "basicSetting",
    },
    {
      key: "value",
      dataIndex: "value",
    },
    {
      title: (
        <ToggleContentButton
          isShowing={tableContent.showMedicalSetting}
          onClick={() => handleToggleDisplay("MEDICAL_SETTING")}
        />
      ),
      width: "20%",
      key: "param",
      dataIndex: "param",
    },
  ];

  const dataSource = [
    prescription.isVisible && {
      key: "prescription",
      basicSetting: "処方箋設定",
      value: (
        <PrescriptionPicker
          value={prescription.val}
          onChange={(value) => handleChangeValue("prescription", value)}
        />
      ),
    },
    taxRounding.isVisible && {
      key: "taxRounding",
      basicSetting: "消費税端数処理",
      value: (
        <TaxRoundingPicker
          value={taxRounding.val}
          onChange={(value) => handleChangeValue("taxRounding", value)}
        />
      ),
    },
    socialInsurance.isVisible && {
      key: "socialInsurance",
      basicSetting: "検査まるめ分点（社保）",
      value: (
        <SocialInsurancePicker
          value={socialInsurance.val}
          onChange={(value) => handleChangeValue("socialInsurance", value)}
        />
      ),
    },
    nationalInsurance.isVisible && {
      key: "nationalInsurance",
      basicSetting: "検査まるめ分点（国保）",
      value: (
        <NationalInsurancePicker
          value={nationalInsurance.val}
          onChange={(value) => handleChangeValue("nationalInsurance", value)}
        />
      ),
    },
    vehicleClassification.isVisible && {
      key: "vehicleClassification",
      basicSetting: "自賠責区分",
      value: (
        <VehicleClassificationPicker
          value={vehicleClassification.val}
          onChange={(value) =>
            handleChangeValue("vehicleClassification", value)
          }
        />
      ),
    },
    accidentCompensationRate.isVisible && {
      key: "accidentCompensationRate",
      basicSetting: "労災準拠加算率",
      value: (
        <NumberInput
          value={accidentCompensationRate.val}
          onChange={(event) =>
            handleChangeValue("accidentCompensationRate", event.target.value)
          }
          min={0}
          onKeyDown={(e) => {
            if (IGNORE_SPECIAL_NUMBER_CHARACTERS.includes(e.key)) {
              e.preventDefault();
            }
          }}
        />
      ),
    },
    accidentComputerizationReceipt.isVisible && {
      key: "accidentComputerizationReceipt",
      basicSetting: "労災レセプト電算",
      value: (
        <AccidentComputerizationReceiptPicker
          value={accidentComputerizationReceipt.val}
          onChange={(value) =>
            handleChangeValue("accidentComputerizationReceipt", value)
          }
        />
      ),
      param: (
        <DateTimeParamInput
          value={accidentComputerizationReceipt.param || ""}
          handleChangeValue={(value) =>
            handleChangeParam("accidentComputerizationReceipt", value)
          }
        />
      ),
    },
    aftercareComputerizationReceipt.isVisible && {
      key: "aftercareComputerizationReceipt",
      basicSetting: "アフターケアレセプト電算",
      value: (
        <AftercareComputerizationReceiptPicker
          value={aftercareComputerizationReceipt.val}
          onChange={(value) =>
            handleChangeValue("aftercareComputerizationReceipt", value)
          }
        />
      ),
      param: (
        <DateTimeParamInput
          value={aftercareComputerizationReceipt.param || ""}
          handleChangeValue={(value) =>
            handleChangeParam("aftercareComputerizationReceipt", value)
          }
        />
      ),
    },
  ].filter(Boolean);
  return dataSource.length ? (
    <MiscellaneousTable
      columns={columns}
      dataSource={tableContent.showMedicalSetting ? dataSource : undefined}
      showEmptyText={false}
    />
  ) : null;
};
