# オンボーディング

## 環境構築

### 前提条件

- ✅ GithubのSSH keys設定が環境していること
- ✅ node v22.16.0がローカル環境にインストールされていること
- ✅ yarn v1.22.22がローカル環境にインストールされていること

### ローカル環境で起動

1. `denkaru-user-web`をclone

```bash
<NAME_EMAIL>:bizleap-healthcare/denkaru-user-web.git
```

2. node_modules（依存モジュール）をインストール

```bash
yarn install
```

3. `.env.local`ファイルを生成

```
yarn env
```

4. 起動

```bash
yarn dev
```

### その他

#### Static Export環境で起動

- Dynamic Routingはサポートしていません（TODO）

```bash
yarn dev:static
```

#### Linter

```bash
yarn lint
```

#### Formatter

```bash
yarn format
```

#### 型チェック

```bash
yarn type-check
```

#### GraphQL Schema Type-Generation（Custom Hooks Generation）

see: https://the-guild.dev/graphql/codegen  
see: https://the-guild.dev/graphql/codegen/plugins/typescript/typescript-operations  
see: https://the-guild.dev/graphql/codegen/plugins/typescript/typescript-react-apollo

1. `denkaru-server/hasura/metadata.json`を最新の状態に更新
2. 下記コマンドを実行
3. `src/apis/gql/operations/*.ts`にスキーマを定義
4. 下記コマンドを再実行

```bash
yarn codegen
```

#### Domain Error（Denkaru Error） Codes-Generation

1. `denkaru-user-web`と同じディレクトリに`denkaru-code`リポジトリをclone
2. `denkaru-code`の対象ブランチを最新の状態に更新
3. 下記コマンドを実行

```bash
yarn denkaru-codegen
```

#### Transform SVGs into React components

※運用的にあまりメリットを感じないので、そのうち`deprecated`にするかも  
see: https://react-svgr.com/

```bash
yarn icon "../Desktop/icon-test.svg"
```

#### ローカル環境での電子証明書認証

対象のシステムごとに異なりますので、CustomHeaderを変更してください

- src/apis/gql/apollo-client.ts
- src/apis/rest/instance/utils.ts

##### クリニックシステム

```ts
{
  customHeaders["denkaru-cn-header"] = "gmohealthtech.com-1-2222";
}
```

##### オンライン服薬指導システム

```ts
{
  customHeaders["denkaru-cn-header"] = "gmohealthtech.com-5-2222";
}
```

#### エディター

- VSCodeの使用を推奨しています（または、拡張機能（`.vscode/extensions.json`）サポートがあるエディター）

#### Node.js / Yarnのバージョン管理ツール

- Nodeのバージョン管理については好みのツールを使用していただいて構いません
