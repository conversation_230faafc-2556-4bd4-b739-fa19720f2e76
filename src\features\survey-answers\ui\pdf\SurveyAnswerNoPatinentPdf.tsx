import React from "react";

import {
  Document,
  Font,
  Image,
  Page,
  StyleSheet,
  Text,
  View,
} from "@react-pdf/renderer";

import type { SurveyRes } from "@/apis/gql/generated/types";

// 日本語フォントを登録
Font.register({
  family: "Noto Sans JP",
  src: "/assets/fonts/NotoSansJP-Regular.otf",
});

interface SurveyAnswerNoPatientPdfProps {
  qrImageUrl: string;
  selectedSurvey?: SurveyRes;
}

const pdfStyles = StyleSheet.create({
  page: {
    flexDirection: "column",
    backgroundColor: "#fff",
    fontFamily: "Noto Sans JP",
  },
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    width: "100%",
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
    margin: 12,
    textAlign: "center",
  },
  guidance: {
    fontSize: 14,
    margin: 8,
    textAlign: "center",
  },
  qr: {
    width: 128,
    height: 128,
    margin: 12,
  },
  surveyName: {
    fontSize: 14,
    margin: 8,
    textAlign: "center",
  },
});

const SURVEY_TITLE = "問診票に回答してください";
const SURVEY_GUIDANCE =
  "以下のQRコードをスマートフォンで読み取り、\n問診票フォームから回答をお願いします";

export const SurveyAnswerNoPatientPdf: React.FC<
  SurveyAnswerNoPatientPdfProps
> = ({ qrImageUrl, selectedSurvey }) => (
  <Document>
    <Page size="A4" style={pdfStyles.page}>
      <View style={pdfStyles.container}>
        <Text style={pdfStyles.title}>{SURVEY_TITLE}</Text>
        <Text style={pdfStyles.guidance}>{SURVEY_GUIDANCE}</Text>
        <Image src={qrImageUrl} style={pdfStyles.qr} />
        <Text style={pdfStyles.surveyName}>{selectedSurvey?.name || ""}</Text>
      </View>
    </Page>
  </Document>
);
