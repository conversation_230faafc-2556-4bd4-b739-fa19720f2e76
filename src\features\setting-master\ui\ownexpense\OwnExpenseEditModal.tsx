import { useEffect, useMemo, useState } from "react";

import dayjs from "dayjs";
import { useForm } from "react-hook-form";
import styled from "styled-components";

import { SvgIconDelete } from "@/components/ui/Icon/IconDelete";
import { SvgIconPlus } from "@/components/ui/Icon/IconPlus";
import { IconButton } from "@/components/ui/IconButton";
import { InputLabel } from "@/components/ui/InputLabel";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { Pulldown } from "@/components/ui/Pulldown";
import { TextInput } from "@/components/ui/TextInput";
import { ItemTypeEnums } from "@/constants/setting-master";
import { useGetTenMstOriginInfoCreate } from "@/hooks/setting-master/useGetTenMstOriginInfoCreate";
import { useGetSetDataTenMst } from "@/hooks/setting-master/useGetSetDataTenMst";
import { DatePickerSuffixClear } from "@/components/ui/DatePicker";

import { KAZEI_KBN_OPTIONS } from "../../constants/mst-tax-type";
import {
  checkTenMstEndDate,
  checkTenMstStartDate,
  getPreviousDate,
} from "../../hooks/checkTenMstStartDate";
import { useModal } from "../../hooks/useModalProvider";
import { TenMstNewStartDateModal } from "../TenMstNewStartDateModal";

import type {
  DomainModelsMstItemJihiSbtMstModel,
  DomainModelsMstItemSetDataTenMstOriginModel,
  EmrCloudApiRequestsMstItemTenMstOriginModelDto,
  EmrCloudApiRequestsMstItemTenMstOriginModelDtoInput,
} from "@/apis/gql/generated/types";

const Wrapper = styled.div`
  display: flex;
`;

const SidebarArea = styled.div`
  width: 200px;
  border-right: 1px solid #e2e3e5;
`;

const TermHeading = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: bold;
  padding: 12px;
  background-color: #e0e6ec;
`;

const StyledPulldown = styled(Pulldown)`
  width: 140px;
  height: 36px;
`;

const StyledTextInput = styled(TextInput)`
  width: 140px;
`;

const TermList = styled.ul`
  height: 632px;
  overflow-y: auto;
`;

const TermListItem = styled.li<{
  currentOption?: boolean;
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 12px;
  padding: 8px;
  border-radius: 6px;
  transition-duration: 0.3s;
  ${({ currentOption }) => currentOption && ` background-color: #eaf0f5;`}
  &:hover {
    background-color: #eaf0f5;
  }
`;

const StyledIconButton = styled(IconButton)`
  width: 28px !important;
  height: 28px;
`;

const DeleteIconButton = styled(IconButton)`
  width: 20px !important;
  height: 20px;
`;

const ContentArea = styled.div`
  width: 480px;
  padding: 20px;
`;

const StyledLabel = styled(InputLabel)`
  margin-bottom: 6px;
`;

const InputSection = styled.div`
  margin-bottom: 20px;

  &:last-of-type {
    margin-bottom: 0;
  }
`;

const Yen = styled.span`
  display: inline-block;
  margin-left: 8px;
`;

const DatePickerWrapper = styled.div`
  display: flex;
  gap: 12px;
`;

const StyledDatePicker = styled(DatePickerSuffixClear)`
  width: 140px;
  height: 36px;
`;

const StyledErrorText = styled.span`
  color: red;
  font-size: 0.8em;
  margin-top: 4px;
`;

type Props = {
  itemCode: string;
  jihiMstList: DomainModelsMstItemJihiSbtMstModel[];
  isCopyMode: boolean;
  setItemCode: (data: string) => void;
  editExpense: (
    data: EmrCloudApiRequestsMstItemTenMstOriginModelDtoInput[],
  ) => void;
  tenMsts: EmrCloudApiRequestsMstItemTenMstOriginModelDto[];
  setTenMstSetData: (data: DomainModelsMstItemSetDataTenMstOriginModel) => void;
};

export const OwnExpenseEditModal: React.FC<Props> = ({
  itemCode,
  isCopyMode,
  setItemCode,
  tenMsts,
  setTenMstSetData,
  editExpense,
  jihiMstList,
}) => {
  const today = parseInt(dayjs().format("YYYYMMDD"), 10);
  const { modal, handleOpenModal, handleCloseModal } = useModal();
  const [selectIndex, setSelectIndex] = useState(0);
  const [isDataChecked, setIsDataChecked] = useState(false);
  const [isDataUpdated, setIsDataUpdated] = useState(false);
  const [currentListInput, setCurrentListInput] =
    useState<EmrCloudApiRequestsMstItemTenMstOriginModelDto[]>(tenMsts);
  const [numberActiveData, setNumberActiveData] = useState(tenMsts.length);
  const tenMstModelData = useMemo(() => {
    return tenMsts[0];
  }, [tenMsts]);

  const { tenMstOriginModel } = useGetTenMstOriginInfoCreate({
    type: ItemTypeEnums.JihiItem,
    skip: !isCopyMode,
    onSuccess: () => {
      if (isCopyMode) {
        refecthTenMstSetData();
        setItemCode(tenMstOriginModel?.itemCd || "");
        setCurrentListInput(
          currentListInput.map((x) => {
            return {
              ...x,
              itemCd: tenMstOriginModel?.itemCd || "",
              isAddNew: true,
            };
          }),
        );
      }
    },
  });

  const expenseItemCd = useMemo(() => {
    return isCopyMode ? tenMstOriginModel?.itemCd || "" : itemCode;
  }, [tenMstOriginModel, itemCode]);

  const jihiOptionsList = useMemo(() => {
    return jihiMstList.map((item) => ({
      value: item.jihiSbt || 0,
      label: item.name || "",
    }));
  }, [jihiMstList]);

  const { data: mstSetData, refetch: refecthTenMstSetData } =
    useGetSetDataTenMst({
      input: {
        sinDate: 0,
        itemCd: expenseItemCd,
        ipnNameCd: "",
        agekasanCd1Note: "",
        agekasanCd2Note: "",
        agekasanCd3Note: "",
        agekasanCd4Note: "",
      },
      skip: isCopyMode && expenseItemCd === "",
    });

  const methods = useForm<EmrCloudApiRequestsMstItemTenMstOriginModelDtoInput>({
    defaultValues: currentListInput[selectIndex],
  });

  const {
    register,
    handleSubmit,
    setValue,
    setError,
    clearErrors,
    watch,
    formState: { errors },
  } = methods;

  const handleEditExpense = async () => {
    await handleSubmit(setValidatedItem)();
    setIsDataUpdated(true);
  };

  const handleSubmitData = () => {
    setIsDataChecked(true);
    if (!errors || Object.keys(errors).length === 0) {
      handleCloseModal("OWNEXPENSE_EDIT");
      editExpense(
        currentListInput.filter(
          (item) => item.isAddNew !== true || item.isDeleted !== 1,
        ),
      );
    }
  };

  const startDate = watch("startDate");
  const endDate = watch("endDate");
  const handleChangeSelectIndex = async (index: number) => {
    await handleSubmit(setValidatedItem)();
    if (!errors || Object.keys(errors).length === 0) {
      setSelectIndex(index);
    }
  };

  const createNewTenMst = async (date: number, tenMstIndex?: number) => {
    await handleSubmit(setValidatedItem)();
    if (!errors || Object.keys(errors).length === 0) {
      const len = currentListInput.length;
      if (tenMstIndex !== undefined) {
        const endDate = currentListInput[tenMstIndex]?.endDate;
        const newEndDate = getPreviousDate(date);
        setCurrentListInput([
          ...currentListInput.slice(0, tenMstIndex),
          { ...currentListInput[tenMstIndex], endDate: newEndDate },
          ...currentListInput.slice(tenMstIndex + 1),
          {
            ...tenMstModelData,
            name: "",
            kanaName1: "",
            ryosyuName: "",
            receUnitName: "",
            ten: 0,
            jihiSbt: jihiOptionsList[0]?.value || 0,
            kazeiKbn: KAZEI_KBN_OPTIONS[0]?.value || 0,
            itemCd: expenseItemCd,
            startDate: date,
            endDate: endDate,
            isAddNew: true,
            isDeleted: 0,
          },
        ]);
      } else {
        setCurrentListInput([
          ...currentListInput,
          {
            ...tenMstModelData,
            name: "",
            kanaName1: "",
            ryosyuName: "",
            receUnitName: "",
            ten: 0,
            jihiSbt: jihiOptionsList[0]?.value || 0,
            kazeiKbn: KAZEI_KBN_OPTIONS[0]?.value || 0,
            itemCd: expenseItemCd,
            startDate: date,
            endDate: date,
            isAddNew: true,
            isDeleted: 0,
          },
        ]);
      }
      setSelectIndex(len);
      setNumberActiveData(numberActiveData + 1);
    }
  };

  const setValidatedItem = () => {
    const data = watch();
    setCurrentListInput([
      ...currentListInput.slice(0, selectIndex),
      {
        ...data,
        odrUnitName: data.receUnitName,
        endDate: data.endDate ? data.endDate : 99999999,
      },
      ...currentListInput.slice(selectIndex + 1),
    ]);
  };

  const handleOpenNewTenMstModal = () => {
    handleOpenModal("TENMST_CREATE");
  };

  const handleDeleteOption = (index: number) => {
    if (numberActiveData > 1) {
      const newList = currentListInput.map((item, idx) => {
        if (idx === index) {
          return {
            ...item,
            isDeleted: 1,
          };
        }
        return item;
      });
      setCurrentListInput(newList);
      setNumberActiveData(
        newList.filter((item) => item.isDeleted !== 1).length,
      );

      if (index === selectIndex) {
        const firstActiveIndex = newList.findIndex(
          (item) => item.isDeleted !== 1,
        );
        if (firstActiveIndex !== -1) {
          setSelectIndex(firstActiveIndex);
          const newData = newList[firstActiveIndex];
          if (newData) {
            setValue("itemCd", newData.itemCd);
            setValue("name", newData.name || "");
            setValue("kanaName1", newData.kanaName1);
            setValue("ryosyuName", newData.ryosyuName);
            setValue("ten", newData.ten);
            setValue("receUnitName", newData.receUnitName);
            setValue("kazeiKbn", newData.kazeiKbn);
            setValue("jihiSbt", newData.jihiSbt);
            setValue(
              "startDate",
              newData.startDate !== 0 ? newData.startDate : today,
            );
            setValue(
              "endDate",
              newData.endDate !== 99999999 ? newData.endDate : undefined,
            );
            setValue("isAddNew", newData.isAddNew);
            setValue("isDeleted", newData.isDeleted);
            setValue("originStartDate", newData.originStartDate);
          }
        }
      }
    }
  };

  useEffect(() => {
    if (isDataUpdated) {
      handleSubmitData();
      setIsDataUpdated(false);
    }
  }, [currentListInput, isDataUpdated]);

  useEffect(() => {
    if (expenseItemCd !== "") {
      setValue("itemCd", expenseItemCd);
      if (isCopyMode) {
        setValue("isAddNew", true);
      }
    }
  }, [expenseItemCd]);

  useEffect(() => {
    if (startDate === undefined) {
      setError("startDate", { message: "開始日を入力してください" });
    } else {
      clearErrors("startDate");
    }
  }, [startDate]);

  useEffect(() => {
    if (mstSetData) setTenMstSetData(mstSetData);
  }, [mstSetData]);

  useEffect(() => {
    if (tenMsts) {
      setCurrentListInput(tenMsts);
    }
  }, [tenMsts]);

  useEffect(() => {
    if (selectIndex !== -1 && currentListInput[selectIndex]) {
      setValue("itemCd", currentListInput[selectIndex]?.itemCd);
      setValue("name", currentListInput[selectIndex]?.name || "");
      setValue("kanaName1", currentListInput[selectIndex]?.kanaName1);
      setValue("ryosyuName", currentListInput[selectIndex]?.ryosyuName);
      setValue("ten", currentListInput[selectIndex]?.ten);
      setValue("receUnitName", currentListInput[selectIndex]?.receUnitName);
      setValue("kazeiKbn", currentListInput[selectIndex]?.kazeiKbn);
      setValue("jihiSbt", currentListInput[selectIndex]?.jihiSbt);
      setValue(
        "startDate",
        currentListInput[selectIndex]?.startDate !== 0
          ? currentListInput[selectIndex]?.startDate
          : today,
      );
      setValue(
        "endDate",
        currentListInput[selectIndex]?.endDate !== 99999999
          ? currentListInput[selectIndex]?.endDate
          : undefined,
      );
      setValue("isAddNew", currentListInput[selectIndex].isAddNew);
      setValue("isDeleted", currentListInput[selectIndex].isDeleted);
      setValue(
        "originStartDate",
        currentListInput[selectIndex].originStartDate,
      );
    }
  }, [selectIndex]);

  return (
    <Modal
      width={680}
      zIndex={1001}
      isOpen={modal.ownexpenseEditOpen}
      title="自費診療マスタ編集"
      footer={[
        <Button
          varient="tertiary"
          key="cancel"
          onClick={() => handleCloseModal("OWNEXPENSE_EDIT")}
        >
          キャンセル
        </Button>,
        <Button varient="primary" key="register" onClick={handleEditExpense}>
          保存
        </Button>,
      ]}
    >
      <Wrapper>
        {modal.tenMstCreateOpen && (
          <TenMstNewStartDateModal
            listTenMst={currentListInput}
            selectItem={createNewTenMst}
          />
        )}
        <SidebarArea>
          <TermHeading>
            <p>適用期間</p>
            <StyledIconButton
              varient="round"
              icon={<SvgIconPlus />}
              onClick={() => {
                handleSubmit(handleOpenNewTenMstModal)();
              }}
            />
          </TermHeading>

          <TermList>
            {currentListInput
              .filter((item) => item.isDeleted !== 1)
              .map((time) => {
                const originalIndex = currentListInput.findIndex(
                  (item) => item.startDate === time.startDate,
                );
                return (
                  <TermListItem
                    key={time.startDate}
                    role="button"
                    tabIndex={0}
                    currentOption={originalIndex === selectIndex}
                    onClick={() => {
                      if (originalIndex !== selectIndex)
                        handleChangeSelectIndex(originalIndex);
                    }}
                  >
                    {originalIndex === selectIndex ? (
                      <p>
                        {startDate}〜{endDate}
                      </p>
                    ) : (
                      <p>
                        {time.startDate}〜
                        {time.endDate === 99999999 ? "" : time.endDate}
                      </p>
                    )}
                    <DeleteIconButton
                      varient="icon-only"
                      icon={<SvgIconDelete />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteOption(originalIndex);
                      }}
                      disabled={numberActiveData <= 1}
                    />
                  </TermListItem>
                );
              })}
          </TermList>
        </SidebarArea>

        <ContentArea>
          <InputSection>
            <DatePickerWrapper>
              <div>
                <StyledLabel label="開始日" required />
                <StyledDatePicker
                  hasError={isDataChecked && !!errors.startDate}
                  disabledDate={(current) =>
                    current &&
                    current.isAfter(dayjs(String(endDate), "YYYYMMDD"), "day")
                  }
                  value={
                    startDate
                      ? dayjs(startDate.toString(), "YYYYMMDD")
                      : undefined
                  }
                  onChange={(e) => {
                    if (e) {
                      if (
                        checkTenMstStartDate(parseInt(e.format("YYYYMMDD")), [
                          ...currentListInput.slice(0, selectIndex),
                          ...currentListInput.slice(selectIndex + 1),
                        ])
                      ) {
                        setValue("startDate", parseInt(e.format("YYYYMMDD")));
                      }
                    } else {
                      setValue("startDate", undefined);
                    }
                  }}
                />
                {isDataChecked && !!errors.startDate && (
                  <StyledErrorText>{errors.startDate?.message}</StyledErrorText>
                )}
              </div>
              <div>
                <StyledLabel label="終了日" />
                <StyledDatePicker
                  disabledDate={(current) =>
                    current &&
                    current.isBefore(
                      dayjs(String(startDate), "YYYYMMDD"),
                      "day",
                    )
                  }
                  value={
                    endDate ? dayjs(endDate.toString(), "YYYYMMDD") : undefined
                  }
                  onChange={(e) => {
                    if (e) {
                      if (
                        checkTenMstEndDate(
                          parseInt(e.format("YYYYMMDD")),
                          [
                            ...currentListInput.slice(0, selectIndex),
                            ...currentListInput.slice(selectIndex + 1),
                          ],
                          startDate,
                        )
                      ) {
                        setValue("endDate", parseInt(e.format("YYYYMMDD")));
                      }
                    } else {
                      setValue("endDate", undefined);
                    }
                  }}
                />
              </div>
            </DatePickerWrapper>
          </InputSection>

          <InputSection>
            <StyledLabel label="表示名称" />
            <TextInput
              value={watch("name")}
              {...register("name", {
                required: "表示名称は必須です",
                maxLength: {
                  value: 30,
                  message: "30文字以内で入力してください",
                },
              })}
              hasError={isDataChecked && !!errors.name}
              onChange={(e) => {
                setValue("name", e.target.value);
              }}
            />
            {isDataChecked && !!errors.name && (
              <StyledErrorText>{errors.name?.message}</StyledErrorText>
            )}
          </InputSection>

          <InputSection>
            <StyledLabel label="カナ名称" />
            <TextInput
              value={watch("kanaName1")}
              {...register("kanaName1")}
              hasError={!!errors.kanaName1}
              onChange={(e) => {
                setValue("kanaName1", e.target.value);
              }}
            />
          </InputSection>

          <InputSection>
            <StyledLabel label="領収書名称" />
            <TextInput
              value={watch("ryosyuName")}
              {...register("ryosyuName")}
              hasError={!!errors.ryosyuName}
              onChange={(e) => {
                setValue("ryosyuName", e.target.value);
              }}
            />
          </InputSection>
          <InputSection>
            <StyledLabel label="金額" required />
            <StyledTextInput
              value={watch("ten")}
              {...register("ten", {
                required: true,
              })}
              hasError={!!errors.ten}
              onChange={(e) => {
                setValue("ten", parseInt(e.target.value, 10));
              }}
            />
            <Yen>円</Yen>
          </InputSection>

          <InputSection>
            <StyledLabel label="単位" />
            <StyledTextInput
              value={watch("receUnitName")}
              {...register("receUnitName")}
              onChange={(e) => {
                setValue("receUnitName", e.target.value);
              }}
            />
          </InputSection>
          <InputSection>
            <StyledLabel label="税区分" />
            <StyledPulldown
              value={watch("kazeiKbn")}
              options={KAZEI_KBN_OPTIONS}
              onChange={(e) => {
                setValue("kazeiKbn", e);
              }}
            />
          </InputSection>

          <InputSection>
            <StyledLabel label="集計先" required />
            <StyledPulldown
              value={watch("jihiSbt")}
              options={jihiOptionsList}
              onChange={(e) => {
                setValue(
                  "jihiSbt",
                  jihiOptionsList.find((item) => item.value === e)?.value || 0,
                );
              }}
            />
          </InputSection>
        </ContentArea>
      </Wrapper>
    </Modal>
  );
};
