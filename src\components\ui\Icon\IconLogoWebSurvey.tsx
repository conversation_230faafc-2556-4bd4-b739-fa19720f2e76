// ![DO NOT EDIT] this file is auto-generated by svgr;
import * as React from "react";
import type { SVGProps } from "react";
export const SvgIconLogoWebSurvey = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={159}
    height={14}
    fill="none"
    {...props}
  >
    <g clipPath="url(#icon-logo-web-survey_svg__a)">
      <path
        fill="#595757"
        d="M83.137 9.896c-2.178-2.573-5.59-6.185-7.934-8.404l-.086-.086-4.423 4.786a4.5 4.5 0 0 1-.573.502l-.107.077 1.533 1.626.122-.19c.036-.05.057-.078.094-.156l.143-.198 3.254-5.026c2.688 3.4 5.813 7.698 6.874 9.443l.086.142 1.612-1.887-.085-.078a7 7 0 0 1-.517-.565zm5.656-8.292c.057.297.071.46.071.763v3.324c0 1.647-.193 2.849-.637 3.902-.445 1.095-1.549 2.325-2.28 2.53l-.164.042.587.806.1-.113c.1-.113.223-.212.416-.34 2.789-1.66 3.77-3.448 3.77-6.834V2.368c0-.212.03-.516.072-.763l.029-.135h-1.993l.03.141zm10.852 3.153c-.459 2.39-2.789 4.806-5.348 5.563V1.647c0-.212.029-.516.072-.763l.029-.135h-1.979l.029.141c.058.297.072.46.072.763v10.979l.136-.028c3.763-.68 6.114-2.758 7.411-6.546q.166-.538.381-.933l.057-.107-.832-.402-.028.148zm10.565 2.608c1.046-1.59 1.763-3.4 2.186-5.527l.029-.142h-8.33c-.214 0-.53-.035-.745-.077l-.143-.029v1.068l.143-.028q.369-.084.745-.078h5.886c-.574 3.209-2.438 6.064-5.247 8.058-1.033.72-2.272 1.315-2.874 1.371l-.173.014.445.877.115-.07a2.3 2.3 0 0 1 .724-.319c2.709-.841 4.694-2.057 6.242-3.838l.022-.035s.114-.135.136-.156c.086-.099.158-.176.208-.247 1.648 1.414 2.903 2.947 3.82 4.686l.079.149 1.563-1.647-.051-.078c-.616-.905-3.046-2.912-4.788-3.966zm8.909-5.258h8.587c.265 0 .53.035.753.077l.136.021v-1.06l-.144.029a4 4 0 0 1-.745.078h-8.587c-.215 0-.53-.035-.746-.077l-.143-.03v1.067l.143-.028c.273-.057.445-.078.746-.078zm9.992 3.039h-11.375q-.376-.007-.746-.077l-.143-.029v1.068l.143-.028q.369-.084.746-.079h5.132c-.072 2.284-.545 3.661-1.613 4.736-.731.77-2.465 1.696-3.046 1.626l-.201-.028.395.94.121-.07c.137-.086.215-.114.574-.22 4.064-1.11 5.712-3.068 5.849-6.977h4.171c.237 0 .415.014.745.078l.144.028v-1.06l-.136.021a5 5 0 0 1-.753.078zm7.411-.728c.38.537 1.061 2.544 1.154 3.407l.029.24v.198l1.541-.622-.029-.1c-.301-1.13-1.096-2.713-1.734-3.455a.7.7 0 0 1-.151-.262l-.043-.134-.917.509.072.106q.035.049.078.113m-.179 3.59c-.365-1.08-.932-2.078-1.971-3.442a1.5 1.5 0 0 1-.18-.261l-.057-.127-.888.558.1.106c.48.488 1.125 1.994 1.39 3.23.05.255.072.34.108.488l.05.22 1.477-.666-.036-.098.007-.007z"
      />
      <path
        fill="#595757"
        d="M141.368 4.481c.014 1.266-.481 3.153-1.126 4.298-.918 1.795-3.139 3.386-4.845 3.456h-.165l.337.884.121-.07c.101-.057.129-.064.316-.12l.179-.057c4.258-1.188 5.992-3.047 6.852-7.359.094-.48.108-.537.151-.657l.043-.127-1.863-.403zm10.386-1.986c.15-.332.308-.728.437-1.117.121-.346.2-.565.258-.679l.064-.126L150.385 0l-.015.141c-.093 1.167-.975 3.428-1.749 4.482-.774 1.095-2.029 2.24-2.523 2.318l-.186.028.559.835.1-.12c.072-.085.258-.22.502-.347 2.2-1.18 3.275-2.191 4.236-3.98h4.573c-.444 2.912-1.684 5.266-3.684 6.998-1.327 1.16-3.175 2.071-4.301 2.114h-.164l.35.898.115-.05c.251-.106.401-.149.696-.226 5.397-1.372 8.235-4.432 9.21-9.924l.115-.672z"
      />
      <path
        fill="#005BAC"
        d="M33.48 12.66h.91c.043 0 .1-.029.129-.07l3.756-6.066 2.043 6.941a.12.12 0 0 0 .108.078h3.942c.042 0 .071-.035.057-.077L40.261.473a.13.13 0 0 0-.108-.078H38.67c-.042 0-.107.028-.128.07l-5.068 7.748-5.06-7.747a.18.18 0 0 0-.13-.07H26.8a.12.12 0 0 0-.108.077l-4.157 12.985c-.014.042.014.078.058.078h3.942a.13.13 0 0 0 .107-.077l2.044-6.942 3.755 6.065c.02.042.079.07.129.07h.91zM22.715 6.191a.084.084 0 0 0-.079-.064h-11.24c-.595 0-1.074.474-1.074 1.06 0 .588.48 1.06 1.074 1.06h6.674c-.796 2.072-3.484 3.599-6.674 3.599-3.82 0-6.916-2.177-6.916-4.87s3.096-4.878 6.916-4.878c2.086 0 3.957.651 5.226 1.683l.014.014a.1.1 0 0 0 .043.014h4.594a.087.087 0 0 0 .087-.085.06.06 0 0 0-.022-.049l-.007-.007c-1.957-2.065-5.676-3.45-9.935-3.45C5.103.219 0 3.244 0 6.969s5.103 6.75 11.396 6.75 11.398-3.018 11.398-6.75c0-.261-.03-.523-.08-.777zM55.564.22c-6.294 0-11.397 3.018-11.397 6.75s5.103 6.75 11.396 6.75S66.96 10.702 66.96 6.97 61.85.22 55.564.22m0 11.62c-3.82 0-6.917-2.177-6.917-4.87s3.096-4.87 6.916-4.87 6.917 2.178 6.917 4.87c0 2.694-3.096 4.871-6.916 4.871z"
      />
    </g>
    <defs>
      <clipPath id="icon-logo-web-survey_svg__a">
        <path fill="#fff" d="M0 0h159v14H0z" />
      </clipPath>
    </defs>
  </svg>
);
