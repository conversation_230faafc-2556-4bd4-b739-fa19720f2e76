import { useState } from "react";

import { omit } from "lodash";

import { usePostApiJsonSettingUpsertMutation } from "@/apis/gql/operations/__generated__/json-setting";
import { useGetApiKaGetListMstQuery } from "@/apis/gql/operations/__generated__/ka";
import {
  useGetApiRaiinFilterGetListMstLazyQuery,
  useGetTreatmentDepartmentListQuery,
  usePostApiRaiinFilterSaveListMstMutation,
} from "@/apis/gql/operations/__generated__/reception";
import { RECEPTION_JSON_SETTING_KEY } from "@/features/reception/constants";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import type {
  FilterOption,
  ListOptionFilter,
} from "@/features/reception/types";
import type { DomainModelsRaiinFilterMstRaiinFilterMstModel } from "@/apis/gql/generated/types";

export const useFilterReception = () => {
  const { handleError } = useErrorHandler();
  const [listKaMst, setListKaMst] = useState<FilterOption[]>([]);
  const [listTreatmentDepartments, setListTreatmentDepartments] = useState<
    FilterOption[]
  >([]);
  const [listTreatmentWithoutItemDeleted, setListTreatmentWithoutItemDeleted] =
    useState<FilterOption[]>([]);
  const [raiinFilterListMst, setRaiinFilterListMst] = useState<
    DomainModelsRaiinFilterMstRaiinFilterMstModel[]
  >([]);

  const [saveFilterListMst, { loading }] =
    usePostApiRaiinFilterSaveListMstMutation({
      onError: (error) => {
        handleError({ error });
      },
      onCompleted: () => getFilterListMst(),
    });
  const [filterOptionsList, setFilterOptionList] = useState<ListOptionFilter[]>(
    [],
  );

  useGetApiKaGetListMstQuery({
    variables: { isDeleted: 0 },
    onError: (error) => {
      handleError({ error });
    },
    onCompleted(data) {
      const departments: FilterOption[] =
        data.getApiKaGetListMst?.data?.departments?.map((item) => ({
          id: item.kaId ?? 0,
          label: item.kaName ?? "",
        })) || [];
      setListKaMst(departments);
    },
  });

  useGetTreatmentDepartmentListQuery({
    variables: { isDeleted: true },
    onError: (error) => {
      handleError({ error });
    },

    onCompleted(data) {
      const dataRes =
        data.getApiTreatmentDepartmentGetTreatmentDepartmentList?.data
          ?.treatmentStatusList;
      if (!dataRes || !dataRes.length) return;
      const listTreatmentDepartments: FilterOption[] =
        dataRes
          .filter((item) => item.treatmentDepartmentStatus === 1)
          .map((item) => ({
            id: item.treatmentDepartmentId ?? 0,
            label: item.title ?? "",
          })) || [];

      const listTreatmentWithoutItemDeleted: FilterOption[] =
        dataRes
          .filter(
            (item) => item.treatmentDepartmentStatus === 1 && !item.isDeleted,
          )
          .map((item) => ({
            id: item.treatmentDepartmentId ?? 0,
            label: item.title ?? "",
          })) || [];

      setListTreatmentWithoutItemDeleted(listTreatmentWithoutItemDeleted);
      setListTreatmentDepartments(listTreatmentDepartments);
    },
  });

  const [getFilterListMst, { loading: loadingGetFilterList }] =
    useGetApiRaiinFilterGetListMstLazyQuery({
      fetchPolicy: "no-cache",
      onError: (error) => {
        handleError({ error });
      },
      onCompleted: (data) => {
        const filterMsts =
          data.getApiRaiinFilterGetListMst?.data?.filterMsts || [];
        const optionsList = filterMsts.map((item) => ({
          label: item.filterName,
          value: item.filterId,
          kaIds: item.kaIds,
          treatmentIds: item.treatmentIds,
          doctorIds: item.doctorIds,
          statusIds: item.statusIds,
          labelIds: item.labelIds,
        }));
        setFilterOptionList(optionsList);
        setRaiinFilterListMst(filterMsts.map((f) => omit(f, "__typename")));
      },
    });

  const [resetSetting] = usePostApiJsonSettingUpsertMutation();

  const handleDeleteOption = async (filterId?: number) => {
    if (!filterId) return;
    const filterList = await getFilterListMst();
    const filterMsts =
      filterList.data?.getApiRaiinFilterGetListMst?.data?.filterMsts || [];
    const filterMstsFilter = filterMsts.filter(
      (item) => item.filterId !== filterId,
    );
    handleSaveFilterListMst(filterMstsFilter);
  };

  const handleSaveFilterListMst = (
    filterMsts: DomainModelsRaiinFilterMstRaiinFilterMstModel[],
  ) => {
    return saveFilterListMst({
      variables: {
        input: {
          filterMsts,
        },
      },
    });
  };

  const resetFilter = () => {
    void resetSetting({
      variables: {
        userId: 0,
        key: RECEPTION_JSON_SETTING_KEY,
        value: "",
      },
    });
  };

  const onSaveFilter = async (
    metaData: {
      labelIds: number[];
      statusIds: number[];
      doctorIds: number[];
      kaIds: number[];
      treatDpmIds: number[];
      filterName: string;
    },
    filterId?: number,
    filterMstsList?: DomainModelsRaiinFilterMstRaiinFilterMstModel[],
  ) => {
    const { labelIds, statusIds, doctorIds, kaIds, treatDpmIds, filterName } =
      metaData;

    let newRaiinFilterListMst = filterMstsList || raiinFilterListMst;
    if (filterId) {
      newRaiinFilterListMst = newRaiinFilterListMst.map((item) =>
        item.filterId === filterId
          ? {
              ...item,
              kaIds: kaIds,
              treatmentIds: treatDpmIds,
              statusIds: statusIds,
              doctorIds: doctorIds,
              labelIds: labelIds,
              filterName: filterName,
            }
          : item,
      );
    } else {
      const newFilter = {
        doctorIds: doctorIds,
        labelIds: labelIds,
        statusIds: statusIds,
        kaIds: kaIds,
        treatmentIds: treatDpmIds,
        selectKbn: 0,
        shortcut: "",
        sortNo: 11,
        filterName: filterName,
        filterId: 0,
      };
      newRaiinFilterListMst.push(newFilter);
    }
    return handleSaveFilterListMst(newRaiinFilterListMst);
  };

  return {
    loading,
    listKaMst,
    listTreatmentDepartments,
    filterOptionsList,
    loadingGetFilterList,
    handleDeleteOption,
    getFilterListMst,
    resetFilter,
    onSaveFilter,
    listTreatmentWithoutItemDeleted,
  };
};
