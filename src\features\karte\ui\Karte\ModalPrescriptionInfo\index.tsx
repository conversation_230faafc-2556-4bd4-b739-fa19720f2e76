import { useEffect, useMemo, useState } from "react";

import styled from "styled-components";
import { Flex, Spin, Tabs, Typography } from "antd";
import { useRouter } from "next/router";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { SvgIconArrowUp } from "@/components/ui/Icon/IconArrowUp";
import { SvgIconArrowDown } from "@/components/ui/Icon/IconArrowDown";
import { RenderIf } from "@/utils/common/render-if";
import {
  usePostApiEpsGetPrescriptionIdListMutation,
  usePostApiEpsUpdatePrescriptionStatusByIdsMutation,
} from "@/apis/gql/operations/__generated__/karte-retry-cancel";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { usePrintOutPatientPrescription } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/usePrintOutPatientPrescription";
import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { SvgIconError } from "@/components/ui/Icon/IconError";
import {
  Flow,
  PaymentAutoCalculationFrom,
  useModal,
} from "@/features/karte/providers/ModalProvider";

import { useEPrescriptionContext } from "../PrintSetting/EPrescriptionContextProvider";
import { ModalCancelPrescriptionHasChange } from "../ModalCancelPrescriptionHasChange";
import { useElectricSignature } from "../ElectronicSignature/useElectricSignature";
import { useObserverWaitingModalContext } from "../StationPrescription/ObserverWaitingModalActionProvider";
import { closeWindow } from "../PrintOutpatientPrescription/utils";

import type { Dispatch, SetStateAction } from "react";
import type { RefillData } from "../ModalRetryCancel";
import type {
  DomainModelsEpsDispensingGroupDetailModel,
  EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse,
} from "@/apis/gql/generated/types";

const { TabPane } = Tabs;

const TitleWrapper = styled.div`
  padding: 8px;
  background-color: #e0e6ec;
  height: 44px;
  border-bottom: solid 1px rgba(0, 0, 0, 0.1);
`;

const TitleContentWrapper = styled.div`
  background-color: "#e0e6ec";
`;

const ToggleButtonWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ContentLeft = styled.div`
  width: 360px;
  max-height: 600px;
  overflow: auto;
  border-right: solid 1px #e2e3e5;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 2px grey;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
`;

const ContentRight = styled.div`
  display: flex;
  flex-direction: column;
  flex: auto;
`;

const DetailInfo = styled.div`
  display: flex;
  flex-direction: column;
  height: 360px;
  border-bottom: solid 1px #e2e3e5;
`;

const TitleDetailInfo = styled.div`
  height: 48px;
  display: flex;
  align-items: center;
  padding-left: 24px;
  border-bottom: solid 1px #e2e3e5;
  border-top: solid 1px #e2e3e5;
`;

const StyledModal = styled(Modal)<{
  $errorModal?: boolean;
}>`
  .ant-modal-header {
    background-color: ${({ $errorModal }) =>
      $errorModal ? "#e74c3c" : "#005BAC"};
  }
  .ant-modal-header {
    background-color: #005bac;
  }
  .ant-modal-body {
    min-height: 256px;
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .ant-modal-footer {
    height: 84px;
    align-items: center;
  }
`;

const Wrapper = styled.div`
  height: auto;
  max-height: 648px;
`;

const WrapperContent = styled.div`
  display: flex;
  flex-direction: row;
`;

const GroupBtn = styled.div`
  display: flex;
  align-items: center;
  gap: 40px;
  flex-direction: row;
`;

const TabVertical = styled(Tabs)`
  width: 100%;
  .ant-tabs-nav {
    width: 200px;
    border-right: solid 1px #e2e3e5;
    padding: 12px;
  }
  .ant-tabs-tab {
    margin: 0 !important;
    height: 32px !important;
    width: 100% !important;
    border-radius: 6px;
    padding: 9px 8px !important;
    align-items: center !important;
    justify-content: flex-start !important;
  }
  .ant-tabs-nav-list .ant-tabs-tab .ant-tabs-tab-btn {
    color: #243544 !important;
  }
  .ant-tabs-tab.ant-tabs-tab-active {
    background-color: #eaf0f5 !important;
  }
  .ant-tabs-nav-list .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    color: #243544 !important;
  }
  .ant-tabs-ink-bar {
    display: none;
  }
`;

const StyledTabs = styled(Tabs)`
  .ant-tabs-nav-list {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 48px;
    background-color: #fff;
  }
  .ant-tabs-nav {
    margin: 0;
  }
  .ant-tabs-tab {
    width: 140px;
    height: 100%;
    justify-content: center;
    font-size: 16px;

    font-weight: normal;
    display: flex;
    align-items: end;
    text-align: center;
    padding: 8px 0px;
    margin-left: 24px;
  }

  .ant-tabs-tab .ant-tabs-tab-btn {
    color: #6a757d !important;
  }

  .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
    text-shadow: none !important;
    font-weight: 400;
    color: #005bac !important;
  }

  .ant-tabs-ink-bar {
    height: 4px !important;
    transition:
      left 0.2s ease-out,
      width 0.2s ease-out !important;
  }

  .ant-tabs-tabpane {
    transition: margin 0.2s ease-out !important;
  }
  .ant-table-thead > tr > th:first-child {
    font-weight: normal !important;
    text-align: center;
  }
`;

const RowWrapper = styled.div`
  min-height: 40px;
  border-bottom: solid 1px #e2e3e5;
  display: flex;
  align-items: center;
  flex-direction: row;
  padding: 13px 8px;
  gap: 20px;
`;

const ContentLeftRowWrapper = styled.div`
  width: 148px;
  color: #6a757d;
`;

const Title = styled(Typography.Title)`
  margin-bottom: 0 !important;
`;

const Text = styled(Typography.Text)`
  color: #243544;
`;

const ContentVertical = styled.div`
  padding-top: 10px;
`;

const ContentDetail = styled.div`
  padding: 10px;
  padding-top: 20px;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 2px grey;
    border-radius: 4px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
`;

const ModalContentConfirm = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 24px;
`;

const MoreInfo = styled.div`
  height: 240px;
  border-bottom: solid 1px #e2e3e5;
  display: flex;
  flex-direction: row;
`;

const ModalContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 30px;
  min-height: 256px;
  width: 100%;
`;

const ConfirmButton = styled(Button)`
  position: absolute;
  bottom: 8px;
  right: 24px;
`;

const ContentErrorWapper = styled.div`
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 24px 50px;
`;

type ModalProps = {
  data: RefillData | undefined;
  isLoading: boolean;
  isOpen: boolean;
  showModalError: boolean;
  handleCloseModal: () => void;
  setShowModalError: Dispatch<SetStateAction<boolean>>;
};

type CollapseContentProps = {
  numberCollapse: number;
  title: string;
  dataToggle: DomainModelsEpsDispensingGroupDetailModel[];
  toggleCollapse: number[];
  handleToggleCollapse: (e: number) => void;
};

type TabPanelContentProps = {
  dataCollapse: {
    title: string;
    data: DomainModelsEpsDispensingGroupDetailModel[];
  }[];
  dataSelected:
    | EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse
    | undefined;
  toggleCollapse: number[];
  handleToggleCollapse: (id: number) => void;
};

const RowData = ({
  data,
}: {
  data: DomainModelsEpsDispensingGroupDetailModel[];
}) => {
  if (data.length > 0) {
    return data.map((item, index) => (
      <RowWrapper key={index}>
        <ContentLeftRowWrapper>{item.item}</ContentLeftRowWrapper>
        <Text>{item.data}</Text>
      </RowWrapper>
    ));
  }
  return null;
};

const CollapseContent = ({
  numberCollapse,
  title,
  dataToggle,
  toggleCollapse,
  handleToggleCollapse,
}: CollapseContentProps) => {
  return (
    <>
      <TitleWrapper>
        <Flex gap={8} justify="space-between">
          <TitleContentWrapper>
            <Title level={5} style={{ margin: 0 }}>
              {title}
            </Title>
          </TitleContentWrapper>
          <ToggleButtonWrapper>
            <Button
              varient="standard"
              shape="round"
              style={{
                width: "28px",
                height: "28px",
                borderRadius: "6px",
                padding: 0,
              }}
              onClick={() => handleToggleCollapse(numberCollapse)}
            >
              {toggleCollapse.includes(numberCollapse) ? (
                <SvgIconArrowUp />
              ) : (
                <SvgIconArrowDown />
              )}
            </Button>
          </ToggleButtonWrapper>
        </Flex>
      </TitleWrapper>
      <RenderIf condition={toggleCollapse.includes(numberCollapse)}>
        <RowData data={dataToggle} />
      </RenderIf>
    </>
  );
};

const TabPanelContent = ({
  dataCollapse,
  dataSelected,
  toggleCollapse,
  handleToggleCollapse,
}: TabPanelContentProps) => {
  return (
    <WrapperContent>
      <ContentLeft>
        {dataCollapse.map((item, index) => (
          <CollapseContent
            key={index}
            numberCollapse={index + 1}
            title={item.title}
            dataToggle={item.data}
            toggleCollapse={toggleCollapse}
            handleToggleCollapse={handleToggleCollapse}
          />
        ))}
      </ContentLeft>
      <ContentRight>
        <DetailInfo>
          <TitleDetailInfo>
            <Title level={5}>処方箋情報明細</Title>
          </TitleDetailInfo>
          <ContentDetail>
            {dataSelected?.compositionModels?.map((item, index) => (
              <Flex key={index} vertical gap="5px">
                <Flex justify="space-between" align="center">
                  <div>{item.rpDisplay}</div>
                  <div>{item.compositionUsages?.[0]?.dispensing}</div>
                </Flex>
                <div>{item.compositionUsages?.[0]?.dosageForm}</div>
                <div>{item.compositionUsages?.[0]?.usage}</div>
                <div>{item.compositionUsages?.[0]?.numberPerDay}</div>
                <div>{item.compositionUsages?.[0]?.usageSupplementary}</div>
                <div></div>
                <Flex justify="space-between" align="center">
                  <div>{item.compositionMedicines?.[0]?.pharmaceutical}</div>
                  <div>{item.compositionMedicines?.[0]?.quantity}</div>
                </Flex>
                <div>{item.compositionMedicines?.[0]?.singleDose}</div>
                <div>
                  {item.compositionMedicines?.[0]?.drugSupplements?.join(",")}
                </div>
              </Flex>
            ))}
          </ContentDetail>
        </DetailInfo>
        <MoreInfo>
          <TabVertical
            defaultActiveKey="1"
            tabPosition="left"
            items={dataSelected?.longInformationModels?.map((item, index) => ({
              key: `${index + 1}`,
              label: item.header,
              children: item.recordedContents?.map((content, i) => (
                <ContentVertical key={i}>{content}</ContentVertical>
              )),
            }))}
          />
        </MoreInfo>
      </ContentRight>
    </WrapperContent>
  );
};

export const ModalPrescriptionInfo = ({
  isLoading,
  data,
  isOpen,
  handleCloseModal,
  showModalError,
  setShowModalError,
}: ModalProps) => {
  const { handleError } = useErrorHandler();
  const [toggleCollapse, setToggleCollapse] = useState<number[]>([1, 2, 3, 4]);
  const [prescriptionIdList, setPrescriptionIdList] = useState<string[]>([]);
  const [isShowModalConfirm, setIsShowModalConfirm] = useState<boolean>(false);
  const [isShowModalLoading, setIsShowModalLoading] = useState<boolean>(true);
  const [disabledBtn, setDisabledBtn] = useState<boolean>(true);

  const [dataSelected, setDataSelected] = useState<
    EmrCloudApiResponsesEpSGetPrescriptionFromCsvResponse | undefined
  >(data?.refill);

  const { handlePrintOutPatientPrescriptionPaper } =
    usePrintOutPatientPrescription();

  const {
    query: { id, raiinNo, sinDate },
  } = useRouter();

  const { data: dataSystemConf } = useGetApiSystemConfGetListQuery({
    onError: (error) => {
      handleCloseModal();
      handleError({ error });
    },
  });

  const { setFlow } = useObserverWaitingModalContext();

  useEffect(() => {
    if (dataSystemConf) {
      const dataSystemConfFiltered =
        dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.find(
          (item) => item.grpCd === 100040 && item.grpEdaNo === 4,
        );
      const timeGetResult = Number(dataSystemConfFiltered?.val) * 1000;

      setTimeout(() => {
        setDisabledBtn(false);
      }, timeGetResult);
    }
  }, [dataSystemConf]);

  useEffect(() => {
    if (data && !dataSelected && !isLoading) {
      setDataSelected(data.refill || data.refill2 || data.refill3);
    }
  }, [data, dataSelected, isLoading]);

  const [getPrescriptionIdList, { loading }] =
    usePostApiEpsGetPrescriptionIdListMutation({
      onError: (error) => {
        handleCloseModal();
        handleError({ error });
      },
    });

  const [updateStatus] = usePostApiEpsUpdatePrescriptionStatusByIdsMutation({
    onError: (error) => {
      handleCloseModal();
      handleError({ error });
    },
  });

  const {
    statePrintSetting: { outpatientOption, isOutpatientPrescription },
    isClosePageKarte,
  } = useEPrescriptionContext();

  const {
    handleOpenModal,
    handleCloseModal: handleCloseModalProvider,
    state: stateModal,
  } = useModal();
  console.log(stateModal, "stateModal");

  const handleConfirm = () => {
    handlePrintOutPatientPrescriptionPaper().then();

    if (!isClosePageKarte || !data || isLoading) {
      if (!data) {
        handleCloseModalNoData();
      }
      handleCloseModal();
      if (stateModal.paymentAutoCalculationOpen) {
        handleOpenModal(
          "PAYMENT_AUTO_CALCULATION",
          undefined,
          Flow.Flow2,
          undefined,
          undefined,
          PaymentAutoCalculationFrom.Accounting,
        );
      }
    }
  };

  const handleUnRegister = () => {
    if (isOutpatientPrescription) {
      setIsShowModalConfirm(true);
    }

    if (!isClosePageKarte && !isOutpatientPrescription) {
      handleCloseModal();
      if (stateModal.paymentAutoCalculationOpen) {
        handleOpenModal(
          "PAYMENT_AUTO_CALCULATION",
          undefined,
          Flow.Flow2,
          undefined,
          undefined,
          PaymentAutoCalculationFrom.Accounting,
        );
      }
    }

    if (isClosePageKarte && !isOutpatientPrescription) {
      closeWindow(isClosePageKarte);
    }
  };

  const onChangeTabs = (key: string) => {
    if (key === "1") {
      setDataSelected(data?.refill);
    }
    if (key === "2") {
      setDataSelected(data?.refill2);
    }
    if (key === "3") {
      setDataSelected(data?.refill3);
    }
  };

  const handleToggleCollapse = (id: number) => {
    setToggleCollapse((prev: number[]) => {
      if (prev.includes(id)) {
        const newData = prev.filter((item) => item !== id);
        return newData;
      }
      return [...prev, id];
    });
  };

  const handleCloseModalNoData = () => {
    setIsShowModalLoading(false);
    setShowModalError(false);
  };

  const { isSignatureLocal } = useElectricSignature({});

  const handleSubmit = () => {
    getPrescriptionIdList({
      variables: {
        emrCloudApiRequestsEpsGetPrescriptionIdListRequestInput: {
          issueType: Number(outpatientOption) === 3 ? 2 : 1,
          prescriptionStatus: [0],
          ptId: String(id),
          raiinNo: String(raiinNo),
          refileCount: data?.refill3 ? 3 : data?.refill2 ? 2 : 1,
        },
      },
      onCompleted: (res) => {
        const prescriptionIds =
          res.postApiEpsGetPrescriptionIdList?.data?.prescriptionIds;
        if (prescriptionIds?.length) {
          const prescriptionStatusItem = prescriptionIds.map((item) => ({
            deletedReason: 2,
            status: 2,
            prescriptionId: item,
          }));

          updateStatus({
            variables: {
              emrCloudApiRequestsEpsUpdatePrescriptionStatusByIdsRequestInput: {
                prescriptionStatusItem,
                ptId: String(id),
                raiinNo: String(raiinNo),
                sinDate: Number(sinDate),
              },
            },
          }).then((resUpdateStatus) => {
            if (
              resUpdateStatus.data?.postApiEpsUpdatePrescriptionStatusByIds
                ?.data?.isSuccess
            ) {
              setPrescriptionIdList(prescriptionIds);
            }
          });
        } else {
          const fileType = outpatientOption === "3" ? 2 : 1;
          if (!isSignatureLocal && fileType == 1) {
            setFlow("REMOTE");
            handleCloseModalProvider("IS_PROCESS_STATION_PRESCRIPTION");
            handleCloseModalProvider("DUPLICATE_MEDICATION_CHECK");
            setTimeout(() => {
              handleOpenModal("IS_PROCESS_STATION_PRESCRIPTION");
            }, 1000);
          } else {
            setFlow("LOCAL");
            handleCloseModalProvider("DUPLICATE_MEDICATION_CHECK");
            handleOpenModal("IS_PROCESS_FLOW_LOCAL_PRESCRIPTION");
          }
          handleCloseModal();
        }
      },
      onError: (error) => {
        handleCloseModal();
        handleError({ error });
      },
    });
  };

  const dataCollapse = useMemo(() => {
    return [
      {
        title: "処方箋基本情報",
        data:
          dataSelected?.dispensingGroupDetailModels?.filter(
            (item) => item.groupTitle === "処方箋基本情報",
          ) || [],
      },
      {
        title: "医療機関情報",
        data:
          dataSelected?.dispensingGroupDetailModels?.filter(
            (item) => item.groupTitle === "医療機関情報",
          ) || [],
      },
      {
        title: "患者情報",
        data:
          dataSelected?.dispensingGroupDetailModels?.filter(
            (item) => item.groupTitle === "患者情報",
          ) || [],
      },
      {
        title: "保険情報",
        data:
          dataSelected?.dispensingGroupDetailModels?.filter(
            (item) => item.groupTitle === "保険情報",
          ) || [],
      },
    ];
  }, [dataSelected]);

  const renderContent = () => {
    if (prescriptionIdList.length > 0) {
      return (
        <ModalCancelPrescriptionHasChange
          ptId={String(id)}
          raiinNo={String(raiinNo)}
          sinDate={Number(sinDate)}
          prescriptionIdList={prescriptionIdList}
          openModalCancelPrescriptionHasChange={!!prescriptionIdList.length}
          handleCloseModal={() => {
            setPrescriptionIdList([]);
            handleCloseModal();
          }}
        />
      );
    }

    return (
      <StyledModal
        centered
        title="処方内容"
        width={1080}
        isOpen={isOpen}
        onCancel={handleCloseModal}
        footer={[
          <Button
            key={"cancel"}
            varient="tertiary"
            onClick={handleCloseModal}
            disabled={loading}
          >
            キャンセル
          </Button>,
          <GroupBtn>
            <Button
              key={"unregister"}
              varient="inline"
              onClick={handleUnRegister}
              disabled={loading}
            >
              処方箋情報を登録しない
            </Button>
            <Button
              key={"ok"}
              varient="primary"
              onClick={handleSubmit}
              loading={loading}
            >
              登録
            </Button>
          </GroupBtn>,
        ]}
      >
        <Wrapper>
          <StyledTabs
            defaultActiveKey={data?.refill ? "1" : data?.refill2 ? "2" : "3"}
            onChange={onChangeTabs}
          >
            {data?.refill ? (
              <TabPane key="1" tab="リフィルなし">
                <TabPanelContent
                  dataCollapse={dataCollapse}
                  dataSelected={dataSelected}
                  toggleCollapse={toggleCollapse}
                  handleToggleCollapse={handleToggleCollapse}
                />
              </TabPane>
            ) : null}
            {data?.refill2 ? (
              <TabPane key="2" tab="リフィル2回">
                <TabPanelContent
                  dataCollapse={dataCollapse}
                  dataSelected={dataSelected}
                  toggleCollapse={toggleCollapse}
                  handleToggleCollapse={handleToggleCollapse}
                />
              </TabPane>
            ) : null}

            {data?.refill3 ? (
              <TabPane key="3" tab="リフィル3回">
                <TabPanelContent
                  dataCollapse={dataCollapse}
                  dataSelected={dataSelected}
                  toggleCollapse={toggleCollapse}
                  handleToggleCollapse={handleToggleCollapse}
                />
              </TabPane>
            ) : null}
          </StyledTabs>
        </Wrapper>
      </StyledModal>
    );
  };

  if (!data || isLoading) {
    return (
      <StyledModal
        title={showModalError ? "エラー" : "処理中"}
        $errorModal={showModalError}
        width={480}
        isOpen={isShowModalLoading || showModalError}
        centerFooterContent
        onCancel={handleCloseModalNoData}
        footer={[
          <Button
            key={"cancel"}
            varient="tertiary"
            onClick={handleCloseModalNoData}
            disabled={showModalError ? false : disabledBtn}
          >
            キャンセル
          </Button>,
        ]}
      >
        {showModalError ? (
          <ContentErrorWapper>
            <SvgIconError />
            <Title style={{ fontSize: 24, textAlign: "center" }}>
              処方箋情報の作成に失敗しました
            </Title>
            <div style={{ width: "100%" }}>処方データが見つかりません。</div>
          </ContentErrorWapper>
        ) : (
          <ModalContentWrapper>
            <span>処方箋情報CSVを作成しています</span>
            <Spin size="large" />
          </ModalContentWrapper>
        )}

        <ConfirmButton
          varient="inline"
          onClick={handleConfirm}
          disabled={showModalError ? false : disabledBtn}
        >
          処方箋情報を登録しない
        </ConfirmButton>
      </StyledModal>
    );
  }

  return (
    <>
      {renderContent()}
      <StyledModal
        title={"処方箋発行形態の確認"}
        width={480}
        isOpen={isShowModalConfirm}
        onCancel={() => setIsShowModalConfirm(false)}
        footer={[
          <Button
            key={"cancel"}
            varient="tertiary"
            onClick={() => setIsShowModalConfirm(false)}
          >
            戻る
          </Button>,
          <Button key={"ok"} varient="primary" onClick={handleConfirm}>
            発行
          </Button>,
        ]}
      >
        <ModalContentConfirm>
          <Title level={2} style={{ fontSize: 24 }}>
            処方箋情報の登録が完了していません
          </Title>
          <Text>
            処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？
          </Text>
        </ModalContentConfirm>
      </StyledModal>
    </>
  );
};
