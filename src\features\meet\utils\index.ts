import { useEffect, useRef } from "react";

import { <PERSON><PERSON><PERSON>abe<PERSON> } from "amazon-chime-sdk-component-library-react";
import { MeetingSessionConfiguration } from "amazon-chime-sdk-js";
import dayjs from "dayjs";

import { AttendeeType, MeetingMode, MeetingStatus } from "@/constants/meeting";
import { formatHHmm } from "@/utils/datetime-format";

import type { Patient } from "@/apis/gql/generated/types";
import type { MeetingManager } from "amazon-chime-sdk-component-library-react";
import type { AttendeeResponse } from "amazon-chime-sdk-component-library-react/lib/providers/MeetingProvider/types";
import type { JoinMeetingInfo } from "@/types/meeting";

export const getMeetingAttendee = async (
  patient: Patient | undefined,
  doctor: string,
  externalUserId?: string,
): Promise<AttendeeResponse> => {
  const [attendeeType, _] = externalUserId?.includes("_")
    ? externalUserId.split("_")
    : [];

  if (Number(attendeeType) === AttendeeType.PATIENT) {
    return Promise.resolve({
      name: patient ? patient.patientName || patient.patientNameKana : "患者",
    });
  }

  return Promise.resolve({
    name: doctor,
  });
};

export const setUpMeeting = async (
  meetingId: string,
  joinInformation: JoinMeetingInfo,
  meetingManager: MeetingManager,
  setMeetingId: (meetingId: string) => void,
  setJoinInfo: (joinInformation: JoinMeetingInfo) => void,
  setMeetingMode: (mode: MeetingMode) => void,
) => {
  setMeetingId(meetingId);
  setJoinInfo(joinInformation);

  const staff = joinInformation.Attendees.find(({ ExternalUserId }) => {
    const [attendeeType, _] = ExternalUserId.includes("_")
      ? ExternalUserId.split("_")
      : [];

    return Number(attendeeType) === AttendeeType.CLINIC;
  });

  const meetingSessionConfiguration = new MeetingSessionConfiguration(
    joinInformation.Meeting,
    staff,
  );

  await meetingManager.join(meetingSessionConfiguration, {
    deviceLabels: DeviceLabels.AudioAndVideo,
    enableWebAudio: true,
  });

  setMeetingMode(MeetingMode.Attendee);

  await meetingManager.start();
};

export const useMemoCompare = <T>(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  next: any,
  compare: (prev: T | undefined, next: T | undefined) => boolean,
): T => {
  const previousRef = useRef<T>();
  const previous = previousRef.current;
  const isComponentEqual = compare(previous, next);

  useEffect(() => {
    if (!isComponentEqual) {
      previousRef.current = next;
    }
  });
  return isComponentEqual ? previous : next;
};

export const isPatientJoinedMeeting = (meetingStatus: MeetingStatus) => {
  return [MeetingStatus.PATIENT_JOINED, MeetingStatus.BOTH_JOINED].includes(
    meetingStatus,
  );
};

/** 服薬指導希望日時タイプ */
const DesiredType = {
  /** 午前 */
  Morning: 1,

  /** 時間指定 */
  Specified: 2,

  /** 指定なし */
  Unspecified: 3,
};

export const formatDesiredDate = (desiredDate: string, desiredType: number) => {
  switch (desiredType) {
    case DesiredType.Unspecified:
      return "時間指定なし";
    case DesiredType.Morning:
    case DesiredType.Specified:
      return `${formatHHmm(desiredDate)}〜${formatHHmm(dayjs(desiredDate).add(1, "h").toDate())}`;
    default:
      return "";
  }
};

export const getAttendeeType = (
  externalUserId?: string,
): number | undefined => {
  const EXTERNAL_USER_ID_SEPARATOR = "_";

  if (!externalUserId?.includes(EXTERNAL_USER_ID_SEPARATOR)) return undefined;

  const [attendeeType] = externalUserId.split(EXTERNAL_USER_ID_SEPARATOR);
  return Number(attendeeType);
};

export const isClinic = (type?: number) => type === AttendeeType.CLINIC;
export const isPatient = (type?: number) => type === AttendeeType.PATIENT;
export const isShortTimeOperation = (updatedAt: string) => {
  return dayjs(updatedAt).isAfter(dayjs().subtract(1, "minute"));
};
