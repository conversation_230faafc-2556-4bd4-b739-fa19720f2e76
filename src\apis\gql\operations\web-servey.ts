import { gql } from "@/apis/gql/apollo-client";

export const GET_SURVEY_BY_SECRET = gql`
  query getSurveyBySecret($secret: String!) {
    getSurveyBySecret(secret: $secret) {
      name
      fQues<PERSON><PERSON>
      clinicName
    }
  }
`;

export const GET_SURVEY_ANSWER_NO_PATIENT_UPLOAD_FILE_URLs = gql`
  query getSurveyAnswerNoPatientUploadFileURLs(
    $input: SurveyAnswerNoPatientUploadFileURLsReq!
  ) {
    getSurveyAnswerNoPatientUploadFileURLs(input: $input) {
      fileName
      uploadUrl
      s3Key
    }
  }
`;

export const CREATE_SURVEY_ANSWER_NO_PATIENT = gql`
  mutation createSurveyAnswerNoPatient(
    $input: SurveyAnswerNoPatientCreateReq!
  ) {
    createSurveyAnswerNoPatient(input: $input)
  }
`;
