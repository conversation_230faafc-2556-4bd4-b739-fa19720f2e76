import { useCallback, useRef, useState } from "react";

import { useE<PERSON>r<PERSON>and<PERSON> } from "@/hooks/useErrorHandler";
import { System } from "@/utils/socket-helper";
import { createXmlFileBatchOnlineCheckRequestValidity } from "@/utils/socket-helper/onlineQualification";
import {
  CONFIRMATION_FILE_NAME,
  SystemHub,
  SystemScreenCode,
} from "@/constants/confirm-online";
import { usePostApiOnlineProcessXmloqSmuquc01resMutation } from "@/apis/gql/operations/__generated__/batch-online";
import { RenderIf } from "@/utils/common/render-if";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { useCheckConctionAgent } from "@/hooks/useCheckConctionAgent";

import { BatchOnlineErrorModal } from "../../../ui/modals/BatchOnlineCheck/BatchOnlineErrorModal";
import { BatchOnlineLoadingModal } from "../../../ui/modals/BatchOnlineCheck/BatchOnlineLoadingModal";

type ValidateState = {
  type: "success" | "error";
  content: {
    messageTitle: string;
    messageContent: string;
  };
};

export const useCheckRequestValidity = (
  onClose: () => void,
  onCloseModalRequest: () => void,
) => {
  const [isLoading, setLoading] = useState<boolean>(false);
  const waitingOQController = useRef<AbortController>();
  const [openValidateModal, setOpenValidateModal] = useState<ValidateState>();
  const { handleError } = useErrorHandler();
  const { notification } = useGlobalNotification();

  const { checkConnection } = useCheckConctionAgent();

  const [postApiOnlineProcessXMLOQSmuquc01res] =
    usePostApiOnlineProcessXmloqSmuquc01resMutation({
      onError: (error) => handleError({ error }),
    });

  const handleResponseFile = async (xmlString: string, date: string) => {
    postApiOnlineProcessXMLOQSmuquc01res({
      variables: {
        payload: {
          batchConfirmationType: 1,
          yoyakuDate: Number(date),
          sinYm: 0,
          xmlString,
          consentFrom: 0,
          consentTo: 0,
          examinationFrom: 0,
          examinationTo: 0,
        },
      },
      onCompleted: (data) => {
        if (data.postApiOnlineProcessXMLOQSmuquc01res?.status === 0) {
          onClose();
          onCloseModalRequest();
        } else {
          notification.error({
            message: data.postApiOnlineProcessXMLOQSmuquc01res?.message,
          });
        }
      },
    });
  };

  const handleSendFileToAgentType1 = async (
    yoyakuDate: string,
    listPatient: unknown,
  ) => {
    try {
      const isConnected = await checkConnection();
      if (!isConnected) {
        return;
      }

      setLoading(true);
      waitingOQController.current?.abort();
      waitingOQController.current = new AbortController();

      const { fileName, msg } = createXmlFileBatchOnlineCheckRequestValidity(
        yoyakuDate,
        listPatient,
      );

      const res = await onlineVisiting.createFile(
        msg.XmlMsg,
        fileName,
        { signal: waitingOQController.current?.signal },
        "CreateXmlFile",
      );

      if (!res || waitingOQController.current.signal.aborted) return;
      await onlineVisiting.moveFile({ files: [res.data.fileName] });
      if (res.data.fileName.includes(CONFIRMATION_FILE_NAME.REQUEST_VALIDITY)) {
        const requestFileName = res.data.fileName.replace("err", "xml");
        await onlineVisiting.moveFile({ files: [requestFileName] });
        const match = res.data.content.match(/\[(.*?)\] (.*)/);
        if (match) {
          const processingResultCode = match[1];
          const errorMessage = match[2];
          setOpenValidateModal({
            type: "error",
            content: {
              messageTitle: "オンライン資格確認に失敗しました",
              messageContent: `処理結果コード: ${processingResultCode}\n${errorMessage}`,
            },
          });
          return;
        }
      }

      if (res.data?.content) handleResponseFile(res.data.content, yoyakuDate);
    } catch (err) {
      if (err instanceof Error) {
        setOpenValidateModal({
          type: "error",
          content: {
            messageTitle: "オンライン資格確認に失敗しました",
            messageContent: "タイムアウト",
          },
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const ValidateCancelRequestComponentType1 = useCallback(() => {
    return (
      <>
        <RenderIf condition={openValidateModal?.type === "error"}>
          <BatchOnlineErrorModal
            onClose={() => setOpenValidateModal(undefined)}
            content={openValidateModal?.content}
          />
        </RenderIf>
        <RenderIf condition={isLoading}>
          <BatchOnlineLoadingModal
            content={{
              title: "処理中",
              heading: "オンライン資格確認システムからの結果を待っています",
            }}
            onClose={() => {
              setLoading(false);
              waitingOQController.current?.abort();
            }}
          />
        </RenderIf>
      </>
    );
  }, [isLoading, openValidateModal?.content, openValidateModal?.type]);

  return {
    isLoading,
    handleSendFileToAgentType1,
    ValidateCancelRequestComponentType1,
  };
};

class OnlineVisiting extends System {
  constructor() {
    super(SystemHub.PatientInf, [{}], {
      screenCode: SystemScreenCode.PatientInfo,
    });
  }
}

const onlineVisiting = new OnlineVisiting();
