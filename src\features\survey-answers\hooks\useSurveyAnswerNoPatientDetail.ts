import { useRouter } from "next/router";

import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGetSurveyAnswerNoPatientByIdQuery } from "@/apis/gql/operations/__generated__/survey-answer";

export const useSurveyAnswerNoPatientDetail = (
  selectedSurveyAnswerId: string,
) => {
  const { isReady } = useRouter();
  const { handleError } = useErrorHandler();

  const {
    data: getSurveyAnswerNoPatientById,
    refetch: refetchSurveyAnswerNoPatientById,
  } = useGetSurveyAnswerNoPatientByIdQuery({
    skip: !isReady || !selectedSurveyAnswerId,
    variables: { id: selectedSurveyAnswerId },
    onError: (error) => handleError({ error }),
  });

  return {
    surveyAnswerDetail:
      getSurveyAnswerNoPatientById?.getSurveyAnswerNoPatientById,
    refetchSurveyAnswerNoPatientById,
  };
};
