import Head from "next/head";
import { useRouter } from "next/router";

type MetaProps = {
  title: string;
  description: string;
  keywords?: string;
  canonical?: string;
  viewport?: string;
};

function Meta({
  title,
  description,
  keywords,
  viewport = "width=device-width,initial-scale=1",
}: MetaProps) {
  const router = useRouter();

  return (
    <Head>
      <meta charSet="UTF-8" key="charset" />
      <meta name="viewport" content={viewport} key="viewport" />
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="noindex, nofollow" />
      <link
        rel="apple-touch-icon"
        href={`${router.basePath}/apple-touch-icon.png`}
        key="apple"
      />
      <link
        rel="icon"
        sizes="32x32"
        href={`${router.basePath}/favicon.ico`}
        key="icon32"
      />
      <link
        rel="icon"
        sizes="16x16"
        href={`${router.basePath}/favicon.ico`}
        key="icon16"
      />
      <link rel="icon" href={`${router.basePath}/favicon.ico`} key="favicon" />
    </Head>
  );
}

export { Meta };
