import { useUpdateReservationMutation } from "@/apis/gql/operations/__generated__/reservation";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import type { ReservationUpdateInput } from "@/apis/gql/generated/types";

export const useUpdateReservation = () => {
  const { handleError } = useErrorHandler();

  const defaultRefetchQueries = [
    "getReservationDetailById",
    "getExamTimeSlotsByConditions",
    "getReservationDetailsByConditions",
  ];

  const [updateReservation, { loading }] = useUpdateReservationMutation();

  const handleUpdateReservation = (
    reserveId: number,
    reserveDetailId: number,
    calendarId: number,
    input: ReservationUpdateInput,
    resendMessage?: boolean,
    refetchQueries?: string[],
    onSuccess?: () => void,
  ) => {
    updateReservation({
      variables: {
        calendarId,
        reserveId,
        reserveDetailId,
        input,
        resendMessage,
      },
      onError: (error) => {
        handleError({ error, commonMessage: "予約の更新に失敗しました" });
      },
      onCompleted: onSuccess,
      refetchQueries: [...defaultRefetchQueries, ...(refetchQueries ?? [])],
    });
  };

  return { loading, handleUpdateReservation };
};
