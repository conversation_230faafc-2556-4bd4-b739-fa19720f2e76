import styled from "styled-components";

import { ContentLoading } from "@/components/ui/ContentLoading";

import { useGetPortalHospital } from "../../hooks/useGetPortalHospital";
import { usePublicHospitalInfo } from "../../hooks/usePublicHospitalInfo";
import { PortalHeader } from "../PortalHeader";

import { PortalInfoForm } from "./PortalInfoForm";

const PageWrapper = styled.div`
  width: 100%;
  position: relative;
`;

const Wrapper = styled.div`
  padding: 20px;
  font-size: 14px;
  height: calc(100% - 110px - 52px); // ヘッダーとフッターの高さ
  overflow-y: auto;
`;

export const PortalInfo = () => {
  const {
    loading,
    hospital,
    tags,
    examination,
    specialist,
    hasError,
    isGettingScuelData,
    scuelData,
  } = useGetPortalHospital();

  const { hospital: hospitalInfo, loading: hospitalInfoLoading } =
    usePublicHospitalInfo({
      skip: loading,
    });

  if (loading || isGettingScuelData || hospitalInfoLoading) {
    return <ContentLoading />;
  }

  if (
    hasError ||
    tags === null ||
    typeof tags === "undefined" ||
    examination === null ||
    typeof examination === "undefined" ||
    specialist === null ||
    typeof specialist === "undefined"
  ) {
    return null;
  }

  // 新規作成時の初期データ
  const initialHospitalData = {
    hospitalId: 0, // 新規作成時は hospitalId が発行されていないので無効値を設定
    name: hospitalInfo?.isOpenClinic ? hospitalInfo.name : "",
    postCode: "",
    telephone: hospitalInfo?.isOpenClinic ? hospitalInfo.telephone : "",
    address1: "",
    isCarpark: false,
    paymentDetails: "",
    descriptionTitle: "",
    description: "",
    accessDetail: "",
    homePage: "",
    isActive: false,
    tags: [],
    businessTimes: [],
    examinations: [],
    specialists: [],
    directorName: "",
  };

  return (
    <PageWrapper>
      <PortalHeader
        activeKey="INFO"
        hpStatus={hospitalInfo?.status}
        hospital={hospital}
      />
      <Wrapper>
        <PortalInfoForm
          hospital={hospital ?? initialHospitalData}
          tags={tags}
          examination={examination}
          specialist={specialist}
          scuelData={scuelData?.getImportDataByHpId}
        />
      </Wrapper>
    </PageWrapper>
  );
};
