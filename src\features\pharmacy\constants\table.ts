import dayjs from "dayjs";

import { SortOrder } from "@/apis/gql/generated/types";

import type { SearchType } from "../types/table";

/** 服薬指導希望日時ステータス */
export const DesiredDateStatus = {
  /** 確認中 */
  Confirm: "1",

  /** 設定前 */
  NotSet: "2",

  /** 設定済 */
  Set: "3",
} as const;

/** 服薬指導希望日時タイプ */
export const DesiredType = {
  /** 午前 */
  Morning: "1",

  /** 時間指定 */
  Specified: "2",

  /** 指定なし */
  Unspecified: "3",
} as const;

/** ステータス */
export const Status = {
  /** 処方箋未着 */
  PrescriptionUnarrived: "1",

  /** 処方箋到着 */
  PrescriptionArrived: "2",

  /** 在庫なし */
  NoStock: "3",

  /** 在庫確認済 */
  StockChecked: "4",

  /** 梱包済 */
  Packed: "5",

  /** 発送済 */
  Sent: "6",

  /** キャンセル */
  Cancelled: "7",
} as const;

export const STATUS_LABEL = [
  {
    value: Status.PrescriptionUnarrived,
    label: "処方箋未着",
  },
  {
    value: Status.PrescriptionArrived,
    label: "処方箋到着",
  },
  {
    value: Status.StockChecked,
    label: "在庫確認済",
  },
  {
    value: Status.NoStock,
    label: "在庫なし",
  },
  {
    value: Status.Packed,
    label: "梱包済",
  },
  {
    value: Status.Sent,
    label: "発送済",
  },
  {
    value: Status.Cancelled,
    label: "キャンセル",
  },
];

/** キャンセルステータス */
const StatusCancelType = {
  /** 患者キャンセル（診療予約） */
  cancelPatientAppointment: "1",

  /** 患者キャンセル（薬局変更） */
  cancelPharmacyChange: "2",

  /** クリニックキャンセル（診療予約） */
  cancelClinicAppointment: "3",

  /** 薬局キャンセル（服薬指導予約） */
  cancelPharmacyCounseling: "4",
} as const;

export const STATUS_CANCEL_TYPE_LABEL = [
  {
    value: StatusCancelType.cancelPatientAppointment,
    label: "患者キャンセル（診療予約）",
  },
  {
    value: StatusCancelType.cancelPharmacyChange,
    label: "患者キャンセル（薬局変更）",
  },
  {
    value: StatusCancelType.cancelClinicAppointment,
    label: "クリニックキャンセル（診療予約）",
  },
  {
    value: StatusCancelType.cancelPharmacyCounseling,
    label: "薬局キャンセル（服薬指導予約）",
  },
];

/** 服薬指導ステータス */
export const GuidanceStatus = {
  /** 指導前 */
  BeforeGuidance: "1",

  /** 未指導 */
  GuidanceUnfinished: "2",

  /** 指導済 */
  GuidanceFinished: "3",
} as const;

export const GUIDANCE_STATUS_LABEL = [
  {
    value: GuidanceStatus.BeforeGuidance,
    label: "指導前",
  },
  {
    value: GuidanceStatus.GuidanceUnfinished,
    label: "未指導",
  },
  {
    value: GuidanceStatus.GuidanceFinished,
    label: "指導済",
  },
];

/** 会計ステータス */
export const PaymentStatus = {
  /** 会計前 */
  BeforePayment: "1",

  /** 会計済 */
  PaymentDone: "2",

  /** 決済エラー */
  PaymentError: "3",
} as const;

/** CSVステータス */
export const CsvStatus = {
  /** 出力前 */
  BeforeExport: "1",

  /** 取込前 */
  BeforeImport: "2",

  /** 取込済 */
  AfterImport: "3",
} as const;

/** SMSステータス */
export const SmsStatus = {
  /** 未通知 */
  NotNotified: "1",

  /** 通知済 */
  Notified: "2",
} as const;

/** ビデオ通話ステータス */
export const VideocallStatus = {
  /** 未入室 */
  NotEntered: "1",

  /** 患者入室済 */
  PatientEntered: "2",

  /** スタッフ入室済 */
  StaffEntered: "3",

  /** 通話中 */
  Calling: "4",
} as const;

/** 郵送サービス種別 */
export const PostalService = {
  /** ゆうパケット */
  YuPacket: "1",

  /** ゆうパック */
  YuPack: "2",
} as const;

/** ステータス検索パラメータ */
export const StatusInput = {
  ...Status,
  /** 指定なし */
  None: "",
} as const;

/** 服薬指導ステータス検索パラメータ */
export const GuidanceStatusInput = {
  ...GuidanceStatus,
  /** 指定なし */
  None: "",
} as const;

/** 会計ステータス検索パラメータ */
export const PaymentStatusInput = {
  ...PaymentStatus,
  /** 指定なし */
  None: "",
} as const;

/** CSVステータス検索パラメータ */
export const CsvStatusInput = {
  ...CsvStatus,
  /** 指定なし */
  None: "",
} as const;

/** 未来日ステータス */
export const ShowFutureTreatment = {
  /** 未来日表示 */
  Future: true,

  /** 未来日非表示 */
  NotFuture: false,
} as const;

/** 受付一覧 初期検索パラメータ */
export const defaultSearchParams: SearchType = {
  displayDate: dayjs().hour(0).minute(0).second(0).millisecond(0),
  pharmacyDesiredDate: SortOrder.Ascend,
  pharmacyReserveUpdateDate: undefined,
  reserveTime: SortOrder.Ascend,
  status: StatusInput.None,
  guidanceStatus: GuidanceStatusInput.None,
  showFutureTreatment: ShowFutureTreatment.NotFuture,
  paymentStatus: PaymentStatusInput.None,
  csvStatus: CsvStatusInput.None,
  customer: undefined,
  patient: undefined,
  clinic: undefined,
};
