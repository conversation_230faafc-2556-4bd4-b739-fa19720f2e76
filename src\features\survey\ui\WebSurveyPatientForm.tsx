import dayjs from "dayjs";
import { Controller, useForm } from "react-hook-form";
import styled from "styled-components";

import { ErrorText } from "@/components/ui/ErrorText";
import { SvgIconLabelExample } from "@/components/ui/Icon/IconLabelExample";
import { Button } from "@/components/ui/NewButton";
import { TextInput } from "@/components/ui/TextInput";
import { FURIGANA_REGEXP } from "@/constants/validation";

/**
 * Web問診票：患者さま情報の入力フォーム
 *
 * バリデーションのソースコードは、クリニックマップの次のコードをベースに作成しています。
 * @see {@link https://github.com/bizleap-healthcare/clinic-reservation/blob/main/user-client/src/components/molecules/input/NameKanjiInput.tsx}
 * @see {@link https://github.com/bizleap-healthcare/clinic-reservation/blob/main/user-client/src/components/molecules/input/NameKatakanaInput.tsx}
 */

// 漢字、英字、ひらがな、カタカナ
export const japaneseNameRegex =
  /^[\p{Script=Hiragana}\p{Script=Katakana}\p{Script=Han}ー・\p{Script=Latin}\s]+$/u;
// スペース
const REGEX_STRING_SPACE = /[\s\u3000\u00A0]/g;

export type WebSurveyPatientFormType = {
  name: string;
  kanaName: string;
  birthDate: string;
};

type Props = {
  data: WebSurveyPatientFormType;
  onSubmit: (data: WebSurveyPatientFormType) => void;
};

export const WebSurveyPatientForm = (props: Props) => {
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<WebSurveyPatientFormType>({
    defaultValues: props.data,
  });

  return (
    <Wrapper>
      <StyledTitle>患者さま情報の入力</StyledTitle>
      <InputFields>
        <InputLabel title="お名前（全角）" example="鈴木 一郎" />
        <Controller
          name="name"
          control={control}
          render={({ field }) => (
            <TextInput {...field} shouldTrim hasError={!!errors.name} />
          )}
          rules={{
            required: "お名前を入力してください。",
            pattern: {
              value: japaneseNameRegex,
              message:
                "お名前は漢字、英字、ひらがな、カタカナで入力してください。",
            },
            validate: (value) => {
              const valueStr = value.trim();
              const maxLength = 100;
              // スペースを除去した文字の長さ
              const sanitizedLength = valueStr.replace(
                REGEX_STRING_SPACE,
                "",
              ).length;

              if (sanitizedLength > maxLength) {
                return "100文字以内で入力してください。";
              }

              return true;
            },
          }}
        />
        {errors.name && (
          <StyledErrorText>{errors.name.message}</StyledErrorText>
        )}

        <InputLabel title="フリガナ（全角）" example="スズキ イチロウ" />
        <Controller
          name="kanaName"
          control={control}
          render={({ field }) => (
            <TextInput {...field} shouldTrim hasError={!!errors.kanaName} />
          )}
          rules={{
            required: "フリガナを入力してください。",
            pattern: {
              value: FURIGANA_REGEXP,
              message: "カタカナで入力してください。",
            },
            validate: (value) => {
              const valueStr = value.trim();
              const maxLength = 100;
              // スペースを除去した文字の長さ
              const sanitizedLength = valueStr.replace(
                REGEX_STRING_SPACE,
                "",
              ).length;

              if (sanitizedLength > maxLength) {
                return "100文字以内で入力してください。";
              }

              return true;
            },
          }}
        />
        {errors.kanaName && (
          <StyledErrorText>{errors.kanaName.message}</StyledErrorText>
        )}

        <InputLabel title="生年月日" example="19800101" />
        <Controller
          name="birthDate"
          control={control}
          render={({ field }) => (
            <TextInput
              {...field}
              shouldTrim
              hasError={!!errors.birthDate}
              style={{ width: "50%" }}
            />
          )}
          rules={{
            required: "生年月日を入力してください。",
            validate: (value) => {
              const valueStr = value.trim();
              // スペースを除去した文字の長さ
              const sanitizedLength = valueStr.replace(
                REGEX_STRING_SPACE,
                "",
              ).length;

              if (sanitizedLength != 8) {
                return "生年月日は8桁で入力してください。";
              }

              if (!dayjs(value, "YYYYMMDD", true).isValid()) {
                return "生年月日は正しい日付で入力してください。";
              }

              return true;
            },
          }}
        />
        {errors.birthDate && (
          <StyledErrorText>{errors.birthDate.message}</StyledErrorText>
        )}
      </InputFields>
      <ButtonWrapper>
        <StyledButton
          htmlType="button"
          onClick={handleSubmit(props.onSubmit)}
          varient="secondary"
        >
          次へ
        </StyledButton>
      </ButtonWrapper>
    </Wrapper>
  );
};

const InputLabel = ({ title, example }: { title: string; example: string }) => {
  return (
    <InputLabelContainer>
      <div>{title}</div>
      <InputLabelRequire>必須</InputLabelRequire>
      <SvgIconLabelExample />
      <div>{example}</div>
    </InputLabelContainer>
  );
};

const Wrapper = styled.div`
  width: 640px;
  display: flex;
  flex-direction: column;
  align-items: start;
  padding: 40px;
  background-color: #fff;
  border-radius: 26px;
  margin-top: 40px;

  @media (max-width: 600px) {
    width: 100%;
    height: 100%;
    padding: 20px;
    border-radius: unset;
    margin-top: unset;
  }
`;

const StyledTitle = styled.div`
  font-family: "NotoSansJP";
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
  color: "#243544";
  padding-bottom: 20px;
  border-bottom: 4px solid #e2e3e5;
  width: 100%;

  @media (max-width: 600px) {
    text-align: center;
  }
`;

const InputFields = styled.div`
  width: 50%;

  @media (max-width: 600px) {
    width: 100%;
  }
`;

const StyledErrorText = styled(ErrorText)`
  margin-top: 8px;
`;

const ButtonWrapper = styled.div`
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 20px;
  border-top: solid 1px #e2e3e5;
  padding-top: 20px;
`;

const StyledButton = styled(Button)`
  height: 36px;
  width: 120px;
`;

const InputLabelContainer = styled.div`
  display: flex;
  flex-direction: row;
  font-size: 14px;
  color: #243544;
  column-gap: 4px;
  margin-top: 20px;
  margin-bottom: 8px;
`;

const InputLabelRequire = styled.div`
  font-size: 11px;
  color: #e74c3c;
  border: solid 1px #e74c3c;
  border-radius: 2px;
  width: 36px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
`;
