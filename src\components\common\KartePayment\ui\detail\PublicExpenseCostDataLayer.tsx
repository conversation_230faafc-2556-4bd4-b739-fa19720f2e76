import { FormProvider, useForm } from "react-hook-form";

import { PublicExpenseMaxCostModal } from "@/components/common/Patient/AddPatient/MaxMoneyModal/PublicExpenseMaxCostModal";
import { usePostApiReceptionSaveMaxMoneyDataMutation } from "@/apis/gql/operations/__generated__/payment-detail";
import { useErrorHandler } from "@/hooks/useErrorHandler";


import { generatePublicExpenseFormData } from "../../utils/accounting-detail";
import { useAccountingQuery } from "../../providers/AccountingQueryProvider";

import type { DataTableMaxMoneyItem } from "@/types/patient";
import type dayjs from "dayjs";
import type {
  DomainModelsInsuranceKohiInfModel,
  DomainModelsMaxMoneyLimitListModelInput,
} from "@/apis/gql/generated/types";
import type { RegistrationPublicExpenseInforForm } from "@/types/insurance";

type Props = {
  data: DomainModelsInsuranceKohiInfModel;
};
export const PublicExpenseCostDataLayer: React.FC<Props> = ({ data }) => {
  const { handleError } = useErrorHandler();
  const formData = generatePublicExpenseFormData(data);

  const selectedPublicExpense = {
    dayLimitFutan: data.hokenMstModel?.dayLimitFutan || 0,
    displayTextMaster: data.hokenMstModel?.displayTextMaster || "",
    endDate: data.hokenMstModel?.endDate || 0,
    futanRate: data.hokenMstModel?.futanRate || 0,
    hokenEdaNo: data.hokenMstModel?.hokenEdaNo || 0,
    hokenName: data.hokenMstModel?.hokenName || "",
    hokenNameCd: data.hokenMstModel?.hokenNameCd || "",
    hokenNo: data.hokenMstModel?.hokenNo || 0,
    houbetu: data.hokenMstModel?.houbetu || "",
    isFutansyaNoCheck: data.hokenMstModel?.isFutansyaNoCheck || 0,
    isJyukyusyaNoCheck: data.hokenMstModel?.isJyukyusyaNoCheck || 0,
    isLimitList: data.hokenMstModel?.isLimitList || 0,
    isTokusyuNoCheck: data.hokenMstModel?.isTokusyuNoCheck || 0,
    jyuKyuCheckDigit: data.hokenMstModel?.jyuKyuCheckDigit || 0,
    kaiLimitFutan: data.hokenMstModel?.kaiLimitFutan || 0,
    monthLimitFutan: data.hokenMstModel?.monthLimitFutan || 0,
    selectedValueMaster: data.hokenMstModel?.selectedValueMaster || "",
    startDate: data.hokenMstModel?.startDate || 0,
    isLimitListSum: data.hokenMstModel?.isLimitListSum || 0,
  };

  const { ptId } = useAccountingQuery();

  const methods = useForm<RegistrationPublicExpenseInforForm>({
    mode: "onSubmit",
    defaultValues: formData,
  });

  const [saveMaxMoneyData] = usePostApiReceptionSaveMaxMoneyDataMutation();

  const saveLimitList = async (
    limitList: Omit<DataTableMaxMoneyItem, "__typename">[],
    slectedMonth: dayjs.Dayjs,
  ): Promise<void> => {
    if (!limitList.length) {
      return;
    }
    const litmitListData: DomainModelsMaxMoneyLimitListModelInput[] =
      limitList.map(({ hasError, isModify, key, ...rest }) => rest);

    await saveMaxMoneyData({
      variables: {
        input: {
          ptId: ptId,
          kohiId: data.hokenId || 0,
          sinYm: Number(slectedMonth.format("YYYYMM")),
          listLimits: litmitListData,
        },
      },
      onError: (error) => {
        handleError({
          error,
          commonMessage: "公費上限額の保存に失敗しました。",
        });
      },
    });
  };

  return (
    <FormProvider {...methods}>
      <PublicExpenseMaxCostModal
        saveLimitList={saveLimitList}
        selectedPublicExpense={selectedPublicExpense}
        ptId={ptId}
      />
    </FormProvider>
  );
};
