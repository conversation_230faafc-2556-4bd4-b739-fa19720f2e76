import { useMemo } from "react";

import styled from "styled-components";

import { useSession } from "@/hooks/useSession";
import { calcAge } from "@/utils/date-helper";
import {
  formatYYYYMDWithJapaneseEra,
  formatYYYYMDWithJapansesBirthdate,
} from "@/utils/datetime-format";
import { formatPhoneNumber } from "@/utils/phonenumber-format";

import { Tooltip } from "../tooltip/Tooltip";

import type {
  Patient,
  PortalCustomer,
  ReservationDetail,
} from "@/apis/gql/generated/types";

type Props = {
  patient?: Patient;
  customer?: PortalCustomer;
  familyReservations?: ReservationDetail[];
};

const Wrapper = styled.div`
  background-color: rgba(255, 255, 255, 0.1);
  height: 106px;
  width: 296px;
  font-size: 18px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 12px;
  gap: 10px;
  border-radius: 12px;
  line-height: 1;
`;

const InfoWrapper = styled.div`
  display: flex;
  gap: 18px;
`;

const AgeWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const FamilyInfoWrapper = styled.div`
  &:not(:last-child) {
    margin-bottom: 4px;
  }
`;

export const AttendeeInfo: React.FC<Props> = ({
  patient,
  customer,
  familyReservations,
}) => {
  const {
    session: { isPharmacy },
  } = useSession();

  const personInfo = useMemo(() => {
    if (isPharmacy && customer) {
      return { ...customer, birthdate: customer.birthday };
    }

    if (patient) {
      return {
        ...patient,
        telephone: patient.phoneNumber1 || patient.phoneNumber2,
      };
    }

    return null;
  }, [isPharmacy, customer, patient]);

  const renderInfo = (person: {
    birthdate?: string;
    gender?: number;
    telephone?: string;
  }) => (
    <>
      {person.birthdate && (
        <p>{`${formatYYYYMDWithJapaneseEra(person.birthdate)}生`}</p>
      )}
      <InfoWrapper>
        <p>{person.gender === 1 ? "男性" : "女性"}</p>
        <AgeWrapper>
          <p>{person.birthdate && ` ${calcAge(person.birthdate).year}歳`}</p>
          {!!familyReservations?.length && (
            <Tooltip
              title={
                <>
                  <p>家族情報</p>
                  {familyReservations?.map((reservation) => (
                    <FamilyInfoWrapper key={reservation.reserveDetailId}>
                      <p>{reservation.patient?.patientName}</p>
                      <p>
                        {`${
                          reservation.patient?.birthdate &&
                          formatYYYYMDWithJapansesBirthdate(
                            reservation.patient.birthdate,
                          )
                        }　
                       ${reservation.patient?.gender === 1 ? "男性" : "女性"}　
                       ${
                         reservation.patient?.birthdate &&
                         ` ${calcAge(reservation.patient.birthdate).year}歳`
                       }`}
                      </p>
                    </FamilyInfoWrapper>
                  ))}
                </>
              }
            />
          )}
        </AgeWrapper>
      </InfoWrapper>
      <p>
        TEL: {person.telephone ? formatPhoneNumber(person.telephone) : "未設定"}
      </p>
    </>
  );

  if (!personInfo) return null;

  return <Wrapper>{renderInfo(personInfo)}</Wrapper>;
};
