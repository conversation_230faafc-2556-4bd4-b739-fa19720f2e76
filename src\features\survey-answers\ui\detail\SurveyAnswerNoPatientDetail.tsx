import React from "react";

import styled from "styled-components";

import { useClipboardCopy } from "@/features/survey-answers/hooks/useClipboardCopy";
import {
  formatYYYYMMDDddHHmmWithJapanese,
  formatYYYYMMDDWithJapanese,
} from "@/utils/datetime-format";

import { useSurveyAnswerNoPatientDetail } from "../../hooks/useSurveyAnswerNoPatientDetail";
import { getCustomAnswersKeyValues } from "../../utils/survey";

import { CustomAnswers } from "./CustomAnswers";

const SurveyAnswerDetailWrapper = styled.div`
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  min-width: 0;
  background: #fbfcfe;
  font-family: "NotoSansJP";
  border-radius: 12px 0 0 0;
`;

const DetailHeader = styled.div`
  display: flex;
  flex-direction: row;
  padding: 12px 12px;
  border-bottom: 1px solid #e2e3e5;
  gap: 0;
`;

const DetailRow = styled.div`
  display: inline-block;
  width: fit-content;
  max-width: 100%;
  min-width: 15%;
  word-break: break-all;
  & + & {
    margin-left: 8px;
  }
`;

const DetailLabel = styled.div`
  margin-bottom: 4px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #6a757d;
`;

const DetailValue = styled.div`
  font-size: 14px;
  font-weight: bold;
  white-space: normal;
  overflow: hidden;
`;

const DetailBody = styled.div`
  height: 100%;
  background: #e0e6ec;
  padding: 24px;
  overflow-y: auto;
`;

const CopyButtonWrapper = styled.div`
  height: 52px;
  min-height: 52px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-grow: 0;
  background-color: #fbfcfe;
`;

const CopyButton = styled.button`
  margin: 12px auto 12px;
  display: block;
  border: 1px solid #e2e3e5;
  background: #fbfcfe;
  border-radius: 18px;
  padding: 6px 24px;
  color: #6a757d;
  cursor: pointer;
  width: 200px;
  height: 36px;
  font-size: 14px;
  font-weight: bold;
`;

type Props = {
  selectedSurveyAnswerId: string;
};

export const SurveyAnswerNoPatientDetail = ({
  selectedSurveyAnswerId,
}: Props) => {
  const { surveyAnswerDetail } = useSurveyAnswerNoPatientDetail(
    selectedSurveyAnswerId,
  );

  const { copyToClipboard } = useClipboardCopy();

  return (
    <SurveyAnswerDetailWrapper>
      <DetailHeader>
        <DetailRow>
          <DetailLabel>回答日時</DetailLabel>
          <DetailValue>
            {surveyAnswerDetail?.createdAt
              ? formatYYYYMMDDddHHmmWithJapanese(surveyAnswerDetail.createdAt)
              : ""}
          </DetailValue>
        </DetailRow>
        <DetailRow>
          <DetailLabel>問診票</DetailLabel>
          <DetailValue>{surveyAnswerDetail?.surveyName || ""}</DetailValue>
        </DetailRow>
        <DetailRow>
          <DetailLabel>氏名(カナ)</DetailLabel>
          <DetailValue>
            {surveyAnswerDetail?.name && surveyAnswerDetail?.kanaName
              ? `${surveyAnswerDetail.name}(${surveyAnswerDetail.kanaName})`
              : ""}
          </DetailValue>
        </DetailRow>
        <DetailRow>
          <DetailLabel>生年月日</DetailLabel>
          <DetailValue>
            {surveyAnswerDetail?.birthday
              ? formatYYYYMMDDWithJapanese(
                  surveyAnswerDetail.birthday.toString(),
                )
              : ""}
          </DetailValue>
        </DetailRow>
      </DetailHeader>
      <DetailBody>
        <CustomAnswers
          answers={JSON.parse(surveyAnswerDetail?.surveyAnswer || "[]")}
          files={surveyAnswerDetail?.files || []}
        />
      </DetailBody>
      <CopyButtonWrapper>
        {surveyAnswerDetail && (
          <CopyButton
            onClick={() =>
              copyToClipboard(
                getCustomAnswersKeyValues(
                  JSON.parse(surveyAnswerDetail?.surveyAnswer || "[]"),
                ),
              )
            }
          >
            回答をコピー
          </CopyButton>
        )}
      </CopyButtonWrapper>
    </SurveyAnswerDetailWrapper>
  );
};
