import "antd/dist/reset.css";
import "../styles/global.css";
// import "../utils/common/wdyr";

import { ApolloProvider } from "@apollo/client";
import { GoogleTagManager } from "@next/third-parties/google";
import dayjs from "dayjs";
import ja from "dayjs/locale/ja";
import customParseFormat from "dayjs/plugin/customParseFormat";
import isBetween from "dayjs/plugin/isBetween";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isToday from "dayjs/plugin/isToday";
import isYesterday from "dayjs/plugin/isYesterday";

import { gqlClient } from "@/apis/gql/apollo-client";
import { Layout } from "@/components/common/RootLayout";
import { AntdConfig } from "@/components/functional/AntdConfig";
import { AdIdsManager } from "@/hooks/useAdIds";
import { GlobalNotificationProvider } from "@/hooks/useGlobalNotification";
import { SessionProvider } from "@/hooks/useSession";

import type { AppProps } from "next/app";

dayjs.extend(customParseFormat);
dayjs.extend(isToday);
dayjs.extend(isYesterday);
dayjs.extend(isBetween);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.locale({
  ...ja,
  weekStart: 0, // Start week from Sunday
});

function DenkaruApp({ Component, pageProps }: AppProps) {
  return (
    <>
      {process.env.NEXT_PUBLIC_ENV === "prd" && (
        <GoogleTagManager gtmId="GTM-KZJ9HJMK" />
      )}
      <AdIdsManager />

      <GlobalNotificationProvider>
        <ApolloProvider client={gqlClient}>
          <AntdConfig>
            <SessionProvider>
              <Layout>
                <Component {...pageProps} />
              </Layout>
            </SessionProvider>
          </AntdConfig>
        </ApolloProvider>
      </GlobalNotificationProvider>
    </>
  );
}

export default DenkaruApp;
