import { styled } from "styled-components";

import {
  InfiniteScrollTable,
  TableDataCell,
} from "@/components/common/InfiniteScrollTable";
import { formatMMDDHHmmWithJapanese } from "@/utils/datetime-format";

import { useSurveyAnswerNoPatientTable } from "../../hooks/useSurveyAnswerNoPatientTable";

import type { TableColumn } from "@/components/common/InfiniteScrollTable";
import type {
  SurveyAnswerNoPatient,
  SurveyRes,
} from "@/apis/gql/generated/types";

const StyledTable = styled(InfiniteScrollTable<SurveyAnswerNoPatient>)`
  width: 100%;
  th {
    height: 48px;
  }
  tbody td {
    border: 1px solid #e2e3e5;
    font-size: 14px;
    vertical-align: middle;
    height: 52px;
  }
`;

const CustomTableRow = styled.tr<{ $isSelected: boolean; $clickable: boolean }>`
  background-color: ${({ $isSelected }) =>
    $isSelected ? "#eaf0f5" : "#ffffff"};
  cursor: ${({ $clickable }) => ($clickable ? "pointer" : "auto")};
  &:hover {
    background-color: ${"#eaf0f5"};
  }
`;

const CopyButton = styled.button`
  width: 80px;
  display: block;
  border: 1px solid #e2e3e5;
  background: #fbfcfe;
  border-radius: 18px;
  padding: 6px 19px 8px;
  color: #6a757d;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  white-space: nowrap;
`;

type Props = {
  isLoadingMore: boolean;
  selectedDate: string;
  selectedSurvey: SurveyRes | undefined;
  searchKeyword: string;
  selectedSurveyAnswerId: string;
  setSelectedSurveyAnswerId: (id: string) => void;
  refetchSurveyAnswerNoPatientById: () => void;
};

export const SurveyAnswerNoPatientListTable = ({
  isLoadingMore,
  selectedDate,
  selectedSurvey,
  searchKeyword,
  selectedSurveyAnswerId,
  setSelectedSurveyAnswerId,
  refetchSurveyAnswerNoPatientById,
}: Props) => {
  const columns: TableColumn[] = [
    {
      title: "回答日時",
      dataIndex: "createdAt",
      width: "104px",
      sortable: true,
    },
    {
      title: "問診票",
      dataIndex: "surveyName",
      width: "320px",
      sortable: true,
    },
    {
      title: "氏名(カナ)",
      dataIndex: "name",
      width: "276px",
    },
    {
      title: "回答",
      dataIndex: "action",
      width: "100px",
    },
  ];

  const handleLoadMore = (lastItem?: SurveyAnswerNoPatient) => {
    if (!lastItem) return;
    const idCursor = String(lastItem.surveyAnswerNoPatientId ?? "");
    const dateCursor: string | undefined = undefined;
    handleLoadMoreSurveyAnswers(idCursor, dateCursor);
  };

  const {
    handleSelectSurveyAnswer,
    handleLoadMoreSurveyAnswers,
    handleSortChange,
    surveyAnswerNoPatients,
    sortField,
    sortOrder,
    handleSurveyAnswerClipboardCopy,
  } = useSurveyAnswerNoPatientTable(
    selectedDate,
    selectedSurvey,
    searchKeyword,
    selectedSurveyAnswerId,
    setSelectedSurveyAnswerId,
    refetchSurveyAnswerNoPatientById,
  );

  return (
    <StyledTable
      isLoadingMore={isLoadingMore}
      style={{ height: window.innerHeight - 300 }}
      data={surveyAnswerNoPatients ?? []}
      columns={columns}
      onSort={(field, order) => handleSortChange(field, order)}
      sortField={sortField}
      sortOrder={sortOrder ?? undefined}
      onLoadMore={() =>
        surveyAnswerNoPatients &&
        handleLoadMore(
          surveyAnswerNoPatients[surveyAnswerNoPatients.length - 1],
        )
      }
      customTableRow={({ item, ...props }) => {
        const isSelected =
          selectedSurveyAnswerId === String(item.surveyAnswerNoPatientId ?? "");
        return (
          <CustomTableRow
            {...props}
            $isSelected={isSelected}
            $clickable={true}
            onClick={() =>
              handleSelectSurveyAnswer(
                String(item.surveyAnswerNoPatientId ?? ""),
              )
            }
          />
        );
      }}
      itemContent={(
        _,
        { createdAt, surveyName, name, kanaName, surveyAnswerNoPatientId },
      ) => (
        <>
          <TableDataCell align="center">
            {createdAt ? formatMMDDHHmmWithJapanese(createdAt) : ""}
          </TableDataCell>
          <TableDataCell>{surveyName}</TableDataCell>
          <TableDataCell>
            {name}
            {kanaName ? `(${kanaName})` : ""}
          </TableDataCell>
          <TableDataCell>
            <CopyButton
              onClick={(e) => {
                e.stopPropagation();
                handleSurveyAnswerClipboardCopy(surveyAnswerNoPatientId ?? "");
              }}
            >
              コピー
            </CopyButton>
          </TableDataCell>
        </>
      )}
    />
  );
};
