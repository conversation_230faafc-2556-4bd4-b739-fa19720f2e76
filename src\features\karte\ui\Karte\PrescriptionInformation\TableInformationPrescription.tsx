import { useState } from "react";

import { Dropdown } from "antd";
import dayjs from "dayjs";
import { useRouter } from "next/router";

import { SvgIconArrowRight } from "@/components/ui/Icon/IconArrowRight";
import { SvgIconMore } from "@/components/ui/Icon/IconMore";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { CustomDropdown } from "@/features/karte/ui/Karte/MedicineOrder/styles";
import { NoDataListPrescription } from "@/features/karte/ui/Karte/PrescriptionInformation/NoDataListPrescription";
import { UserGender } from "@/features/karte/ui/Karte/PrescriptionInformation/UserGender";
import { WrapInformationGender } from "@/features/karte/ui/Karte/PrescriptionInformation/style";
import {
  renderEpsPrescriptionDeletedReason,
  renderEpsPrescriptionIssueType,
  renderEpsPrescriptionMessageFlgOpenByDoctor,
  renderEpsPrescriptionRefillCount,
  renderEpsPrescriptionResultType,
  renderEpsPrescriptionStatus,
} from "@/features/karte/ui/Karte/PrescriptionInformation/utils";
import { StyledTable } from "@/features/karte/ui/SimpleKarte/ExamResults/style";
import { useHeaderLogic } from "@/hooks/useHeaderLogic";
import { RenderIf } from "@/utils/common/render-if";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useGetApiSystemConfGetListLazyQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { usePostApiEpsGetPreRegistrationDataMutation } from "@/apis/gql/operations/__generated__/pre_registration";
import { usePostApiEpsSavePrescriptionInfoMutation } from "@/apis/gql/operations/__generated__/duplicate-medication";
import { usePostApiEpsUpsertDispensingInfMutation } from "@/apis/gql/operations/__generated__/prescription-medicine";
import { checkConnectionSocket } from "@/utils/socket-helper/connection";
import { Loading } from "@/components/ui/Loading";

import { ModalInformationPrescriptionDetail } from "./ModalInformationPrescriptionDetail";
import { ModalCancelPrescription } from "./ModalCancelPrescription";
import { ModalUpdateDispensingResult } from "./ModalUpdateDispensingResult";

import type { MenuProps, TableColumnsType } from "antd";
import type {
  DomainModelsEpsDispensingEpsDispensingModel,
  DomainModelsEpsEpsPrescriptionInfModel,
  DomainModelsEpsPrescriptionEpsPrescriptionModel,
  EmrCloudApiResponsesSystemConfSystemConfDto,
} from "@/apis/gql/generated/types";

type Props = {
  data: DomainModelsEpsEpsPrescriptionInfModel[];
  loading: boolean;
  setOpenModal: (open: string) => void;
  setModal: (modal: string) => void;
  setCancelReason: (cancelReason: string) => void;
  setDispensingTimes: (dispensingTimes: number[]) => void;
  setBase64Dispensing: (base64Dispensing: string[]) => void;
  setBase64Prescription: (base64Prescription: string) => void;
  setRefillCount: (refillCount: number) => void;
};

export enum MODAL_TYPE {
  HAVE_PRESCRIPTION = "havePrescription",
  HAVE_NOT_PRESCRIPTION = "haveNotPrescription",
  LOADING = "loading",
  ERROR = "error",
  DONE = "done",
}

export function TableInformationPrescription({
  data,
  loading,
  setOpenModal,
  setModal,
  setCancelReason,
  setDispensingTimes,
  setBase64Dispensing,
  setBase64Prescription,
  setRefillCount,
}: Props) {
  const { staffInfo } = useHeaderLogic();

  const [modalCancelPrescription, setModalCancelPrescription] =
    useState<MODAL_TYPE>();

  const [modalUpdateDispensingResult, setModalUpdateDispensingResult] =
    useState<MODAL_TYPE>();

  const [numberOfRegistered, setNumberOfRegistered] = useState<number>(0);

  const items = [
    {
      key: "1",
      type: "item",
      label: "詳細",
    },
    {
      key: "2",
      type: "item",
      label: "処方内容",
    },
    {
      key: "3",
      type: "item",
      label: "調剤結果",
      disabled: (record: DomainModelsEpsEpsPrescriptionInfModel) => {
        const cancelStatuses = [1, 3];
        return cancelStatuses.includes(record?.epsPrescription?.status ?? 0);
      },
    },
    {
      key: "4",
      type: "item",
      label: "調剤結果更新",
      disabled: (record: DomainModelsEpsEpsPrescriptionInfModel) => {
        const cancelStatuses = [1, 3];
        return cancelStatuses.includes(record?.epsPrescription?.status ?? 0);
      },
    },
    {
      key: "5",
      type: "item",
      label: "処方箋取消",
      disabled: (record: DomainModelsEpsEpsPrescriptionInfModel) => {
        const cancelStatuses = [0, 1];
        return !cancelStatuses.includes(record?.epsPrescription?.status ?? 0);
      },
      danger: true,
    },
  ];

  const columns: TableColumnsType = [
    {
      title: "患者番号",
      align: "center",
      width: "115px",
      render: (_, record) => <div>{record?.ptInf?.ptNum}</div>,
    },
    {
      title: "氏名",
      align: "center",
      width: "160px",
      onCell: () => ({
        style: {
          whiteSpace: "normal",
          wordWrap: "break-word",
          wordBreak: "break-word",
        },
      }),
      render: (_, record) => (
        <div>
          <p>{record?.ptInf?.name}</p>
          <WrapInformationGender>
            {record?.ptInf?.kanaName}
            <UserGender sex={record?.ptInf?.sex ?? 1} />
          </WrapInformationGender>
        </div>
      ),
    },
    {
      title: "診療日",
      align: "center",
      render: (_, record) => (
        <div>
          {record?.epsPrescription?.sinDate
            ? dayjs(
                String(record?.epsPrescription?.sinDate),
                "YYYYMMDD",
              ).format("YYYY/MM/DD")
            : ""}
        </div>
      ),
    },
    {
      title: "診療科",
      align: "center",
      width: "260px",
      onCell: () => ({
        style: {
          whiteSpace: "normal",
          wordWrap: "break-word",
          wordBreak: "break-word",
        },
      }),
      render: (_, record) => (
        <div style={{ textAlign: "left", marginLeft: "10px" }}>
          {record?.kaSName}
        </div>
      ),
    },
    {
      title: "リフィル",
      align: "center",
      render: (_, record) => (
        <div>
          {renderEpsPrescriptionRefillCount(
            record?.epsPrescription?.refileCount ?? null,
            record?.epsDispensing?.dispensingTimes ?? null,
          )}
        </div>
      ),
    },
    {
      title: "発行形態",
      align: "center",
      render: (_, record) => (
        <div>
          {renderEpsPrescriptionIssueType(record?.epsPrescription?.issueType)}
        </div>
      ),
    },
    {
      title: "処方箋状態",
      align: "center",
      render: (_, record) => (
        <div>
          {renderEpsPrescriptionStatus(record?.epsPrescription?.status)}
        </div>
      ),
    },
    {
      title: "取消理由",
      align: "center",
      render: (_, record) => (
        <div>
          {renderEpsPrescriptionDeletedReason(
            record?.epsPrescription?.deletedReason,
          )}
        </div>
      ),
    },
    {
      title: "調剤状態",
      align: "center",
      render: (_, record) => (
        <div>
          {renderEpsPrescriptionResultType(record?.epsDispensing?.resultType)}
        </div>
      ),
    },
    {
      title: "調剤日",
      align: "center",
      render: (_, record) => (
        <div>
          {record?.epsDispensing?.dispensingDate
            ? dayjs(
                String(record?.epsDispensing?.dispensingDate),
                "YYYYMMDD",
              ).format("YYYY/MM/DD")
            : ""}
        </div>
      ),
    },
    {
      title: "伝達事項",
      align: "center",
      render: (_, record) => (
        <div>
          {renderEpsPrescriptionMessageFlgOpenByDoctor(
            record?.epsDispensing?.messageFlg,
          )}
        </div>
      ),
    },
    {
      title: "",
      align: "center",
      width: "40px",
      render: (_, record) => (
        <div>
          <Dropdown
            trigger={["click"]}
            placement="bottomRight"
            menu={{
              items: items.map((item) => ({
                ...item,
                disabled:
                  typeof item.disabled === "function"
                    ? item.disabled(record)
                    : item.disabled,
              })) as MenuProps["items"],
              expandIcon: <SvgIconArrowRight />,
              onClick: (e) => {
                handleClickItem(e.key, record);
              },
            }}
            dropdownRender={(menu) => {
              return <CustomDropdown>{menu}</CustomDropdown>;
            }}
          >
            <SvgIconMore />
          </Dropdown>
        </div>
      ),
    },
  ];

  const {
    state: { isOpenInforPrescriptionDetail },
    handleOpenModal,
  } = useModal();

  const {
    query: { id },
  } = useRouter();

  const [disableCancel, setDisableCancel] = useState(true);
  const [prescriptionIdList, setPrescriptionIdList] = useState<
    DomainModelsEpsPrescriptionEpsPrescriptionModel[] | undefined
  >([]);
  const [systemConf, setSystemConf] = useState<
    EmrCloudApiResponsesSystemConfSystemConfDto[] | undefined
  >();

  const [isLoading, setIsLoading] = useState(false);

  const { handleError } = useErrorHandler();

  const [getDataSystemConf] = useGetApiSystemConfGetListLazyQuery({
    onError: (error) => {
      handleError({ error });
    },
  });

  const [getPreRegistrationData] = usePostApiEpsGetPreRegistrationDataMutation({
    onError: (error) => {
      handleError({ error });
    },
  });

  const [updateStatusPrescription] = usePostApiEpsSavePrescriptionInfoMutation({
    onError: (error) => {
      handleError({ error });
    },
  });
  const [postApiEpsUpsertDispensingInf] =
    usePostApiEpsUpsertDispensingInfMutation({
      onError: (error) => {
        handleError({ error });
      },
    });

  const [rowSelected, setRowSelected] =
    useState<DomainModelsEpsEpsPrescriptionInfModel>();
  const handleUpdateDispensingResult = (
    record: DomainModelsEpsEpsPrescriptionInfModel,
  ) => {
    setModalUpdateDispensingResult(MODAL_TYPE.LOADING);
    setRowSelected(record);
  };

  const onCancelPrescriptionIdList = () => {
    setModalCancelPrescription(MODAL_TYPE.LOADING);
    getDataSystemConf({
      onError: (error) => {
        handleError({ error });
        setModalCancelPrescription(undefined);
      },
    }).then((res) => {
      setSystemConf(res?.data?.getApiSystemConfGetList?.data?.systemConfList);
      const dataSystemConfFiltered =
        res?.data?.getApiSystemConfGetList?.data?.systemConfList?.find(
          (item) => item.grpCd === 100040 && item.grpEdaNo === 4,
        );
      const timeout = Number(dataSystemConfFiltered?.val) * 1000;
      if (timeout) {
        setTimeout(() => {
          setDisableCancel(false);
        }, timeout);
      }
    });
    getPreRegistrationData({
      variables: {
        input: {
          odrInfs: [],
          statusList: [0, 1, 2],
          raiinNo: rowSelected?.epsPrescription?.raiinNo,
          ptId: String(id),
        },
      },
      onError: (error) => {
        handleError({ error });
        setModalCancelPrescription(undefined);
      },
    }).then((res) => {
      const epsPrescriptionModel =
        res.data?.postApiEpsGetPreRegistrationData?.data
          ?.preRegistrationCheckingModel?.epsPrescriptionModel;
      const prescriptionInfos = epsPrescriptionModel?.map((item) => ({
        bango: item?.bango,
        deletedReason: 5,
        edaNo: item?.edaNo,
        hokensyaNo: item?.hokensyaNo,
        issueType: item?.issueType,
        kigo: item?.kigo,
        kohiFutansyaNo: item?.kohiFutansyaNo,
        kohiJyukyusyaNo: item?.kohiJyukyusyaNo,
        prescriptionDocument: item?.prescriptionDocument,
        ptId: item?.ptId,
        raiinNo: item?.raiinNo,
        refileCount: item?.refileCount,
        seqNo: item?.seqNo,
        sinDate: item?.sinDate,
        status: 2,
        accessCode: item?.accessCode,
        prescriptionId: item?.prescriptionId,
      }));
      updateStatusPrescription({
        variables: {
          input: {
            prescriptionInfos,
          },
        },
        onError: (error) => {
          handleError({ error });
          setModalCancelPrescription(undefined);
        },
      });
      setPrescriptionIdList(epsPrescriptionModel);
    });
  };

  const handlePrescriptionCancel = (
    record: DomainModelsEpsEpsPrescriptionInfModel,
    data: DomainModelsEpsEpsPrescriptionInfModel[],
  ) => {
    const resultType = record.epsDispensing?.resultType;
    const status = record.epsPrescription?.status;
    if (
      (resultType === 1 || resultType === 3 || resultType === 4) &&
      status !== 3
    ) {
      setModalCancelPrescription(MODAL_TYPE.HAVE_PRESCRIPTION);
    }

    if (resultType !== 1 && resultType !== 3 && resultType !== 4) {
      const numRegistered =
        data.filter((item) => item.epsPrescription?.status === 0)?.length || 0;
      if (numRegistered > 1 && data.length > 1) {
        setModalCancelPrescription(MODAL_TYPE.HAVE_NOT_PRESCRIPTION);
      } else {
        onCancelPrescriptionIdList();
      }
    }
  };

  const handleClickItem = async (
    key: string,
    record: DomainModelsEpsEpsPrescriptionInfModel,
  ) => {
    switch (key) {
      case "1":
        setRowSelected(record);
        handleOpenModal("INFOR_PRESCRIPTION_DETAIL");
        break;
      case "2": {
        const { epsPrescription } = record;
        if (!epsPrescription?.prescriptionDocument) {
          setModal("error");
        } else {
          setBase64Prescription(epsPrescription?.prescriptionDocument ?? "");
          setRefillCount(epsPrescription?.refileCount ?? 0);
          setOpenModal("PRESCRIPTION_MEDICINE");
        }
        break;
      }
      case "3":
        {
          const { epsDispensing } = record;

          if (!epsDispensing) {
            setIsLoading(true);
            await checkConnectionSocket({
              onSuccess: () => handleUpdateDispensingResult(record),
              onError: () => handleOpenModal("CONNECTION_SOCKET"),
            });
            setIsLoading(false);
            return;
          }

          if (staffInfo?.staffType === 1) {
            const epsDispensingNew: DomainModelsEpsDispensingEpsDispensingModel =
              {
                ...epsDispensing,
                messageFlg: 3,
              };
            postApiEpsUpsertDispensingInf({
              variables: {
                input: [epsDispensingNew],
              },
              onError: (error) => {
                handleError({ error });
              },
            });
          }

          const { resultType } = epsDispensing;
          if (resultType === 1) {
            setBase64Dispensing([epsDispensing?.dispensingDocument ?? ""]);
            setDispensingTimes([epsDispensing?.dispensingTimes ?? 1]);
            setOpenModal("DISPENSING_INFO");
          }
          if (resultType !== 1 && resultType !== 3) {
            setIsLoading(true);
            await checkConnectionSocket({
              onSuccess: () => handleUpdateDispensingResult(record),
              onError: () => handleOpenModal("CONNECTION_SOCKET"),
            });
            setIsLoading(false);
          }
          if (resultType === 3) {
            setCancelReason(epsDispensing?.cancelReason ?? "");
            setOpenModal("PrescriptionRecall");
          }
        }
        break;
      case "4":
        setIsLoading(true);
        await checkConnectionSocket({
          onSuccess: () => handleUpdateDispensingResult(record),
          onError: () => handleOpenModal("CONNECTION_SOCKET"),
        });
        setIsLoading(false);
        break;
      case "5":
        {
          const numRegistered =
            data.filter((item) => item.epsPrescription?.status === 0)?.length ||
            0;
          setNumberOfRegistered(numRegistered);
          setRowSelected(record);
          setIsLoading(true);
          await checkConnectionSocket({
            onSuccess: () => handlePrescriptionCancel(record, data),
            onError: () => handleOpenModal("CONNECTION_SOCKET"),
          });
          setIsLoading(false);
        }
        break;
      default:
        break;
    }
  };

  return (
    <>
      <RenderIf condition={isLoading}>
        <Loading isLoading={isLoading} />
      </RenderIf>
      <StyledTable
        dataSource={data || []}
        columns={columns}
        loading={loading}
        pagination={false}
        tableLayout={"fixed"}
        scroll={{ y: 310 }}
        style={{ width: "100%" }}
        locale={{
          emptyText: () => <NoDataListPrescription />,
        }}
      />
      <RenderIf condition={isOpenInforPrescriptionDetail}>
        <ModalInformationPrescriptionDetail data={rowSelected!} />
      </RenderIf>
      <RenderIf condition={!!modalCancelPrescription}>
        <ModalCancelPrescription
          prescriptionIdList={prescriptionIdList}
          systemConf={systemConf}
          disableCancel={disableCancel}
          submit={onCancelPrescriptionIdList}
          numberOfRegistered={numberOfRegistered}
          type={modalCancelPrescription}
          setModalType={setModalCancelPrescription}
        />
      </RenderIf>
      <RenderIf condition={!!modalUpdateDispensingResult}>
        <ModalUpdateDispensingResult
          rowSelected={rowSelected}
          type={modalUpdateDispensingResult}
          setModalType={setModalUpdateDispensingResult}
          setOpenModal={setOpenModal}
          setBase64Dispensing={setBase64Dispensing}
          setDispensingTimes={setDispensingTimes}
        />
      </RenderIf>
    </>
  );
}
