import React from "react";

import styled from "styled-components";

import { But<PERSON> } from "@/components/ui/NewButton";

import { useSurveyAnswerNoPatientTemplate } from "../hooks/useSurveyAnswerNoPatientTemplate";

import { SurveyAnswerNoPatientDetail } from "./detail/SurveyAnswerNoPatientDetail";
import { SurveyAnswerNoPatientFilter } from "./filter/SurveyAnswerNoPatientFilter";
import { SurveyAnswerNoPatientListTable } from "./table/SurveyAnswerNoPatientListTable";

const Container = styled.div`
  flex: 1;
  height: 100%;
  min-height: 0;
  padding: 20px 0 0 20px;
  display: flex;
  gap: 20px;
  box-sizing: border-box;
`;

const HeaderWrapper = styled.span`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const SurveyManagementButtonWrapper = styled.div`
  display: flex;
  gap: 12px;
`;

const Title = styled.p`
  line-height: 20px;
  font-size: 20px;
  font-weight: bold;
`;

const SurveyAnswerListWrapper = styled.div`
  min-width: 800px;
  height: 100%;
  display: flex;
  flex-direction: column;
`;

export const SurveyAnswerNoPatientTemplate = () => {
  const {
    isFetchingMore,
    handleSettingSurveyRoute,
    selectedDate,
    selectedSurvey,
    searchKeyword,
    selectedSurveyAnswerId,
    setSelectedDate,
    setSelectedSurvey,
    setSearchKeyword,
    setSelectedSurveyAnswerId,
    refetchSurveyAnswerNoPatientById,
    refetchSurveyAnswerNoPatients,
  } = useSurveyAnswerNoPatientTemplate();

  return (
    <Container>
      <SurveyAnswerListWrapper>
        <HeaderWrapper>
          <Title>Web問診</Title>
          <SurveyManagementButtonWrapper>
            <Button varient="secondary" onClick={handleSettingSurveyRoute}>
              問診票管理
            </Button>
          </SurveyManagementButtonWrapper>
        </HeaderWrapper>
        <SurveyAnswerNoPatientFilter
          refetchSurveyAnswerNoPatients={refetchSurveyAnswerNoPatients}
          selectedDate={selectedDate}
          setSelectedDate={setSelectedDate}
          selectedSurvey={selectedSurvey}
          setSelectedSurvey={setSelectedSurvey}
          searchKeyword={searchKeyword}
          setSearchKeyword={setSearchKeyword}
          setSelectedSurveyAnswerId={setSelectedSurveyAnswerId}
        />
        <SurveyAnswerNoPatientListTable
          isLoadingMore={isFetchingMore}
          selectedDate={selectedDate}
          selectedSurvey={selectedSurvey}
          searchKeyword={searchKeyword}
          selectedSurveyAnswerId={selectedSurveyAnswerId}
          setSelectedSurveyAnswerId={setSelectedSurveyAnswerId}
          refetchSurveyAnswerNoPatientById={refetchSurveyAnswerNoPatientById}
        />
      </SurveyAnswerListWrapper>
      <SurveyAnswerNoPatientDetail
        selectedSurveyAnswerId={selectedSurveyAnswerId}
      />
    </Container>
  );
};
