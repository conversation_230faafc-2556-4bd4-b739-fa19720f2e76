import React from "react";

import { QRCode } from "antd";
import dayjs from "dayjs";
import { Controller } from "react-hook-form";
import styled from "styled-components";

import { ReservationDatePicker } from "@/components/common/Reservation/ReservationDatePicker";
import { SvgIconArrowLeft } from "@/components/ui/Icon/IconArrowLeft";
import { SvgIconArrowRight } from "@/components/ui/Icon/IconArrowRight";
import { SvgIconCalendar } from "@/components/ui/Icon/IconCalendar";
import { IconButton } from "@/components/ui/IconButton";
import { InputLabel } from "@/components/ui/InputLabel";
import { Button } from "@/components/ui/NewButton";
import { Pulldown } from "@/components/ui/Pulldown";
import { TextInput } from "@/components/ui/TextInput";

import { RELEASE_DATE } from "../../constant";
import { useSurveyAnswerNoPatientFilter } from "../../hooks/useSurveyAnswerNoPatientFilter";

import type { SurveyRes } from "@/apis/gql/generated/types";

const FilterContainer = styled.div`
  width: 100%;
  min-height: 112px;
  flex-grow: 0;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-top: 16px;
  margin-bottom: 16px;
  padding: 0;
`;

const LeftSection = styled.div`
  width: 508px;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FilterWrapper = styled.div`
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  height: 56px;
  flex-grow: 0;
`;

const SurveyPulldownWrapper = styled.div`
  width: 220px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  border-radius: 6px !important;
`;

const StyledPulldown = styled(Pulldown)`
  width: 220px;
  height: 32px;
  margin-top: 4px;
  .ant-select-selection-placeholder {
    color: #243544 !important;
  }
`;

const KeywordInputWrapper = styled.div`
  width: 280px;
  display: flex;
  flex-direction: column;
`;

const KeywordInput = styled(TextInput)`
  width: 280px;
  height: 36px;
  margin-top: 4px;
  border-radius: 6px !important;
`;

const DatePickerWrapper = styled.div`
  width: 280px;
  display: flex;
  align-items: center;
  min-width: 0;
  flex-shrink: 1;
`;

const DatePickerSection = styled(DatePickerWrapper)`
  width: 135px;
  text-align: center;
  font-size: 18px;
  color: #243544;
  gap: 8px;
  margin-left: 8px;
  border-radius: 6px !important;
`;

const TodayButton = styled(Button)`
  background: #fff !important;
  color: #243544 !important;
  border: 1px solid #e2e3e5 !important;
  border-radius: 6px !important;
  &:hover {
    background: #f5f5f5 !important;
    color: #243544 !important;
  }
  width: 60px;
  height: 36px;
`;

const ArrowButton = styled(IconButton)`
  min-width: 36px;
  max-width: 36px;
  height: 36px;
  background: #fff;
  color: #243544;
  border: 1px solid #e2e3e5;
  border-radius: 6px;
  &:hover {
    background: #f5f5f5 !important;
    color: #243544 !important;
  }
`;

const QRCodeWrapper = styled.div`
  width: 272px;
  height: 112px;
  flex-grow: 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background-color: #fff;
  max-height: 112px;
  min-height: 112px;
  max-width: 100%;
  min-width: 0;
`;

const QRCodeInfo = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 8px;
`;

const QRCodeStyle = styled(QRCode)`
  width: 88px !important;
  height: 88px !important;
`;

const QRCodeLabel = styled.div`
  font-size: 14px;
  font-weight: bold;
`;

const QRCodeButton = styled.button`
  width: 140px;
  height: 28px;
  background: #fff;
  color: #243544;
  border: 1px solid #e2e3e5;
  border-radius: 6px;
  padding: 4px 0;
  margin-bottom: 4px;
  cursor: pointer;
  font-size: 14px;
  background-color: #fbfcfe;
`;

const StyledDatePicker = styled(ReservationDatePicker)`
  width: 220px;
  height: 36px;
  font-family: inherit;
  input {
    font-weight: bold;
  }
`;

type Props = {
  refetchSurveyAnswerNoPatients?: () => void;
  selectedDate: string;
  setSelectedDate: (date: string) => void;
  selectedSurvey: SurveyRes | undefined;
  setSelectedSurvey: (survey: SurveyRes | undefined) => void;
  searchKeyword: string;
  setSearchKeyword: (keyword: string) => void;
  setSelectedSurveyAnswerId: (id: string) => void;
};

export const SurveyAnswerNoPatientFilter = ({
  refetchSurveyAnswerNoPatients,
  selectedDate,
  setSelectedDate,
  selectedSurvey,
  setSelectedSurvey,
  searchKeyword,
  setSearchKeyword,
  setSelectedSurveyAnswerId,
}: Props) => {
  const {
    handleFilterBySurvey,
    handleFilterByName,
    handleFilterByAnswerDate,
    goToCurrentDate,
    onChangeDate,
    surveys,
    control,
    handlePrintSurvey,
    handleUrlClipboardCopy,
  } = useSurveyAnswerNoPatientFilter({
    refetchSurveyAnswerNoPatients,
    selectedDate,
    setSelectedDate,
    selectedSurvey,
    setSelectedSurvey,
    searchKeyword,
    setSearchKeyword,
    setSelectedSurveyAnswerId,
  });

  return (
    <FilterContainer>
      <LeftSection>
        <FilterWrapper>
          <SurveyPulldownWrapper>
            <InputLabel label="問診票" />
            <StyledPulldown
              placeholder="すべて"
              value={selectedSurvey?.surveyId}
              onChange={(event) => {
                const survey = event
                  ? surveys?.find(
                      (s: SurveyRes) => s.surveyId === Number(event),
                    )
                  : undefined;
                handleFilterBySurvey(survey);
              }}
              options={[
                { label: "すべて", value: "" },
                ...(surveys?.map((survey: SurveyRes) => ({
                  label: survey.name,
                  value: survey.surveyId,
                })) || []),
              ]}
            />
          </SurveyPulldownWrapper>
          <KeywordInputWrapper>
            <InputLabel label="キーワード" />
            <Controller
              name="searchKeyword"
              control={control}
              render={({ field }) => (
                <KeywordInput
                  {...field}
                  placeholder=""
                  onChange={(e) => handleFilterByName(e.target.value)}
                />
              )}
            />
          </KeywordInputWrapper>
        </FilterWrapper>
        <DatePickerWrapper>
          <StyledDatePicker
            format="YYYY年MM月DD日(ddd)"
            suffixIcon={<SvgIconCalendar />}
            allowClear={false}
            value={dayjs(selectedDate)}
            onChange={(date) =>
              handleFilterByAnswerDate(date.format("YYYYMMDD"))
            }
            minDate={dayjs(RELEASE_DATE)}
            maxDate={dayjs()}
          />
          <DatePickerSection>
            <ArrowButton
              varient="square"
              icon={<SvgIconArrowLeft />}
              onClick={() => onChangeDate(-1)}
              disabled={dayjs(selectedDate).isSame(dayjs(RELEASE_DATE), "day")}
            />
            <TodayButton
              varient="standard-sr"
              onClick={() => goToCurrentDate()}
            >
              今日
            </TodayButton>
            <ArrowButton
              varient="square"
              icon={<SvgIconArrowRight />}
              onClick={() => onChangeDate(1)}
              disabled={dayjs(selectedDate).isSame(dayjs(), "day")}
            />
          </DatePickerSection>
        </DatePickerWrapper>
      </LeftSection>
      {selectedSurvey && selectedSurvey.isDeleted === 0 && (
        <QRCodeWrapper>
          <QRCodeStyle
            value={`${window.location.origin}/survey/${selectedSurvey.secret}`}
            bordered={false}
          />
          <QRCodeInfo>
            <QRCodeLabel>回答URL</QRCodeLabel>
            <QRCodeButton onClick={handlePrintSurvey}>印刷</QRCodeButton>
            <QRCodeButton
              onClick={() => handleUrlClipboardCopy(`${selectedSurvey.secret}`)}
            >
              回答URLをコピー
            </QRCodeButton>
          </QRCodeInfo>
        </QRCodeWrapper>
      )}
    </FilterContainer>
  );
};
