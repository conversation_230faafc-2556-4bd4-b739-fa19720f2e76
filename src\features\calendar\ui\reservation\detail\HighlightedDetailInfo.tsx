import dynamic from "next/dynamic";
import Link from "next/link";
import styled from "styled-components";

import { ErrorText as CommonErrorText } from "@/components/ui/ErrorText";
import {
  PharmacyPrescriptionReceiveMethod,
  ReservationStatus,
} from "@/constants/reservation";
import { useReservationMemo } from "@/features/calendar/hooks/useReservationMemo";
import { useCalendar } from "@/hooks/useCalendar";
import { PaymentModalProvider } from "@/hooks/usePaymentModalProvider";
import { getReservationTime } from "@/utils/reservation-helper";
import { TextInput } from "@/components/ui/TextInput";
import { GotoMedicalFrom } from "@/constants/renkei";

import { PrescriptionDeliveryAddress } from "./PrescriptionDeliveryAddress";
import { ReservationPayment } from "./ReservationPayment";
import { SurveyStatus } from "./SurveyStatus";

import type { ReservationUpdateInput } from "@/types/reservation";
import type { GetYakkyoku24InfoQuery } from "@/apis/gql/operations/__generated__/hospital";
import type { ReservationDetail } from "@/apis/gql/generated/types";
import type { ApolloError } from "@apollo/client";

const ReservationMeetingDetail = dynamic(() =>
  import("./meeting/ReservationMeetingDetail").then(
    (mod) => mod.ReservationMeetingDetail,
  ),
);

type Props = {
  reservation: ReservationDetail;
  reserveType: string;
  updateReservationDetail: (
    reservation: ReservationUpdateInput,
  ) => Promise<ApolloError | undefined>;
  yakkyoku24Info: GetYakkyoku24InfoQuery["getYakkyoku24Info"] | undefined;
};

const TreatmentTime = styled.p`
  font-size: 16px;
  line-height: 1;
`;

const PatientInfo = styled.div`
  font-size: 16px;
  font-weight: bold;
  line-height: 1;
  color: #007aff;
  margin-top: 4px;
  margin-bottom: 8px;
  > span {
    font-weight: normal;
  }
`;

const Label = styled.div`
  white-space: nowrap;
  display: flex;
  margin-right: 4px;
  color: #6a757d;
`;

const BasicInfoWrapper = styled.div`
  background-color: #f1f4f7;
  padding: 12px;
  line-height: 1;
`;

const BasicInfoItem = styled.div`
  display: flex;
  align-items: center;
  border-top: 1px solid #e2e3e5;
  padding: 8px 0;
  line-height: 1;

  &:last-child {
    padding-bottom: 0;
  }
`;

const BasicInfoValue = styled.div`
  display: flex;
  align-items: start;
  width: 100%;
  flex-direction: row;
  gap: 4px;
`;

const CustomTextFieldInput = styled.div`
  flex: 1;
`;

const ErrorText = styled(CommonErrorText)`
  margin-top: 4px;
`;

const FamilyReservationWrapper = styled.div`
  display: flex;
  background-color: #e0e6ec;
  padding: 8px;
  margin-bottom: 8px;
  gap: 2px;
`;

const FlexWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6px;
`;

const FamilyReservationName = styled.div``;

const StyledLink = styled(Link)`
  line-height: normal;
  text-decoration: none;
`;

export const HighlightedDetailInfo: React.FC<Props> = ({
  reservation,
  reserveType,
  updateReservationDetail,
  yakkyoku24Info,
}) => {
  const {
    state: { showReservationDetail },
  } = useCalendar();

  const { memo, onChangeMemo, isValidMemo } = useReservationMemo({
    reservation,
    updateReservationDetail,
    showReservationDetail,
  });

  const familyReservations =
    reservation.reservation.reservationDetails?.filter(
      ({ patient }) => patient?.patientID !== reservation.patient?.patientID,
    ) ?? [];

  return (
    <BasicInfoWrapper>
      <FlexWrapper>
        <TreatmentTime>
          {getReservationTime({
            examStartDate: new Date(reservation.examTimeSlot.examStartDate),
            examEndDate: new Date(reservation.examTimeSlot.examEndDate),
          })}
        </TreatmentTime>
      </FlexWrapper>
      <div>患者番号: {reservation.patient?.patientNumber}</div>
      <PatientInfo>
        <StyledLink
          href={`/karte/${reservation.patient?.patientID}?from=${GotoMedicalFrom.Other}`}
          target="_blank"
        >
          {reservation.patient?.patientName ||
            reservation.patient?.patientNameKana}
        </StyledLink>
      </PatientInfo>
      {familyReservations.length > 0 && (
        <FamilyReservationWrapper>
          <Label>家族予約:</Label>
          <FamilyReservationName>
            {familyReservations
              .map(
                ({ patient }) =>
                  patient?.patientName || patient?.patientNameKana,
              )
              .join("、")}
          </FamilyReservationName>
        </FamilyReservationWrapper>
      )}
      <BasicInfoItem>
        <BasicInfoValue>
          <Label>問診ステータス:</Label>
          <SurveyStatus isExisted={reservation.isSurveyAnswered} />
        </BasicInfoValue>
      </BasicInfoItem>
      {reserveType === "オンライン" && (
        <>
          {(reservation.reservation.prescriptionReceiveMethod ===
            PharmacyPrescriptionReceiveMethod.GMO24_PHARMACY || // 薬局24からの配送
            reservation.reservation.prescriptionReceiveMethod ===
              PharmacyPrescriptionReceiveMethod.CUSTOMER_SPECIFIED_PHARMACY) && ( // 顧客指定の薬局で受取の場合
            <PrescriptionDeliveryAddress
              prescriptionReceiveMethod={
                reservation.reservation.prescriptionReceiveMethod
              }
              portalCustomerPharmacy={
                reservation.reservation.portalCustomerPharmacy
              }
              yakkyoku24Info={yakkyoku24Info}
            />
          )}
          <ReservationMeetingDetail
            meetingId={Number(reservation.reservation.meeting?.meetingId)}
            reserveDetailId={reservation.reserveDetailId}
          />
        </>
      )}
      <BasicInfoItem>
        <Label>当日メモ</Label>
        <CustomTextFieldInput>
          <TextInput
            value={memo || ""}
            onChange={onChangeMemo}
            disabled={reservation.status === ReservationStatus.MEDICAL_EXAM_END}
            hasError={!isValidMemo}
          />
          {!isValidMemo && (
            <ErrorText>250文字以内で入力してください。</ErrorText>
          )}
        </CustomTextFieldInput>
      </BasicInfoItem>
      <PaymentModalProvider>
        <ReservationPayment reservation={reservation} />
      </PaymentModalProvider>
    </BasicInfoWrapper>
  );
};
