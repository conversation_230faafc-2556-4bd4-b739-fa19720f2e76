import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";

const Wrapper = styled.div`
  padding: 24px;
  height: 260px;
`;

const ModalContentTitle = styled.p`
  font-size: 24px;
  line-height: 24px;
  font-weight: bold;
  margin-bottom: 30px;
`;

type Props = {
  isOpen: boolean;
  onClose: () => void;
};

export const MailDeliveryConfirmModal: React.FC<Props> = ({
  isOpen,
  onClose,
}) => {
  return (
    <Modal
      isOpen={isOpen}
      title="メール配信設定"
      centerFooterContent
      onCancel={onClose}
      width={480}
      footer={[
        <Button key="cancel" varient="tertiary" onClick={onClose}>
          閉じる
        </Button>,
      ]}
    >
      <Wrapper>
        <ModalContentTitle>確認メールをお送りしました</ModalContentTitle>
        <p>
          ご登録いただいたメールアドレスに確認メールをお送りしました。
          確認メールに記載されているURLをクリックして、メールアドレスの変更を完了してください。
        </p>
      </Wrapper>
    </Modal>
  );
};
