import { useForm } from "react-hook-form";

import { useFindPharmacyReservesQuery } from "@/apis/gql/operations/__generated__/pharmacy-reserve";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { useAddAuditlogMutation } from "@/apis/gql/operations/__generated__/audit-log";
import { logger } from "@/utils/sentry-logger";

import { defaultSearchParams } from "../constants/table";

import type { FindPharmacyReservesInput } from "@/apis/gql/generated/types";
import type { SearchType } from "../types/table";

const defaultSearchInput: FindPharmacyReservesInput = {
  dateFilter: {
    displayDate: defaultSearchParams.displayDate.format(),
  },
  sort: {
    pharmacyDesiredDate: defaultSearchParams.pharmacyDesiredDate,
    pharmacyReserveUpdateDate: defaultSearchParams.pharmacyReserveUpdateDate,
    reserveTime: defaultSearchParams.reserveTime,
  },
  statusFilter: {},
  keywordFilter: {},
  showFutureTreatment: defaultSearchParams.showFutureTreatment,
};

export const useGetReceptions = () => {
  const { handleError } = useErrorHandler();

  const method = useForm<SearchType>({
    defaultValues: defaultSearchParams,
  });

  const {
    data,
    loading,
    refetch: apolloRefetch,
  } = useFindPharmacyReservesQuery({
    variables: {
      input: defaultSearchInput,
    },
    onError: (error) => {
      handleError({ error, commonMessage: "受付一覧の取得に失敗しました。" });
    },
    fetchPolicy: "no-cache",
  });

  const [addAuditLog] = useAddAuditlogMutation();
  const handleAuditLog = async (eventCd: string, hosoku: string) => {
    try {
      await addAuditLog({
        variables: {
          input: {
            eventCd,
            hosoku,
          },
        },
      });
    } catch (error) {
      logger({
        error,
        message: "failed to add audit log",
      });
    }
  };

  const refetch = (input: SearchType) => {
    apolloRefetch({
      input: {
        dateFilter: {
          displayDate: input.displayDate.format(),
        },
        statusFilter: {
          status: input.status ? Number(input.status) : undefined,
          csvStatus: input.csvStatus ? Number(input.csvStatus) : undefined,
          guidanceStatus: input.guidanceStatus
            ? Number(input.guidanceStatus)
            : undefined,
          paymentStatus: input.paymentStatus
            ? Number(input.paymentStatus)
            : undefined,
        },
        sort: {
          pharmacyDesiredDate: input.pharmacyDesiredDate,
          pharmacyReserveUpdateDate: input.pharmacyReserveUpdateDate,
          reserveTime: input.reserveTime,
        },
        keywordFilter: {
          customer: input.customer,
          patient: input.patient,
          clinic: input.clinic,
        },
        showFutureTreatment: input.showFutureTreatment || false,
      },
    });
    // 服薬指導：受付一覧：検索（会員）
    if (input.customer) {
      handleAuditLog("90001001001", input.customer);
    }
    // 服薬指導：受付一覧：検索（患者）
    if (input.patient) {
      handleAuditLog("90001004001", input.patient);
    }
    // 服薬指導：受付一覧：検索（クリニック）
    if (input.clinic) {
      handleAuditLog("90001005001", input.clinic);
    }
  };

  const receptions = data?.findPharmacyReserves;

  return {
    loading,
    receptions,
    formMethod: {
      ...method,
      onSubmitSearchQuery: method.handleSubmit(refetch),
    },
  };
};
