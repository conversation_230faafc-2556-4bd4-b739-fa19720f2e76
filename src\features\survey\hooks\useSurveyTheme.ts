import { useEffect, useState } from "react";

import { logger } from "@/utils/sentry-logger";

import type { ITheme } from "survey-react";

//
// @/features/setting-survey/hooks/useSurveyTheme からコピー
//

// 新しいカスタムフックを定義します。
export const useSurveyTheme = () => {
  const [theme, setTheme] = useState<ITheme | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const loadSurveyTheme = async () => {
      try {
        // `public`ディレクトリに配置されている`survey_theme.json`へのパス
        const response = await fetch("/assets/survey/survey_theme.json");
        if (!response.ok) {
          throw new Error("Survey theme loading failed");
        }
        const newTheme: ITheme = await response.json();

        setTheme(newTheme);
        setLoading(false);
      } catch (error) {
        logger({
          error,
          message: "failed to load survey theme json",
        });
        setError(
          error instanceof Error ? error : new Error("An error occurred"),
        );
        setLoading(false);
      }
    };

    loadSurveyTheme();
  }, []);

  return { theme, loading, error };
};
