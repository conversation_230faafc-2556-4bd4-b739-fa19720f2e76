import { Tooltip as AntdTooltip } from "antd";
import styled from "styled-components";

import { SvgIconInfo } from "@/components/ui/Icon/IconInfo";

const StyledSvgIconInfo = styled(SvgIconInfo)`
  width: 20px;
  height: 20px;

  path {
    fill: #fff !important;
  }
`;

type Props = {
  title: React.ReactNode;
};

export const Tooltip: React.FC<Props> = ({ title }) => (
  <AntdTooltip
    placement="right"
    title={title}
    arrow={false}
    color="#505050e6 90%"
    overlayStyle={{ maxWidth: "max-content" }}
    overlayInnerStyle={{
      borderRadius: "0",
    }}
    autoAdjustOverflow={false}
  >
    <StyledSvgIconInfo />
  </AntdTooltip>
);
