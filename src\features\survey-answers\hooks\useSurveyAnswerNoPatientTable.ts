import { useMemo, useState } from "react";

import { useRouter } from "next/router";

import { useClipboardCopy } from "@/features/survey-answers/hooks/useClipboardCopy";
import { getCustomAnswersKeyValues } from "@/features/survey-answers/utils/survey";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import {
  SortOrder,
  type SurveyAnswerNoPatient,
  type SurveyRes,
} from "@/apis/gql/generated/types";
import {
  useGetSurveyAnswerNoPatientsQuery,
  useGetSurveyAnswerNoPatientByIdLazyQuery,
} from "@/apis/gql/operations/__generated__/survey-answer";

export const useSurveyAnswerNoPatientTable = (
  selectedDate: string,
  selectedSurvey: SurveyRes | undefined,
  searchKeyword: string,
  selectedSurveyAnswerId: string,
  setSelectedSurveyAnswerId: (id: string) => void,
  refetchSurveyAnswerNoPatientById: () => void,
) => {
  const { query, isReady } = useRouter();
  const [sortField, setSortField] = useState<string>("");
  const [sortOrder, setSortOrder] = useState<SortOrder | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const { handleError } = useErrorHandler();
  const { copyToClipboard } = useClipboardCopy();
  const {
    data: getSurveyAnswersNoPatients,
    fetchMore: fetchMoreSurveyAnswersNoPatients,
    refetch: refetchSurveyAnswerNoPatients,
  } = useGetSurveyAnswerNoPatientsQuery({
    skip: !isReady,
    variables: {
      answerDate: selectedDate,
      surveyId: selectedSurvey?.surveyId,
    },
    onError: (error) => handleError({ error }),
    onCompleted: (data) => {
      if (
        data.getSurveyAnswerNoPatients &&
        data.getSurveyAnswerNoPatients.length < 20
      )
        setHasMore(false);
    },
  });

  const [getSurveyAnswerNoPatientById] =
    useGetSurveyAnswerNoPatientByIdLazyQuery({
      onError: (error) => handleError({ error }),
    });

  const handleLoadMoreSurveyAnswers = async (
    cursorId: string,
    cursorDate?: string,
  ) => {
    if (!hasMore || isFetchingMore) return;
    setIsFetchingMore(true);
    try {
      await fetchMoreSurveyAnswersNoPatients({
        variables: {
          answerDate: selectedDate,
          surveyId: selectedSurvey?.surveyId,
          cursorId,
          cursorDate,
          limit: 20,
          order:
            query.order === SortOrder.Ascend ||
            query.order === SortOrder.Descend
              ? query.order
              : undefined,
        },
      });
    } finally {
      setIsFetchingMore(false);
    }
  };

  const handleSelectSurveyAnswer = (id: string) => {
    if (id === selectedSurveyAnswerId) {
      refetchSurveyAnswerNoPatientById?.();
    } else {
      setSelectedSurveyAnswerId(id);
    }
  };

  const handleSortChange = (field: string, order: SortOrder | null) => {
    if (order === null) return;
    setSortField(field);
    setSortOrder(order);
  };

  const filteredSurveyAnswerNoPatients = useMemo(() => {
    if (!searchKeyword)
      return getSurveyAnswersNoPatients?.getSurveyAnswerNoPatients;
    return getSurveyAnswersNoPatients?.getSurveyAnswerNoPatients?.filter(
      (survey: SurveyAnswerNoPatient) =>
        survey.name?.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        survey.kanaName?.toLowerCase().includes(searchKeyword.toLowerCase()),
    );
  }, [searchKeyword, getSurveyAnswersNoPatients?.getSurveyAnswerNoPatients]);

  const sortedSurveyAnswerNoPatients = useMemo(() => {
    if (!filteredSurveyAnswerNoPatients) return [];
    return [...filteredSurveyAnswerNoPatients].sort((a, b) => {
      const aValue = a[sortField as keyof typeof a];
      const bValue = b[sortField as keyof typeof b];
      if (aValue === undefined || bValue === undefined) return 0;
      if (sortOrder === SortOrder.Ascend) return aValue > bValue ? 1 : -1;
      return aValue < bValue ? 1 : -1;
    });
  }, [filteredSurveyAnswerNoPatients, sortField, sortOrder]);

  const handleSurveyAnswerClipboardCopy = async (surveyId: string) => {
    if (!surveyId) return;

    try {
      const { data } = await getSurveyAnswerNoPatientById({
        variables: { id: surveyId },
      });

      const answers = getCustomAnswersKeyValues(
        JSON.parse(data?.getSurveyAnswerNoPatientById?.surveyAnswer || "[]"),
      );

      copyToClipboard(answers);
    } catch (error) {
      handleError({ error: error as Error });
    }
  };

  return {
    surveyAnswerNoPatients: sortedSurveyAnswerNoPatients,
    selectedSurvey,
    selectedSurveyAnswerId,
    handleSortChange,
    handleSelectSurveyAnswer,
    refetchSurveyAnswerNoPatients,
    handleLoadMoreSurveyAnswers,
    sortField,
    sortOrder,
    setSelectedSurveyAnswerId,
    handleSurveyAnswerClipboardCopy,
  };
};
