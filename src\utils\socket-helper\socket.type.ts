export interface ISystemConfig {
  screenCode?: string;
}

export interface ISystemRequestConfig {
  signal?: AbortSignal;
}

export interface IParamsFileMove {
  files: string[];
}

export interface IFile {
  fileName: string;
  content: string;
  contentBase64?: string;
  typeFile?: number;
}

export interface IDataGetFiles {
  fileList: IFile[];
}

export interface IResponse<Data> {
  data: Data;
  message: string;
  status: number;
}

export interface IXmlMsgRequest {
  MessageHeader: object;
  MessageBody: object;
}

export interface PayloadSignLocally {
  drugXml: string;
  hpkiPin: string;
}

export interface PayloadSignRemote {
  remoteToken: string;
  drugCsv: string;
  electronicPrescriptionProxyUse: number;
  electronicPrescriptionProxyServer: string;
  electronicPrescriptionProxyPort: number;
  epsSignServerUriType: number;
  webToken?: string;
}

export interface PayloadCancelPrescription {
  ptId: number;
  raiinNo: number;
  sinDate: number;
  refillSupported: string;
  timeout: number;
}

export interface PayloadAutoDispensing {
  ptId: number;
  raiinNo: number;
  sinDate: number;
  refillSupported: string;
  timeout: number;
}

interface QualificationConfirmSearchInfoReq {
  InsurerNumber: string;
  InsuredCardSymbol: string;
  InsuredIdentificationNumber: string;
  InsuredBranchNumber: string;
  Birthdate: string;
  LimitApplicationCertificateRelatedConsFlg: string;
  ArbitraryIdentifier: string;
}

export interface IHokenMessageBody {
  QualificationConfirmSearchInfo: QualificationConfirmSearchInfoReq;
}

export interface IHokenMessageHeader {
  MedicalInstitutionCode: string;
  ArbitraryFileIdentifier: string;
  QualificationConfirmationDate: string;
}

// SystemError
export class SystemError extends Error {
  public code?: string;
  public cause?: Error;

  constructor(code?: string, message?: string) {
    super(message);
    this.code = code;
    this.name = "SystemError";
  }

  static readonly ERR_BAD_REQUEST_CHECK_TOKEN = "ERR_BAD_REQUEST_CHECK_TOKEN";
  static readonly ERR_BAD_REQUEST = "ERR_BAD_REQUEST";
  static readonly ERR_CONNECT = "ERR_CONNECT";

  static isSystemError(error: unknown) {
    return (
      error instanceof SystemError &&
      error.code !== this.ERR_BAD_REQUEST_CHECK_TOKEN
    );
  }

  static isErrorConnect(error: unknown) {
    return error instanceof SystemError && error.code === this.ERR_CONNECT;
  }
}

export interface GetTokenRequest {
  secondaryCertId: string;
}

export interface PayloadCreateDrugXML {
  drugCsv: string;
}

export interface PayloadCancelRegisteredPrescription {
  ptId: number;
  raiinNo: number;
  sinDate: number;
  isHasFile: boolean;
  isError: boolean;
  refillSupported: number;
  medicalInstitutionCode?: string;
}

export interface DrugXmlResponse {
  DrugXml: string;
  message: string;
  status: number;
}

export interface LogOffHPKIRequest {
  secondaryCertId: string;
}

export type InvokeFileResponse = {
  data: {
    isDeleted: boolean;
  };
  message: string;
  status: number;
};

export type RequestUpdateRefNo = { fileName: string; content: string };

export type ListPortItem = {
  commonHost: string;
  host: string;
  isCommon: number;
  port: string;
};
