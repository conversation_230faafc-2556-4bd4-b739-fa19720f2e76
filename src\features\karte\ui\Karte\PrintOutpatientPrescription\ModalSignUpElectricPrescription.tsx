import router from "next/router";
import { useSearchParams } from "next/navigation";

import { TitleContent } from "@/features/karte/ui/Karte/PrintSetting/styles";
import { Button } from "@/components/ui/NewButton";
import {
  ModalContentGetBatch,
  StyledModal,
} from "@/features/karte/ui/Karte/PrescriptionInformation/style";
import { useModal } from "@/features/karte/providers/ModalProvider";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { usePostApiPdfCreatorOutDrugMutation } from "@/apis/gql/operations/__generated__/print-setting";
import { openPdfKarteFile } from "@/features/karte/ui/Karte/PrintOutpatientPrescription/utils";
import { useEPrescriptionContext } from "@/features/karte/ui/Karte/PrintSetting/EPrescriptionContextProvider";

import type { ApolloError } from "@apollo/client";

export function ModalSignUpElectricPrescription() {
  const { handleCloseModal } = useModal();
  const { dataCheckPrescriptionChangeOrder } = useEPrescriptionContext();
  const [postApiPdfCreatorOutDrug] = usePostApiPdfCreatorOutDrugMutation();
  const searchParams = useSearchParams();
  const raiinNo = searchParams.get("raiinNo") ?? "";
  const { id } = router.query;
  const sinDate = parseInt(searchParams.get("sinDate") ?? "0");
  const ptId = id?.toString() ?? "0";

  const { handleError } = useErrorHandler();

  async function handlePrintOutPatientPrescription() {
    try {
      //todo loop list prescriptions
      console.log(
        "dataCheckPrescriptionChangeOrder EPrescriptionContextProvider",
        dataCheckPrescriptionChangeOrder,
      );

      await postApiPdfCreatorOutDrug({
        variables: {
          ptId: ptId,
          sinDate: sinDate,
          raiinNo: raiinNo,
          epsPrintType: 1,
          hokenGp: -1,
        },
        onCompleted: (response) => {
          openPdfKarteFile(response?.postApiPdfCreatorOutDrug?.fileUrl ?? "");
        },
        onError: (error) => {
          handleError({ error });
        },
      });

      handleCloseModal("SIGN_UP_PRESCRIPTION");
      window.close();
    } catch (error) {
      handleError({ error: error as ApolloError }).then();
    }
  }

  function renderContent() {
    return (
      <div>
        <TitleContent>処方箋情報の登録が完了していません</TitleContent>
        <p>
          処方箋情報を登録せずに、紙の処方箋（引換番号なし）を発行しますか？
        </p>
      </div>
    );
  }

  function renderButtonFooter() {
    return [
      <Button
        onClick={() => {
          //todo handle case back to screen before
          handleCloseModal("SIGN_UP_PRESCRIPTION");
        }}
        shape="round"
        varient="tertiary"
        key="cancel"
      >
        戻る
      </Button>,
      <Button
        onClick={handlePrintOutPatientPrescription}
        shape="round"
        varient="primary"
        key="submit"
      >
        発行
      </Button>,
    ];
  }

  return (
    <StyledModal
      errorModal={false}
      isOpen={true}
      title={"処方箋発行形態の確認"}
      centered
      forceRender
      width={480}
      centerFooterContent={false}
      footer={renderButtonFooter()}
    >
      <ModalContentGetBatch>{renderContent()}</ModalContentGetBatch>
    </StyledModal>
  );
}
