import { useState, useCallback, createContext, useContext } from "react";

import styled from "styled-components";

import { checkConnectionSocket } from "@/utils/socket-helper/connection";
import { RenderIf } from "@/utils/common/render-if";
import { Loading } from "@/components/ui/Loading";
import { SvgIconError } from "@/components/ui/Icon/IconError";
import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { guideLineUrl } from "@/constants/guide-url";

import type { ReactNode } from "react";

/**
 * Type for the connection agent context
 */
type ConnectionAgentContextType = {
  isConnected: boolean;
  isLoading: boolean;
  openModal: boolean;
  setOpenModal: (openModal: boolean) => void;
  checkConnection: () => Promise<boolean>;
};

/**
 * Context for the connection agent
 */
const ConnectionAgentContext = createContext<
  ConnectionAgentContextType | undefined
>(undefined);

/**
 * Hook to check connection with agent
 * @returns {Object} - Object containing connection status and check function
 */
export const useCheckConctionAgent = (): ConnectionAgentContextType => {
  const context = useContext(ConnectionAgentContext);
  if (!context) {
    throw new Error(
      "useCheckConctionAgent must be used within a ConnectionAgentProvider",
    );
  }
  return context;
};

/**
 * Component to display loading indicator when checking connection with agent
 */
const ConnectionAgentLoadingIndicator: React.FC = () => {
  const { isLoading } = useCheckConctionAgent();

  return (
    <RenderIf condition={isLoading}>
      <Loading isLoading={isLoading} />
    </RenderIf>
  );
};

/**
 * Modal component to show when connection to agent fails
 */
const ConnectSocketErrorModal = () => {
  const { openModal, setOpenModal } = useCheckConctionAgent();

  const handleOpenGuilde = () => {
    window.open(guideLineUrl, "_blank", "noopener,noreferrer");
    setOpenModal(false);
  };

  return (
    <StyledModal
      title="エージェントの起動が必要です"
      isOpen={openModal}
      onCancel={() => setOpenModal(false)}
      footer={
        <div style={{ display: "flex", justifyContent: "center" }}>
          <Button
            key="cancel"
            varient="tertiary"
            onClick={() => setOpenModal(false)}
          >
            閉じる
          </Button>
        </div>
      }
      zIndex={1001}
    >
      <ModalWrapper>
        <ErrorIcon />
        <Heading>
          エージェントプログラムが
          <br />
          起動していません。
        </Heading>
        <Description>
          エージェントが起動していることをご確認ください。インストールされていない場合は、
          <span
            onClick={handleOpenGuilde}
            style={{ color: "#007bff", cursor: "pointer" }}
          >
            インストールガイド
          </span>
          をご参照の上、インストールを行ってください。
        </Description>
      </ModalWrapper>
    </StyledModal>
  );
};

/**
 * Provider component for the connection agent
 */
export const ConnectionAgentProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const checkConnection = useCallback(async () => {
    setIsLoading(true);
    try {
      // debugger;
      const result = await checkConnectionSocket({
        onSuccess: () => setIsConnected(true),
        onError: () => {
          // debugger;
          setIsConnected(false);
          setOpenModal(true);
        },
      });
      setIsConnected(result);
      return result;
    } catch {
      setIsConnected(false);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const value = {
    isConnected,
    isLoading,
    openModal,
    setOpenModal,
    checkConnection,
  };

  return (
    <ConnectionAgentContext.Provider value={value}>
      {children}
      <ConnectionAgentLoadingIndicator />
      <ConnectSocketErrorModal />
    </ConnectionAgentContext.Provider>
  );
};

// Styled components for ConnectSocketErrorModal
const ModalWrapper = styled.div`
  padding: 20px 24px;
  min-height: 256px;
`;

const StyledModal = styled(Modal)`
  .ant-modal-footer {
    justify-content: center !important;
  }
  .ant-modal-header {
    background-color: #e74c3c;
  }
`;

const Heading = styled.p`
  font-size: 24px;
  line-height: 24px;
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 34px;
  text-align: center;
`;

const ErrorIcon = styled(SvgIconError)`
  display: block;
  margin: 0 auto;
`;

const Description = styled.p``;
