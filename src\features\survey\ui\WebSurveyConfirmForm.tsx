import React, { useMemo } from "react";

import styled from "styled-components";

import { Modal } from "@/components/ui/Modal";
import { Button } from "@/components/ui/NewButton";
import { formatYYYYMMDDWithJapanese } from "@/utils/datetime-format";

import { useWebSurveyConfirm } from "../hooks/useWebSurveyConfirm";

import type { CustomAnswerType } from "../utils/customer-survey";
import type { WebSurveyPatientFormType } from "./WebSurveyPatientForm";

/**
 * Web問診票：問診内容の確認フォーム
 *
 * 問診内容のソースコードは、クリニックマップの次のコードをベースに作成しています。
 * @see {@link https://github.com/bizleap-healthcare/clinic-reservation/blob/main/user-client/src/components/molecules/customer-survey/CustomerSurveyClinicAnswer.tsx}
 */

type Props = {
  secret: string;
  patientData: WebSurveyPatientFormType;
  surveyAnswer: CustomAnswerType[];
  onComplete: () => void;
  onBack: () => void;
};

export const WebSurveyConfirmForm = ({
  secret,
  patientData,
  surveyAnswer,
  onComplete,
  onBack,
}: Props) => {
  const displayQuestions = useMemo(
    () =>
      surveyAnswer.filter((question) => question.questionType !== "expression"),
    [surveyAnswer],
  );

  const {
    handleSubmit,
    errorMessage,
    isOpenErrorModal,
    handleErrorModalClose,
  } = useWebSurveyConfirm(secret, patientData, surveyAnswer, onComplete);

  return (
    <Wrapper>
      <ScrollArea>
        <StyledTitle>問診内容の確認</StyledTitle>
        <InfoWrapper>
          <ItemWrapper>
            <QuestionTitle>お名前</QuestionTitle>
            <div>{patientData.name}</div>
          </ItemWrapper>
          <ItemWrapper>
            <QuestionTitle>フリガナ</QuestionTitle>
            <div>{patientData.kanaName}</div>
          </ItemWrapper>
          <ItemWrapper>
            <QuestionTitle>生年月日</QuestionTitle>
            <div>{formatYYYYMMDDWithJapanese(patientData.birthDate)}</div>
          </ItemWrapper>
        </InfoWrapper>
        <InfoWrapper>
          {displayQuestions.map((question) => (
            <React.Fragment key={question.questionName}>
              <ItemWrapper>
                <QuestionTitle>{question.questionTitle}</QuestionTitle>
                <div> {getQuestionDisplayAnswer(question)}</div>
              </ItemWrapper>
            </React.Fragment>
          ))}
        </InfoWrapper>
        <InfoWrapper>
          <AgreeWrapper>
            【同意事項】
            <br />
            ご入力いただいた個人情報は、GMOヘルステック株式会社がWeb問診票サービスを提供する目的、および受診先の医療機関から委託を受けた業務（問診回答の取得・管理等）を行う目的で利用いたします。
          </AgreeWrapper>
        </InfoWrapper>
        <ButtonWrapper>
          <StyledButton htmlType="button" onClick={onBack} varient="tertiary">
            戻る
          </StyledButton>
          <StyledButton
            htmlType="button"
            onClick={handleSubmit}
            varient="primary"
          >
            提出
          </StyledButton>
        </ButtonWrapper>
      </ScrollArea>

      <Modal
        title="エラー"
        errorModal
        centerFooterContent
        isOpen={isOpenErrorModal}
        footer={[
          <Button
            key="closeButton"
            style={{ backgroundColor: "#cfddea", height: 44, width: 180 }}
            varient="custom"
            onClick={handleErrorModalClose}
          >
            閉じる
          </Button>,
        ]}
      >
        <ErrorMessage>{errorMessage}</ErrorMessage>
      </Modal>
    </Wrapper>
  );
};

const getQuestionDisplayAnswer = ({
  questionType,
  answerValue,
  answerDisplayValue,
}: CustomAnswerType): React.JSX.Element => {
  if (questionType === "multipletext") {
    if (!answerValue) return <Answer>-</Answer>;
    return (
      <>
        {Object.entries(answerValue).map(([key, value]) => (
          <Answer key={key}>
            {key}: {value}
          </Answer>
        ))}
      </>
    );
  }

  return <Answer>{answerDisplayValue || "-"}</Answer>;
};

const Wrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: start;
  overflow-y: auto;

  @media (max-width: 600px) {
    height: 100%;
    border-radius: unset;
    margin-top: unset;
  }
`;

const ScrollArea = styled.div`
  width: 640px;
  margin: 40px 0;
  background-color: #fff;
  border-radius: 26px;

  @media (max-width: 600px) {
    margin: unset;
  }
`;

const StyledTitle = styled.div`
  font-family: "NotoSansJP";
  font-size: 20px;
  font-weight: bold;
  color: "#243544";
  padding-bottom: 20px;
  margin: 40px 40px;
  border-bottom: solid 4px #e2e3e5;

  @media (max-width: 600px) {
    margin: 20px 20px;
    text-align: center;
  }
`;

const ButtonWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 40px 40px;

  @media (max-width: 600px) {
    margin: 0 20px 20px;
  }
`;

const InfoWrapper = styled.div`
  margin: 20px 40px;
  border-bottom: solid 1px #e2e3e5;

  @media (max-width: 600px) {
    margin: 20px 20px;
  }
`;

const ItemWrapper = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 20px;
  align-items: center;
`;

const QuestionTitle = styled.div`
  font-size: 14px;
  color: #6a757d;
  white-space: pre-wrap;
  margin-right: 20px;
`;

const Answer = styled.div`
  font-size: 16px;
  color: "#243544";
  white-space: pre-wrap;
`;

const ErrorMessage = styled.div`
  font-size: 14px;
  color: "#243544";
  text-align: center;
  padding: 72px 0;
`;

const AgreeWrapper = styled.div`
  font-size: 14px;
  color: #6a757d;
  white-space: pre-wrap;
  margin-bottom: 20px;
`;

const StyledButton = styled(Button)`
  height: 36px;
  width: 140px;
`;
