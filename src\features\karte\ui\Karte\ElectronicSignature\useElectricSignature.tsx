import { useMemo, useRef } from "react";

import { useGetApiSystemConfGetListQuery } from "@/apis/gql/operations/__generated__/karte-get-online-consent";
import { System } from "@/utils/socket-helper";
import { useErrorHandler } from "@/hooks/useErrorHandler";

import type {
  ISystemRequestConfig,
  PayloadSignRemote,
} from "@/utils/socket-helper/socket.type";

type Props = {
  onError?: () => void;
};

export function useElectricSignature({ onError }: Props) {
  const { handleError } = useErrorHandler();
  const controllerAbort = useRef<AbortController | null>(null);
  const { data: dataSystemConf, loading: loadingSystem } =
    useGetApiSystemConfGetListQuery({
      onError: (error) => {
        handleError({ error }).then();
        if (onError) {
          onError();
        }
      },
    });

  const systemConf =
    dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList;

  const system = new System("/medical", systemConf);

  function handleDisconnectSocket() {
    if (controllerAbort.current) {
      controllerAbort.current.abort();
    }
  }

  const electronicPrescriptionProxyUse = useMemo(() => {
    return (
      systemConf?.find((item) => item.grpEdaNo === 11 && item.grpCd === 100040)
        ?.val || "0"
    );
  }, [systemConf]);

  const isSignatureLocal = useMemo(() => {
    return systemConf?.some(
      (item) =>
        item.grpEdaNo === 10 && item.grpCd === 100040 && item?.val === 0,
    );
  }, [systemConf]);

  const electronicPrescriptionProxyServer = useMemo(() => {
    return (
      systemConf?.find((item) => item.grpEdaNo === 12 && item.grpCd === 100040)
        ?.param || "hpkicardless-clientadapter-server"
    );
  }, [systemConf]);

  const electronicPrescriptionProxyPort = useMemo(() => {
    return (
      systemConf?.find((item) => item.grpEdaNo === 12 && item.grpCd === 100040)
        ?.val || "3128"
    );
  }, [systemConf]);

  const epsSignServerUriType = useMemo(() => {
    return (
      systemConf?.find((item) => item.grpEdaNo === 18 && item.grpCd === 100040)
        ?.val || "0"
    );
  }, [systemConf]);

  const timeCachePinSetting = useMemo(() => {
    return (
      systemConf?.find((item) => item.grpEdaNo === 5 && item.grpCd === 100040)
        ?.val ?? 0
    );
  }, [systemConf]);

  const timeSettingEnableBtn = useMemo(() => {
    return (
      systemConf?.find((item) => item.grpEdaNo === 4 && item.grpCd === 100040)
        ?.val ?? 0
    );
  }, [systemConf]);

  async function checkHpkiDriverSetting() {
    return await system.loadHpkiCard();
  }

  async function signRemotely(payload: PayloadSignRemote) {
    controllerAbort.current = new AbortController();
    const signal = controllerAbort.current.signal;
    const configSignal: ISystemRequestConfig = { signal };
    return await system.signRemotely(payload, configSignal);
  }

  async function loginElectricSignature(pin: string, drugXml: string) {
    controllerAbort.current = new AbortController();
    const signal = controllerAbort.current.signal;
    const configSignal: ISystemRequestConfig = { signal };
    return await system.signLocally(
      {
        drugXml,
        hpkiPin: pin,
      },
      configSignal,
    );
  }

  const isLoginLocal = useMemo(() => {
    return dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.some(
      (sys) => sys.grpCd === 100040 && sys.grpEdaNo === 10 && sys.val === 0,
    );
  }, [dataSystemConf]);

  const isLoginRemote = useMemo(() => {
    return dataSystemConf?.getApiSystemConfGetList?.data?.systemConfList?.some(
      (sys) => sys.grpCd === 100040 && sys.grpEdaNo === 10 && sys.val === 1,
    );
  }, [dataSystemConf]);

  return {
    isLoginLocal,
    isLoginRemote,
    loading: loadingSystem,
    checkHpkiDriverSetting,
    loginElectricSignature,
    timeCachePinSetting,
    timeSettingEnableBtn,
    handleDisconnectSocket,
    electronicPrescriptionProxyUse,
    electronicPrescriptionProxyServer,
    electronicPrescriptionProxyPort,
    epsSignServerUriType,
    signRemotely,
    isSignatureLocal,
  };
}
