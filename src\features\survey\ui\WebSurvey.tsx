import { useState } from "react";

import router from "next/router";
import styled from "styled-components";

import { ContentLoading } from "@/components/ui/ContentLoading";
import { SvgIconLogoWebSurvey } from "@/components/ui/Icon/IconLogoWebSurvey";
import { Modal } from "@/components/ui/Modal";

import { useSurveyTheme } from "../hooks/useSurveyTheme";
import { useWebSurvey } from "../hooks/useWebSurvey";

import { WebSurveyClinicForm } from "./WebSurveyClinicForm";
import { WebSurveyCompleteForm } from "./WebSurveyCompleteForm";
import { WebSurveyConfirmForm } from "./WebSurveyConfirmForm";
import {
  WebSurveyPatientForm,
  type WebSurveyPatientFormType,
} from "./WebSurveyPatientForm";

import type { WebSurveyClinicFormType } from "./WebSurveyClinicForm";

/**
 * Web問診票：トップ画面
 */

export const WebSurvey = () => {
  const { theme } = useSurveyTheme();
  const { secret, data, loading, errorMessage } = useWebSurvey(
    router.query.secret,
  );

  const [currentForm, setCurrentForm] = useState<
    "patient" | "clinic" | "confirm" | "complete"
  >("patient");

  const [patientData, setPatientData] = useState<WebSurveyPatientFormType>({
    name: "",
    kanaName: "",
    birthDate: "",
  });

  const [clinicData, setClinicData] = useState<WebSurveyClinicFormType>({
    answerData: undefined,
    tempFiles: {},
    surveyAnswer: [],
  });

  const handlePatientFormSubmit = (data: WebSurveyPatientFormType) => {
    setPatientData(data);
    setCurrentForm("clinic");
  };

  const handleClinicFormSubmit = (data: WebSurveyClinicFormType) => {
    setClinicData(data);
    setCurrentForm("confirm");
  };

  const handleClinicFormBack = (data: WebSurveyClinicFormType) => {
    setClinicData(data);
    setCurrentForm("patient");
  };

  return (
    <Wrapper>
      <Header>
        <SurveyHeader>
          <StyledSurveyHeader>Web問診票</StyledSurveyHeader>
          <StyledClinicHeader>{data?.clinicName}</StyledClinicHeader>
        </SurveyHeader>
      </Header>
      <FormContainer>
        {currentForm === "patient" && (
          <WebSurveyPatientForm
            data={patientData}
            onSubmit={handlePatientFormSubmit}
          />
        )}
        {currentForm === "clinic" && theme && data && (
          <WebSurveyClinicForm
            theme={theme}
            surveyModelJson={data.fQuesJson}
            data={clinicData}
            onSubmit={handleClinicFormSubmit}
            onBack={handleClinicFormBack}
          />
        )}
        {currentForm === "confirm" && (
          <WebSurveyConfirmForm
            secret={secret}
            patientData={patientData}
            surveyAnswer={clinicData.surveyAnswer}
            onComplete={() => setCurrentForm("complete")}
            onBack={() => setCurrentForm("clinic")}
          />
        )}
        {currentForm === "complete" && <WebSurveyCompleteForm />}
      </FormContainer>
      <Footer>
        <SvgIconLogoWebSurvey />
      </Footer>

      {loading && (
        <LoadingWrapper>
          <ContentLoading />
        </LoadingWrapper>
      )}
      {errorMessage && (
        <Modal title="エラー" errorModal isOpen={true} footer={false}>
          <ErrorMessage>{errorMessage}</ErrorMessage>
        </Modal>
      )}
    </Wrapper>
  );
};

const Wrapper = styled.div`
  overflow-y: hidden;
`;

const Header = styled.div`
  width: 100%;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  @media (max-width: 600px) {
    border-bottom: solid 1px #e2e3e5;
  }
`;

const SurveyHeader = styled.div`
  width: 640px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  @media (max-width: 600px) {
    margin: 0 20px;
  }
`;

const StyledSurveyHeader = styled.div`
  font-family: "NotoSansJP";
  font-size: 20px;
  font-weight: bold;
  color: "#243544";
`;

const StyledClinicHeader = styled.div`
  font-family: "NotoSansJP";
  font-size: 14px;
  color: "#243544";
`;

const FormContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: calc(100svh - 48px - 40px);

  @media (max-width: 600px) {
    margin-top: 0;
    background-color: #fff;
  }
`;

const Footer = styled.div`
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  @media (max-width: 600px) {
    border-top: solid 1px #e2e3e5;
  }
`;

const LoadingWrapper = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.25);
  z-index: 1000;
`;

const ErrorMessage = styled.div`
  font-size: 14px;
  color: "#243544";
  text-align: center;
  padding: 72px 0;
`;
