import React, { useEffect } from "react";

import { Menu } from "antd";
import { useRouter } from "next/router";
import styled from "styled-components";

import {
  useZendeskSsoFormHandler,
  ZendeskSsoForm,
} from "@/components/common/Zendesk";
import { ZIndexOrder } from "@/constants/common";
import { pharmacySettingMenuData, settingMenuData } from "@/constants/url";
import { ZendeskFirstPage } from "@/constants/zendesk";

import type { ReactNode } from "react";

const SettingContentWrapper = styled.div`
  padding: 0px;
  height: 100%;
  display: flex;
`;

const LayoutWrapper = styled.div`
  background-color: white;
  padding: 20px;
  width: var(--setting-layout-sidebar-width);
  min-width: var(--setting-layout-sidebar-width);
  height: 100%;
  border-right: 1px solid #e2e3e5;
  z-index: ${ZIndexOrder.LayoutWrapper};
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
  }
  &::-webkit-scrollbar-track {
    border-radius: 4px;
  }
`;

const Title = styled.p`
  font-size: 20px;
  line-height: 20px;
  font-weight: bold;
  margin-bottom: 20px;
`;

const StyledMenu = styled(Menu)`
  border-inline-end: none !important;
  font-size: 16px;

  .ant-menu-item {
    width: 100%;
    border-radius: 6px;
    padding: 0;
    margin: 0;
    margin-bottom: 8px;
    transition: 0.3s;

    &:hover {
      background: #eaf0f5 !important;
    }
  }

  .ant-menu-title-content {
    padding: 12px 8px;
  }

  .ant-menu-item-selected {
    color: #243544;
    font-weight: bold;
    background: #eaf0f5;
  }
`;

export const SettingContent: React.FC<{
  children: ReactNode;
  isPharmacy?: boolean;
}> = ({ children, isPharmacy = false }) => {
  const router = useRouter();
  const { zendeskFormRef, sendZendeskForm } = useZendeskSsoFormHandler();

  const applicatedSettingRoutes = isPharmacy
    ? pharmacySettingMenuData
    : settingMenuData;

  const selectedKey =
    applicatedSettingRoutes.find(({ pathname, relatedPaths }) =>
      [pathname, ...relatedPaths].includes(router.pathname),
    )?.pathname || "";

  useEffect(() => {
    if (!router.isReady || !selectedKey) {
      return;
    }

    const targetElement = document.querySelector(
      `[data-pathname='${selectedKey}']`,
    );

    if (targetElement) {
      targetElement.scrollIntoView({ block: "center" });
    }
  }, [router.isReady, selectedKey]);

  return (
    <SettingContentWrapper>
      <ZendeskSsoForm ref={zendeskFormRef} />
      <LayoutWrapper>
        <Title>設定</Title>
        <StyledMenu tabIndex={-1} selectedKeys={[selectedKey]}>
          {applicatedSettingRoutes.map((menu) => {
            return (
              <Menu.Item
                data-pathname={menu.pathname}
                key={menu.pathname}
                tabIndex={0}
                onClick={() => {
                  if (menu.pathname === "/setting/terms") {
                    sendZendeskForm(ZendeskFirstPage.AGREEMENT);
                    return;
                  }

                  if (menu.pathname.startsWith("https://")) {
                    window.open(menu.pathname, "_blank");
                    return;
                  }

                  if (menu.pathname === "/setting/receipt") {
                    window.open("/setting/receipt", "_blank");
                    return;
                  }

                  router.push(`${menu.pathname}/${menu.query ?? ""}`);
                }}
              >
                {menu.label}
              </Menu.Item>
            );
          })}
        </StyledMenu>
      </LayoutWrapper>
      {children}
    </SettingContentWrapper>
  );
};
