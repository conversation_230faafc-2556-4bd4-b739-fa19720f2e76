import { useMemo } from "react";

import { useRouter } from "next/router";

import { ReservationStatus } from "@/constants/reservation";
// eslint-disable-next-line import/no-restricted-paths
import { useUpdateReservation } from "@/features/calendar/hooks/useUpdateReservation";
import { useGlobalNotification } from "@/hooks/useGlobalNotification";
import { useSession } from "@/hooks/useSession";

import { isPatientJoinedMeeting, isShortTimeOperation } from "../utils";

import type { Meeting } from "@/apis/gql/generated/types";

export const useMeetingWaiting = (meeting: Meeting) => {
  const { query, replace } = useRouter();
  const {
    session: { isPharmacy },
  } = useSession();

  const { notification } = useGlobalNotification();
  const { handleUpdateReservation, loading } = useUpdateReservation();

  const {
    reservationDetail,
    familyReservations,
    treatmentPatient,
    displayName,
    pharmacyDesiredDate,
  } = useMemo(() => {
    const detailId = Number(query.detail);
    const reservationDetail = meeting.reservation?.reservationDetails?.find(
      (detail) => detail.reserveDetailId === detailId,
    );

    // If detailId exists but no matching reservationDetail is found, remove it from query
    if (detailId && !reservationDetail) {
      const { detail, ...restQuery } = query;
      replace({ query: restQuery }, undefined, { shallow: true });
    }

    const familyReservations =
      meeting.reservation?.reservationDetails?.filter(
        (detail) => detail.reserveDetailId !== detailId,
      ) ?? [];

    const treatmentPatient = reservationDetail?.patient || meeting.patient;

    const pharmacyDesiredDate = meeting.pharmacyReserve?.desiredDate[0];
    const displayName = isPharmacy
      ? meeting.pharmacyReserve?.customer.name ||
        meeting.pharmacyReserve?.customer.kanaName
      : treatmentPatient?.patientName || treatmentPatient?.patientNameKana;

    return {
      reservationDetail:
        reservationDetail || meeting.reservation?.reservationDetails?.[0],
      familyReservations,
      treatmentPatient,
      displayName,
      pharmacyDesiredDate,
    };
  }, [
    meeting.reservation,
    query.detail,
    meeting.patient,
    meeting.pharmacyReserve,
    isPharmacy,
    replace,
  ]);

  const validateShortTimeOperation = (updatedAt: string) => {
    if (isShortTimeOperation(updatedAt)) {
      notification.error({
        message:
          "短時間に何度も操作が行われました。しばらくしてから再度お試しください。",
      });

      return false;
    }

    return true;
  };

  const requestPatientJoin = async () => {
    if (loading) {
      return;
    }

    if (reservationDetail) {
      if (
        reservationDetail.status > ReservationStatus.RESERVED &&
        !validateShortTimeOperation(reservationDetail.updatedAt)
      ) {
        return;
      }

      handleUpdateReservation(
        reservationDetail.reserveId,
        reservationDetail.reserveDetailId,
        reservationDetail.examTimeSlot.calendar.calendarID,
        {
          calendarId: reservationDetail.examTimeSlot.calendar.calendarID,
          calendarTreatmentId:
            reservationDetail.calendarTreatment.calendarTreatmentID,
          examTimeSlotId: reservationDetail.examTimeSlot.examTimeSlotID,
          status: ReservationStatus.MEDICAL_EXAM_START,
        },
        true,
        ["getMeetingDetail"],
        () => {
          notification.success({
            message: "患者呼び出しを行いました。",
          });
        },
      );
    }
  };

  return {
    isPharmacy,
    loading,
    reservationDetail,
    familyReservations,
    treatmentPatient,
    displayName,
    pharmacyDesiredDate,
    isPatientJoined: isPatientJoinedMeeting(meeting.status),
    requestPatientJoin,
  };
};
