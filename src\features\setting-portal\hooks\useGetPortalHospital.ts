import { useGetExaminationsQuery } from "@/apis/gql/operations/__generated__/examination";
import { useGetImportDataByHpIdLazyQuery } from "@/apis/gql/operations/__generated__/hospital";
import { useGetPortalHospitalQuery } from "@/apis/gql/operations/__generated__/portal-hospital";
import { useGetSpecialistsQuery } from "@/apis/gql/operations/__generated__/specialist";
import { useGetTagsQuery } from "@/apis/gql/operations/__generated__/tag";
import { useErrorHandler } from "@/hooks/useErrorHandler";

export const useGetPortalHospital = () => {
  const { handleError } = useErrorHandler();

  const [getImportData, { loading: isGettingScuelData, data: scuelData }] =
    useGetImportDataByHpIdLazyQuery();

  const getScuelData = () => {
    getImportData({
      onError: (error) => {
        handleError({ error });
      },
    });
  };

  const {
    loading: hospitalLoading,
    data: hospitalData,
    error,
  } = useGetPortalHospitalQuery({
    onError: (error) => {
      handleError({
        error,
        commonMessage: "GMOクリニック・マップ連携情報の取得に失敗しました",
      });
    },
    onCompleted: (data) => {
      if (!data.getPortalHospitalById.portalHospital) {
        getScuelData();
      }
    },
  });

  const { loading: tagsLoading, data: tagData } = useGetTagsQuery();
  const { loading: examLoading, data: examData } = useGetExaminationsQuery();
  const { loading: specialistLoading, data: specialistData } =
    useGetSpecialistsQuery();

  return {
    loading: hospitalLoading || tagsLoading || examLoading || specialistLoading,
    hospital: hospitalData?.getPortalHospitalById.portalHospital,
    tags: tagData?.getTags,
    examination: examData?.getExaminations,
    specialist: specialistData?.getSpecialists,
    hasError: !!error,
    isGettingScuelData,
    scuelData,
  };
};
