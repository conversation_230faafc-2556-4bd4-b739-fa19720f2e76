// import React from "react";

// if (typeof window !== "undefined" && process.env.NODE_ENV === "development") {
//   console.log(
//     "Applying whyDidYouRender, to help you locate unnecessary re-renders during development. See https://github.com/welldone-software/why-did-you-render",
//   );

//   import("@welldone-software/why-did-you-render").then((whyDidYouRender) => {
//     whyDidYouRender.default(React, {
//       trackAllPureComponents: true,
//       trackHooks: true,
//       logOwnerReasons: true,
//       logOnDifferentValues: true,
//       collapseGroups: true,
//     });
//   });
// }
