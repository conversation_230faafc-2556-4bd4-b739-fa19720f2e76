version: 0.2

env:
  shell: bash
phases:
  install:
    runtime-versions:
      nodejs: 22
    commands:
      - n 22.16.0
  pre_build:
    commands:
      ### 環境準備
      - bash --version
      - pwd
      - rm -rf .npmrc
      - aws --version
      - node -v
      - yarn -v
      - ls -la
      - yarn install

      ### 静的解析
      - export NODE_OPTIONS="--max-old-space-size=8192"

      # バックグラウンドで並列実行（同一シェルセッションで行う）
      # いずれかの処理が失敗した場合に即時Abort
      - |
        yarn type-check &
        P1=$!

        yarn lint &
        P2=$!

        while kill -0 "$P1" 2>/dev/null || kill -0 "$P2" 2>/dev/null; do
          wait -n
          CODE=$?
          if [ "$CODE" -ne 0 ]; then
            echo "❌ CODE_CHECK FAILED! KILLING OTHER CODE_CHECK PROCESS..."
            kill $P1 $P2 2>/dev/null
            exit 1
          fi
        done

        echo "🟢 ALL CODE_CHECK SUCCEEDED!"
        exit 0
  build:
    commands:
      - yarn build:dev
    on-failure: ABORT

cache:
  paths:
    - /root/.cache/yarn/**/*
