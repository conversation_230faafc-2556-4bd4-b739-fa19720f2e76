import React, { useCallback, useRef, useState } from "react";

import {
  CONFIRMATION_FILE_NAME,
  SystemHub,
  SystemScreenCode,
} from "@/constants/confirm-online";
import { System } from "@/utils/socket-helper";
import { createXmlFileConsentCancelInfo } from "@/utils/socket-helper/onlineQualification";
import { useErrorHandler } from "@/hooks/useErrorHandler";
import { RenderIf } from "@/utils/common/render-if";
import { usePostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation } from "@/apis/gql/operations/__generated__/batch-online";
import { useCheckConctionAgent } from "@/hooks/useCheckConctionAgent";

import { BatchOnlineLoadingModal } from "../ui/modals/BatchOnlineCheck/BatchOnlineLoadingModal";
import { BatchOnlineErrorModal } from "../ui/modals/BatchOnlineCheck/BatchOnlineErrorModal";

type ConfirmCancelInfo = {
  birthdate: string;
  medicalTreatmentFlag: string;
  selectedHokenInf: {
    hokensyaNo: string;
    kigo: string;
    bango: string;
    edaNo: string;
  };
};

type ValidateState = {
  type: "success" | "error";
  content: {
    messageTitle: string;
    messageContent: string;
    messageContent2?: string;
  };
};

type Props = {
  callback?: () => void;
};

export const useCancelConsentHomeVisit = ({ callback }: Props) => {
  const waitingOQController = useRef<AbortController>();
  const [openValidateModal, setOpenValidateModal] = useState<ValidateState>();

  const [isLoading, setLoading] = useState<boolean>(false);

  const { handleError } = useErrorHandler();
  const { checkConnection } = useCheckConctionAgent();
  const [convertXmlToOqSsihvd01XmlMsg] =
    usePostApiOnlineConvertXmlToOqSsihvd01XmlMsgMutation({
      onError: (error) => handleError({ error }),
    });

  const handleResponseFile = useCallback(
    async (xmlString: string) => {
      const res = await convertXmlToOqSsihvd01XmlMsg({
        variables: {
          payload: {
            xmlString,
          },
        },
      });
      const response =
        res?.data?.postApiOnlineConvertXmlToOQSsihvd01XmlMsg?.data
          ?.oqSsihvdred01Response;

      if (response?.messageHeader?.segmentOfResult)
        switch (response?.messageHeader?.segmentOfResult) {
          case "1": {
            return callback?.();
          }

          case "2": {
            if (Number(response.messageBody?.processingResultStatus) === 2) {
              return setOpenValidateModal({
                type: "error",
                content: {
                  messageTitle: `閲覧同意の取り消しに失敗しました`,
                  messageContent: `処理結果コード: ${response?.messageBody?.processingResultCode}`,
                  messageContent2: `${response?.messageBody?.processingResultMessage}`,
                },
              });
            } else {
              return setOpenValidateModal({
                type: "error",
                content: {
                  messageTitle: "閲覧同意の取り消しに失敗しました",
                  messageContent:
                    "資格確認システムで処理中のため、取り消しできません。",
                  messageContent2: "しばらくたってから再試行してください。",
                },
              });
            }
          }

          case "9": {
            return setOpenValidateModal({
              type: "error",
              content: {
                messageTitle: `閲覧同意の取り消しに失敗しました`,
                messageContent: `${response?.messageHeader?.errorCode && "エラーコード:"} ${response?.messageHeader?.errorCode}`,
                messageContent2: `${response?.messageHeader?.errorMessage}`,
              },
            });
          }

          default:
            break;
        }
    },
    [callback, convertXmlToOqSsihvd01XmlMsg],
  );

  const handleRequestCancelConsent = useCallback(
    async (confirmOnlineInfo: ConfirmCancelInfo) => {
      try {
        const isConnected = await checkConnection();
        if (!isConnected) {
          return;
        }

        setLoading(true);
        waitingOQController.current?.abort();
        waitingOQController.current = new AbortController();

        const { birthdate, medicalTreatmentFlag, selectedHokenInf } =
          confirmOnlineInfo;

        const { fileName, msg } = createXmlFileConsentCancelInfo(
          birthdate,
          medicalTreatmentFlag ?? "0",
          {
            ...selectedHokenInf,
          },
        );

        const res = await onlineVisiting.createFile(
          msg.XmlMsg,
          fileName,
          { signal: waitingOQController.current?.signal },
          "CreateXmlFile",
        );

        if (!res || waitingOQController.current.signal.aborted) return;
        await onlineVisiting.moveFile({ files: [res.data.fileName] });
        if (res.data.fileName.includes(CONFIRMATION_FILE_NAME.CONSENT)) {
          const requestFileName = res.data.fileName.replace("err", "xml");
          await onlineVisiting.moveFile({ files: [requestFileName] });
          const match = res.data.content.match(/\[(.*?)\] (.*)/);
          if (match) {
            const processingResultCode = match[1];
            const errorMessage = match[2];
            setOpenValidateModal({
              type: "error",
              content: {
                messageTitle: "閲覧同意の取り消しに失敗しました",
                messageContent: `処理結果コード: ${processingResultCode}\n${errorMessage}`,
              },
            });
          }
        }

        if (res.data?.content) handleResponseFile(res.data.content);
      } catch (err) {
        if (err instanceof Error) {
          setOpenValidateModal({
            type: "error",
            content: {
              messageTitle: "閲覧同意の取り消しに失敗しました",
              messageContent:
                "保険情報が見つかりません。有効な保険情報を登録してください。",
            },
          });
        }
      } finally {
        setLoading(false);
      }
    },
    [handleResponseFile],
  );

  const ValidateCancelRequestComponent = useCallback(() => {
    return (
      <>
        <RenderIf condition={openValidateModal?.type === "error"}>
          <BatchOnlineErrorModal
            onClose={() => setOpenValidateModal(undefined)}
            content={openValidateModal?.content}
          />
        </RenderIf>
        <RenderIf condition={isLoading}>
          <BatchOnlineLoadingModal
            content={{
              title: "処理中",
              heading: "オンライン資格確認システムからの結果を待っています",
            }}
            onClose={() => setLoading(false)}
          />
        </RenderIf>
      </>
    );
  }, [isLoading, openValidateModal?.content, openValidateModal?.type]);

  return {
    isLoading,
    handleRequestCancelConsent,
    ValidateCancelRequestComponent,
    setOpenValidateModal,
  };
};

class OnlineVisiting extends System {
  constructor() {
    super(SystemHub.PatientInf, [{}], {
      screenCode: SystemScreenCode.PatientInfo,
    });
  }
}

const onlineVisiting = new OnlineVisiting();
