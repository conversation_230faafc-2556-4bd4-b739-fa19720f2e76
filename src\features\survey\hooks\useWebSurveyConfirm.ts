import { useState } from "react";

import { uploadFile } from "@/apis/rest/file";
import { DenkaruCode } from "@/constants/denkaru-codes";
import {
  useGetSurveyAnswerNoPatientUploadFileUrLsLazyQuery,
  useCreateSurveyAnswerNoPatientMutation,
} from "@/apis/gql/operations/__generated__/web-servey";

import { isCustomFileAnswer } from "../utils/customer-survey";

import type { ApolloError } from "@apollo/client";
import type { RcFile } from "antd/lib/upload";
import type { WebSurveyPatientFormType } from "../ui/WebSurveyPatientForm";
import type {
  CustomAnswerType,
  CustomSurveyAnswer,
} from "../utils/customer-survey";
import type { SurveyAnswerNoPatientUploadFileUrl } from "@/apis/gql/generated/types";

/**
 * Web問診票：問診内容の送信
 *
 * 問診内容の送信のソースコードは、クリニックマップの次のコードをベースに作成しています。
 * @see {@link https://github.com/bizleap-healthcare/clinic-reservation/blob/main/user-client/src/hooks/customer-survey/useCustomerSurveyByReservationTemplate.ts}
 */

export const useWebSurveyConfirm = (
  secret: string,
  patientData: WebSurveyPatientFormType,
  clinicSurveyData: CustomAnswerType[],
  onComplete: () => void,
) => {
  const [getUploadFileUrls] =
    useGetSurveyAnswerNoPatientUploadFileUrLsLazyQuery({
      fetchPolicy: "network-only",
      onError: (error) => {
        setErrorMessage(formatErrorMessage(error));
      },
    });
  const [createSurvey] = useCreateSurveyAnswerNoPatientMutation();
  const [errorMessage, setErrorMessage] = useState<string>("");

  const handleSubmit = async () => {
    // ファイル回答を抽出
    const fileQuestions = clinicSurveyData
      .filter(isCustomFileAnswer)
      .filter((item) => !!item.answerDisplayValue);
    let uploadedFiles: SurveyAnswerNoPatientUploadFileUrl[] = [];

    // ファイル回答がある場合
    if (fileQuestions.length > 0) {
      // ファイルアップロード用のPreSignedURLを取得
      const { data } = await getUploadFileUrls({
        variables: {
          input: {
            secret,
            fileNames: fileQuestions.map((item) => item.answerValue.name),
          },
        },
      });
      if (!data) {
        // useLazyQueryのonErrorで、setErrorMessage()しているため、ここでは何もしない
        return;
      }
      uploadedFiles = data.getSurveyAnswerNoPatientUploadFileURLs;
      if (fileQuestions.length !== uploadedFiles.length) {
        // requestとresponseでファイル数が異なる場合（あり得ないが）
        setErrorMessage("エラーが発生しました");
        return;
      }

      // ファイルアップロード
      for (let i = 0; i < fileQuestions.length; i++) {
        const fileQuestion = fileQuestions[i]!;
        const uploadedFile = uploadedFiles[i]!;

        const result = await uploadFile(
          uploadedFile.uploadUrl,
          fileQuestion.answerValue as RcFile,
          {
            headers: {
              "Content-Type": fileQuestion.answerValue.type,
              Authorization: undefined,
            },
          },
        );
        if (result instanceof Error) {
          // S3アップロードに失敗した場合
          setErrorMessage("エラーが発生しました");
          return;
        }
      }
    }

    const transformedSurveyAnswers: CustomSurveyAnswer[] = [];
    clinicSurveyData.forEach((question) => {
      if (question.questionType === "file") {
        const uploadedFile = uploadedFiles[fileQuestions.indexOf(question)];
        if (uploadedFile) {
          transformedSurveyAnswers.push({
            ...question,
            answerDisplayValue: uploadedFile.fileName,
            answerValue: {
              fileName: uploadedFile.fileName,
              fileType: question.answerValue.type,
              s3Key: uploadedFile.s3Key,
            },
          });
        } else {
          transformedSurveyAnswers.push({
            ...question,
            answerDisplayValue: "",
            answerValue: {},
          });
        }
      } else {
        transformedSurveyAnswers.push(question);
      }
    });

    // 回答を登録
    createSurvey({
      variables: {
        input: {
          secret,
          name: patientData.name,
          kanaName: patientData.kanaName,
          birthday: Number(patientData.birthDate),
          surveyAnswer: JSON.stringify(transformedSurveyAnswers),
          files: uploadedFiles.map((uploadedFile) => ({
            fileName: uploadedFile.fileName,
            s3Key: uploadedFile.s3Key,
          })),
        },
      },
      onCompleted: () => {
        onComplete();
      },
      onError: (error) => {
        setErrorMessage(formatErrorMessage(error));
      },
    });
  };

  return {
    handleSubmit,
    errorMessage,
    isOpenErrorModal: errorMessage !== "",
    handleErrorModalClose: () => setErrorMessage(""),
  };
};

const formatErrorMessage = (error: ApolloError) => {
  switch (error.graphQLErrors[0]?.extensions?.code) {
    case DenkaruCode.INVALID_PARAMETER:
      return "Web問診票のURLが無効です。";
    case DenkaruCode.INTERNAL_SERVER_ERROR:
      return "システムエラーが発生しました。";
    default:
      return "エラーが発生しました";
  }
};
